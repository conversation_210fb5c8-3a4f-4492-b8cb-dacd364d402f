import { Banner, Box, DataTable, InlineCode, Text } from "@shopify/polaris";
import React from "react";
import parse, { domToReact } from "html-react-parser";

const getBannerTone = (className) => {
  if (className.includes("callout-yellow")) {
    return "warning";
  }

  if (className.includes("callout-green")) {
    return "success";
  }

  if (className.includes("callout-red")) {
    return "critical";
  }

  return "info";
};

class HelpscoutParser {
  static parseAndReplace(htmlString) {
    const options = {
      replace(node) {
        if (node.tagName === "h1") {
          return (
            <Text variant="headingLg" as="h1">
              {domToReact(node.children, options)}
            </Text>
          );
        }

        if (node.tagName === "h2" || node.tagName === "h3") {
          return (
            <Box paddingBlockStart={200} paddingBlockEnd={200}>
              <Text variant="headingMd" as="h3">
                {domToReact(node.children, options)}
              </Text>
            </Box>
          );
        }

        if (node.tagName === "pre") {
          return <InlineCode>{domToReact(node.children, options)}</InlineCode>;
        }

        if (node.name === "table") {
          try {
            const trElements = node.children
              .filter(
                (child) => child.name === "thead" || child.name === "tbody"
              )
              .map((child) =>
                child.children.filter((child) => child.name === "tr")
              )
              .flat();

            const columns = trElements[0].children
              .filter((child) => child.name === "td" || child.name === "th")
              .map((th) => domToReact(th.children));
            const rows = trElements
              .slice(1)
              .map((tr) =>
                tr.children
                  .filter((child) => child.name === "td" || child.name === "th")
                  .map((td) => domToReact(td.children))
              );

            return (
              <DataTable
                columnContentTypes={Array(columns.length).fill("text")}
                headings={columns}
                hasZebraStripingOnData={true}
                rows={rows}
              ></DataTable>
            );
          } catch (err) {
            return domToReact(node);
          }
        }

        if (node.tagName === "a") {
          node.attribs.target = "_blank";
        }

        if (node.tagName === "img") {
          node.attribs.style = {
            width: '75%'
          };

          return (
            <p style={{ textAlign: "center", width: '100%', margin: '10px 0' }}>
              <img {...node.attribs} />
            </p>
          )
        }

        if (node.tagName === "iframe" && node.attribs.src.includes("youtube.com")) {
          return (
            <Box paddingBlock={200} style={{ margin: "0 auto", width: "75%" }}>
              <div style={{ position: 'relative', paddingBottom: '56.25%', height: 0, overflow: 'hidden' }}>
                <iframe
                  src={node.attribs.src}
                  style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}
                  frameBorder="0"
                  allowFullScreen
                ></iframe>
              </div>
            </Box>
          );
        }

        if (node.attribs?.class && node.attribs?.class.includes("callout")) {
          return (
            <Box paddingBlockStart={200} paddingBlockEnd={200}>
              <Banner tone={getBannerTone(node.attribs?.class)}>
                {domToReact(node.children, options)}
              </Banner>
            </Box>
          );
        }

        return domToReact(node);
      },
    };

    return parse(htmlString, options);
  }
}

export default HelpscoutParser;
