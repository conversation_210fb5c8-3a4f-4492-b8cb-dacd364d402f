import { defineConfig, searchForWorkspaceRoot } from 'vite';
import { execSync } from 'child_process';
import ViteRails from 'vite-plugin-rails';
import react from "@vitejs/plugin-react";

const gemPath = execSync(`bundle show cross_promotion_app`, { encoding: 'utf-8' }).trim();

export default defineConfig({
  server: {
    host: 'localhost',
    hmr: {
      host: 'localhost',
      port: 3036,
      protocol: 'ws'
    },
    fs: {
      allow: [
        searchForWorkspaceRoot(process.cwd()),
        gemPath
      ]
    }
  },
  plugins: [
    ViteRails({
      envVars: {
        'CABLE_ENDPOINT': null,
        'SHOPIFY_CLIENT_API_KEY': null,
        'HONEYBADGER_FRONTEND_API_KEY': null,
        'LEXOFFICE_SITE': null,
        'HELPSCOUT_CHANGELOG_ARTICLE_ID': null,
      }
    }),
    react(),
  ],
  resolve: {
    alias: {
      'cross-promotion-app': gemPath,
    }
  }
})