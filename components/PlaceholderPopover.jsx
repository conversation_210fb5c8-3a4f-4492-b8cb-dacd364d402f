import React from 'react';
import { Popover, ActionList } from '@shopify/polaris';
import {useTranslation} from "react-i18next";

export default function PlaceholderPopover({active, setActive, onSelectPlaceholder}) {
  const { t } = useTranslation();
  const handleClose = () => {setActive(false)};

  const placeholders = [
    {label: t('settings.placeholders.order_name'), helpText: t('settings.placeholders.order_name_help'), value: '{{name}}'},
    {label: t('settings.placeholders.first_name'), helpText: t('settings.placeholders.first_name_help'), value: '{{customer.first_name}}'},
    {label: t('settings.placeholders.last_name'), helpText: t('settings.placeholders.last_name_help'), value: '{{customer.last_name}}'}
  ];

  const actions = placeholders.map((placeholder) => {
    return {
      content: placeholder.label,
      helpText: placeholder.helpText,
      onAction: () => {
        onSelectPlaceholder(placeholder.value);
        handleClose();
      }
    }
  });

  return (
    <Popover active={active}
             onClose={handleClose}
             activator={<span></span>}
             preferredAlignment="left"
             preferredPosition="below"
    >
      <ActionList items={actions}/>
    </Popover>
  )
}
