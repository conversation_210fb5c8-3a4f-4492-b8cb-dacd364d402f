import {React, useState, useCallback, useEffect} from "react";
import {<PERSON><PERSON>, Popover, ActionList, InlineStack} from '@shopify/polaris';
import {useTranslation} from "react-i18next";
export default function BeaconMessages({title, beacons}) {
    const [popoverActive, setPopoverActive] = useState(false);
    const { t } = useTranslation();

    const togglePopoverActive = useCallback(
        () => setPopoverActive((popoverActive) => !popoverActive),
        [],
    );

    const activator = (
        <Button  onClick={togglePopoverActive} textAlign="center" variant="plain">
            {t('beacons.help')}
        </Button>
    );

    const showBeacon = (id) => {
        return () => {
            window.Beacon('show-message', id, { force: true, delay: 1 });
        }
    }


    return (
        <InlineStack wrap={false} align="space-between">
            {title}
            {beacons && beacons.length > 0 && <Popover
                active={popoverActive}
                activator={activator}
                autofocusTarget="first-node"
                onClose={togglePopoverActive}
            >
                <ActionList
                    actionRole="menuitem"
                    items={beacons.map((beacon) => {
                        return {content: beacon.name, onAction: showBeacon(beacon.help_scout_id)}
                    })}
                />
            </Popover>}
        </InlineStack>
    );
}