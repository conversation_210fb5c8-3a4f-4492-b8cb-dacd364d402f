import React from "react";
import { <PERSON>, Badge, Button, Box, InlineGrid, InlineStack,
    Image, Layout, Text, BlockStack } from "@shopify/polaris";
import {checkMark} from "~/assets/index.js";
import {useTranslation} from "react-i18next";

export default function PlanCard({plan, billingStatus, onPlanActivation, activationDisabled = false}) {
  const { t } = useTranslation();

  const plan_active = () => {
    if (plan.type === 'recurring') {
      return billingStatus?.plan_active
    } else {
      return billingStatus?.import_unlocked
    }
  }

  const removeTrailingZeros = (numberStr) => {
    // Remove trailing zeros after a decimal point, if any
    numberStr = numberStr.replace(/(\.\d*?[1-9])0+$/, '$1');
    // Remove the decimal point if the number is a whole number
    numberStr = numberStr.replace(/\.0+$/, '');
    return numberStr;
  }

  const planFeatures = () => {
    return plan.features?.split(".").map((feature, index) => (
      <InlineStack gap="100" align="start" key={`plan_features_${index}`} wrap={false}>
        <Image alt={"checkmark"} source={checkMark} height={"20px"}/>
        <Text as="span" breakWord={true} alignment={"start"}>
          {feature}
        </Text>
      </InlineStack>
    ));
  }
  return (
    <Layout.Section>
      <Card>
        <InlineGrid columns={{sm: 1, md: 3}} alignItems="center" gap="200">
          <div>
            <Text variant="headingXl" as="h4">
              {plan.name}
            </Text>
            <Text tone="subdued" as="span">
              {plan.type === 'recurring' ? t('billing.plan.recurring.pricing_hint') : t('billing.plan.import.pricing_hint')}
            </Text>
          </div>
          <div style={{ flexGrow: 1 }}>
            <BlockStack gap="100">
              <Text fontWeight="bold">
                {t('billing.plan.features')}
              </Text>
              {planFeatures()}
            </BlockStack>
          </div>
          <div>
            <BlockStack gap="100" inlineAlign="center" align={"space-around"}>
              <Text variant="heading3xl" as="span">
                {removeTrailingZeros(plan.price)} $
              </Text>
              <Text as={"span"} fontWeight="semibold">
                {plan.type === 'recurring' ? t('billing.plan.recurring.frequency') : t('billing.plan.import.frequency')}
              </Text>
              <Box paddingBlockStart="100">
                {(!plan_active()
                  ? <Button

                  onClick={() => onPlanActivation(plan)}
                  disabled={activationDisabled === true}
                  variant="primary">
                      {t('billing.plan.activate_plan')}
                    </Button>
                  : <Badge tone="info">{t('billing.plan.active')}</Badge>)}
              </Box>
            </BlockStack>
          </div>
        </InlineGrid>
      </Card>
    </Layout.Section>
  );
}