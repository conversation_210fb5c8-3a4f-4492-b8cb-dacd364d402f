import React, {useCallback, useEffect, useState} from "react";
import {
  Page, Layout, Card, BlockStack, Grid, Divider, Text, TextField, Button, Icon,
  MediaCard, VideoThumbnail, SkeletonBodyText, Banner, Listbox, InlineGrid, Box, EmptyState
} from "@shopify/polaris";
import {SearchMinor, NoteMinor} from '@shopify/polaris-icons';
import HelpNavigation from "@/shared/components/HelpAndSupport/HelpNavigation";
import ArticlesDetailsModal from "@/shared/components/HelpAndSupport/ArticlesDetailsModal";
import {useAuthenticatedFetch} from "@/hooks";
import {Outlet, useNavigate, useParams} from "react-router-dom";
import { useTranslation } from 'react-i18next';
import debounce from 'lodash.debounce';
import IntroVideoModal from "@/shared/components/HelpAndSupport/IntroVideoModal";
import { default as emptyStateImage } from "../assets/empty-state.png";
import { formatTitle } from "./HelpAndSupport/HelpArticle";

export default function HelpAndSupport({videoThumbnail, videoLength, videoId}) {
  const authenticatedFetch = useAuthenticatedFetch();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [articles, setArticles] = useState({});
  const [searchValue, setSearchValue] = useState("");
  const [noContent, setNoContent] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [videoModalActive, setVideoModalActive] = useState(false);

  const { article } = useParams();

  const fetchArticles = useCallback(debounce(async (searchParam) => {
    setIsLoading(true);
    setNoContent(false);

    const endpoint = searchParam
      ? `/api/help/search/${searchParam}`
      : "/api/help/default_articles";

    const response = await authenticatedFetch(endpoint, {
      method: 'GET',
      headers: {'Content-Type': 'application/json'}
    });

    switch(response.status) {
      case 200:
        const data = await response.json();
        setArticles(data.articles);
        break;
      case 204:
        setNoContent(true);
        break;
      default:
        console.error(`Unhandled response status: ${response.status}`);
    }
    
    setIsLoading(false);
  }, 500), []);
  const handleClearSearchValue = useCallback(() => setSearchValue(""), []);

  useEffect(() => {
    fetchArticles(searchValue);
  }, [searchValue, fetchArticles]);

  // Show helpscout article in modal
  const [helpTextModalActive, setHelpTextModalActive] = useState(false);
  const [articleId, setArticleId] = useState("");
  const showModal = (id) => {
    setHelpTextModalActive(true);
    setArticleId(id);
  }

  const reloadPage = useCallback(() => {
    setSearchValue("")
    navigate("/Help")
  });

  const onVideoThumbClick = () => {
    setVideoModalActive(true);
  }
  return (
    <Page title={t("help.page_title")} subtitle={t("general.app_name")} backAction={{content: 'Back', onAction: () => navigate(-1)}}>
      {helpTextModalActive &&
        <ArticlesDetailsModal articleId={articleId} active={helpTextModalActive} setActive={setHelpTextModalActive} />
      }
      {videoModalActive &&
        <IntroVideoModal active={videoModalActive} setActive={setVideoModalActive} videoId={videoId}/>
      }
      <Layout>
        <Layout.Section>
          <Layout>
            <Layout.Section>
              <Card>
                <BlockStack gap="500">
                  <Text variant="headingMd" as="h2"> {t("help.intro.heading")} </Text>
                  <Text as="p"> {t("help.intro.text")} </Text>
                  <Divider />
                  <InlineGrid gap={"500"} columns={['twoThirds', 'oneThird']}>

                      <TextField value={searchValue}
                                 prefix={<Icon source={SearchMinor}/>}
                                 placeholder={t('help.search.placeholder')}
                                 onChange={setSearchValue}
                                 clearButton
                                 onClearButtonClick={handleClearSearchValue}
                                 autoComplete="off"
                                 autoFocus={false}
                      >
                      </TextField>

                    <Button

                      onClick={() => { if (window.Beacon) { window.Beacon('open'); window.Beacon('navigate', '/ask/message') }}}
                      variant="primary">
                      {t('help.search.beacon_button')}
                    </Button>

                  </InlineGrid>
                </BlockStack>
              </Card>
            </Layout.Section>

            <Layout.Section>
              <Card>
                {isLoading ? (<SkeletonBodyText />) : (
                  noContent ? (<EmptyState
                  heading={t('help.errors.no_result')}
                  image={emptyStateImage}
                />) : (
                    <Listbox>
                      {articles && articles.map((article) => (
                        <Listbox.Action key={article.id} value={article.id}>
                          <Box paddingBlockStart={"100"} paddingBlockEnd={"100"}>
                            <Button fullWidth
                                    textAlign="left"
                                    variant={"monochromePlain"}
                                    removeUnderline={true}
                                    icon={NoteMinor}
                                    onClick={() => navigate("/Help/"+article.id)}
                            >
                              {formatTitle(article.title)}
                            </Button>
                          </Box>
                        </Listbox.Action>
                      ))}
                    </Listbox>
                  )
                )}
              </Card>
            </Layout.Section>

          </Layout>
        </Layout.Section>

        <Layout.Section variant="oneThird">
          <Layout>
            {videoId && videoLength && videoThumbnail && (
              <Layout.Section>
                <MediaCard portrait title={t('help.main_video_title')}>
                  <VideoThumbnail
                    videoLength={videoLength}
                    thumbnailUrl={videoThumbnail}
                    onClick={onVideoThumbClick}
                  />
                </MediaCard>
              </Layout.Section>
            )}

            <Layout.Section>
              <Card>
                <HelpNavigation showModal={showModal} reloadPage={reloadPage}/>
              </Card>
            </Layout.Section>
          </Layout>
        </Layout.Section>
        <Layout.Section>
          <Outlet />
        </Layout.Section>
      </Layout>
    </Page>
  );
}
