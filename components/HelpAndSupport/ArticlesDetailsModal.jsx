import React, { useCallback, useEffect, useState } from "react";
import {Modal, SkeletonBodyText, Banner} from "@shopify/polaris";
import { useAuthenticatedFetch } from '~/hooks';
import { useTranslation } from "react-i18next";

export default function ArticlesDetailsModal({ articleId, active, setActive }) {
  const authenticatedFetch = useAuthenticatedFetch();
  const { t } = useTranslation();

  const handleChange = useCallback(() => setActive(!active), [active]);
  const [articleTitle, setArticleTitle] = useState("");
  const [articleBody, setArticleBody] = useState("");
  const [errors, setErrors] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function fetchData() {
      const response = await authenticatedFetch(`/api/help/article/${articleId}`, {
        method: 'GET', headers: {'Content-Type': 'application/json'}
      });
      const data = await response.json();

      if (response.status === 404) {
        setErrors(true);
      } else {
        setArticleTitle(data.article.title);
        setArticleBody(data.article.body);
      }
      setIsLoading(false);
    }
    fetchData();
  }, []);

  return (
    <Modal open={active} onClose={handleChange}
           title={isLoading ? <SkeletonBodyText lines={1}/> : !errors ? articleTitle : t('help.errors.not_found.title')}>
      <Modal.Section>
        {isLoading ? (<SkeletonBodyText />) : (
          !errors ? (
            <div dangerouslySetInnerHTML={{ __html: articleBody }} />
          ) : (
            <Banner tone="critical"><p>{t('help.errors.not_found.body')}</p></Banner>
          )
        )}
      </Modal.Section>
    </Modal>
  );
}
