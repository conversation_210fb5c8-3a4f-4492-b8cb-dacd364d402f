import React from "react";
import {Icon} from "@shopify/polaris";
import {HomeMinor, InviteMinor, MergeMinor, ThumbsUpMinor, AppsMinor, ExternalSmallMinor} from '@shopify/polaris-icons';
import { useTranslation } from 'react-i18next';
import { useNavigate } from "@shopify/app-bridge-react";

export default function HelpNavigation({showModal, reloadPage}) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const changelogArticleId = import.meta.env.HELPSCOUT_CHANGELOG_ARTICLE_ID;

  return (
    <ul className="Polaris-Navigation__Section">
      <li className="Polaris-Navigation__ListItem">
        <div className="Polaris-Navigation__ItemWrapper">
          <div className="Polaris-Navigation__ItemInnerWrapper Polaris-Navigation__ItemInnerWrapper--selected">
            <button data-polaris-unstyled="true" tabIndex="0" onClick={reloadPage}
                    className="Polaris-Navigation__Item Polaris-Navigation__Item--selected Polaris-Navigation--subNavigationActive">
              <div className="Polaris-Navigation__Icon">
                <Icon source={HomeMinor} tone="subdued"/>
              </div>
              <span className="Polaris-Navigation__Text">{t('help.navigation.overview')}</span>
            </button>
          </div>
        </div>
      </li>
      <li className="Polaris-Navigation__ListItem">
        <div className="Polaris-Navigation__ItemWrapper">
          <div className="Polaris-Navigation__ItemInnerWrapper">
            <button data-polaris-unstyled="true" className="Polaris-Navigation__Item" tabIndex="0"
                    onClick={() => { if (window.Beacon) { window.Beacon('open'); window.Beacon('navigate', '/ask/message') }}}>
              <div className="Polaris-Navigation__Icon">
                <Icon source={InviteMinor} tone="subdued"/>
              </div>
              <span className="Polaris-Navigation__Text">{t('help.navigation.contact')}</span>
            </button>
          </div>
        </div>
      </li>
      <li className="Polaris-Navigation__ListItem">
        <div className="Polaris-Navigation__ItemWrapper">
          <div className="Polaris-Navigation__ItemInnerWrapper">
            <button data-polaris-unstyled="true" className="Polaris-Navigation__Item" tabIndex="0"
                    onClick={() => navigate('/Help/'+changelogArticleId)}>
              <div className="Polaris-Navigation__Icon">
                <Icon source={MergeMinor} tone="subdued"/>
              </div>
              <span className="Polaris-Navigation__Text">{t('help.navigation.changelog')}</span>
            </button>
          </div>
        </div>
      </li>
      <li className="Polaris-Navigation__ListItem">
        <div className="Polaris-Navigation__ItemWrapper">
          <div className="Polaris-Navigation__ItemInnerWrapper">
            <button data-polaris-unstyled="true" className="Polaris-Navigation__Item" tabIndex="0"
                    onClick={() => { if (window.Beacon) { window.Beacon('open'); window.Beacon('navigate', '/ask/message') }}}>
              <div className="Polaris-Navigation__Icon">
                <Icon source={ThumbsUpMinor} tone="subdued"/>
              </div>
              <span className="Polaris-Navigation__Text">{t('help.navigation.feature_requests')}</span>
            </button>
          </div>
        </div>
      </li>
      <li className="Polaris-Navigation__ListItem">
        <div className="Polaris-Navigation__ItemWrapper">
          <div className="Polaris-Navigation__ItemInnerWrapper">
            <a data-polaris-unstyled="true" className="Polaris-Navigation__Item" tabIndex="0"
               href="https://www.eshop-guide.de/pages/eshop-guide-shopify-apps" target="_blank">
              <div className="Polaris-Navigation__Icon">
                <Icon source={AppsMinor} tone="subdued"/>
              </div>
              <span className="Polaris-Navigation__Text">{t('help.navigation.more_apps')}</span>
              <div className="Polaris-Navigation__Badge">
                <Icon source={ExternalSmallMinor} tone="subdued"/>
              </div>
            </a>
          </div>
        </div>
      </li>
      <li className="Polaris-Navigation__ListItem">
        <div className="Polaris-Navigation__ItemWrapper">
          <div className="Polaris-Navigation__ItemInnerWrapper">
            <a data-polaris-unstyled="true" className="Polaris-Navigation__Item" tabIndex="0"
               href="https://www.youtube.com/@eshopguideapps" target="_blank">
              <div className="Polaris-Navigation__Icon">
                <span className="Polaris-Icon">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512">{/*! Font Awesome Pro 6.4.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2023 Fonticons, Inc. */}
                    <path d="M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z"/>
                  </svg>
                </span>
              </div>
              <span className="Polaris-Navigation__Text">{t('help.navigation.youtube')}</span>
              <div className="Polaris-Navigation__Badge">
                <Icon source={ExternalSmallMinor} tone="subdued"/>
              </div>
            </a>
          </div>
        </div>
      </li>
    </ul>
  )
}
