import React, { useCallback, useState } from "react";
import { Banner, Button, Image, Text, InlineGrid, BlockStack, Collapsible, TextField } from "@shopify/polaris";
import { plansAndCoupons } from "~/assets";
import { useTranslation } from "react-i18next";
import {useAppQuery} from "@/hooks";
import BeaconMessages from "@/shared/components/BeaconMessages";

export default function CouponsCard({ shopStatus, couponCode, onCouponCodeChange, onCouponCodeSave }) {
  const { t } = useTranslation();
  const { billing } = shopStatus;

  const [couponCodeArea, setCouponCodeArea] = useState(false);
  const handleCouponCodeToggle = useCallback(() => setCouponCodeArea((couponCodeArea) => !couponCodeArea), []);
  const {
    data: beaconMessagesData
  } = useAppQuery({ url: '/api/beacon_messages?domain=plans' });

  return <>
    <InlineGrid gap="200" columns={{md:['twoThirds', 'oneThird']}} alignItems="center">
      <div>
        <BlockStack gap="400">
          <Text as="h1" variant="heading3xl">
            {beaconMessagesData ?
              <BeaconMessages beacons={beaconMessagesData.beacon_messages} title={t('billing.overview.title')}/> :
              t('billing.overview.title')
            }
          </Text>
          <p>{t('billing.overview.description')}</p>
          {shopStatus && !billing?.plan_active && (
            <Banner title={t('billing.overview.status.inactive')} tone="warning">
              <p>{t('billing.overview.status.trial_period_hint')}</p>
            </Banner>
          )}
          {shopStatus && (
            <div>
              <Button onClick={handleCouponCodeToggle} ariaExpanded={couponCodeArea} ariaControls="basic-collapsible">
                {t('billing.coupons.toggle_button')}
              </Button>

              <div style={{display: 'inherit', background: 'transparent', height: "10px", width: undefined}}/>

              <Collapsible id="basic-collapsible"
                           open={couponCodeArea}
                           transition={{duration: '500ms', timingFunction: 'ease-in-out'}}
              >
                <TextField id={'coupon_code_input'}
                           placeholder={t('billing.coupons.placeholder')}
                           value={couponCode}
                           onChange={onCouponCodeChange}
                           autoComplete="off"
                           monospaced={true}
                           connectedRight={
                             <Button onClick={onCouponCodeSave}>
                               {t('billing.coupons.activate')}
                             </Button>
                           }
                />
              </Collapsible>
            </div>
          )}
        </BlockStack>
      </div>
      <div className="mobile--hidden" style={{ margin: "0 0 0 20px" }}>
        <Image alt={"plansImage"} source={plansAndCoupons} width={256} />
      </div>
    </InlineGrid>
  </>;
}
