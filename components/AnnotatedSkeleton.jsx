import React from 'react'
import {Card, Layout, SkeletonBodyText, SkeletonDisplayText} from "@shopify/polaris";
export default function AnnotatedSkeleton({cardCount = 2}) {
  function createCard(key) {
    return (
      <React.Fragment key={key}>
        <Card>
          <SkeletonBodyText lines={2} />
          <br></br>
          <SkeletonDisplayText size="large" />
        </Card>
        <Layout.Section />
      </React.Fragment>
    );
  }

  const cards = [];
  for (let i = 0; i < cardCount; i++) {
    cards.push(createCard(i));
  }

  return (
    <Layout>
      <Layout.AnnotatedSection title={<SkeletonDisplayText size="large" />} description={<SkeletonBodyText lines={4} />}>
        {cards}
      </Layout.AnnotatedSection>
    </Layout>
  )
}