import React from 'react';
import {<PERSON><PERSON><PERSON>elp, Link} from '@shopify/polaris';
import { useTranslation } from "react-i18next";

export default function BeaconFooter() {
  const { t } = useTranslation();

  return (
    <FooterHelp>
      {t('beacon_footer.text')}
      <Link onClick={() => { if (window.Beacon) { window.Beacon('open') }}}>
        {t('beacon_footer.help')}
      </Link>.
    </FooterHelp>
  );
}
