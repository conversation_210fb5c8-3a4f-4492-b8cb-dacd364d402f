import React from 'react';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>} from '@shopify/polaris';
import { useTranslation } from "react-i18next";
import {useNavigate} from "react-router-dom";

export default function HelpFooter() {
  const { t } = useTranslation();
  const navigate = useNavigate();

  return (
    <FooterHelp>
      {t('beacon_footer.text')}
      <Link onClick={() => navigate("/Help")}>
        {t('beacon_footer.help')}
      </Link>.
    </FooterHelp>
  );
}
