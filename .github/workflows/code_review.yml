name: Code review

on:
  workflow_call:
    inputs:
      backend_changed:
        required: true
        type: string
      frontend_changed:
        required: true
        type: string

jobs:
  autoresolve:
    name: Resolve outdated comments
    runs-on: ubuntu-latest
    steps:
      - name: Auto resolve outdated comments
        uses: Ardiannn08/resolve-outdated-comment@v1.1
        with:
          token: ${{ github.token }}
          filter-user: "github-actions"
          mode: "delete"

  backend:
    name: Backend linters
    if: ${{ inputs.backend_changed == 'true' }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true

      - name: Run Pronto backend linters
        run: bundle exec pronto run -r brakeman rails_best_practices rails_schema rubocop -f github_status github_pr_review -c origin/${{ github.base_ref }} --exit-code
        env:
          PRONTO_PULL_REQUEST_ID: ${{ github.event.pull_request.number }}
          PRONTO_GITHUB_ACCESS_TOKEN: "${{ github.token }}"

  frontend:
    name: Frontend linters
    if: ${{ inputs.frontend_changed == 'true' }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true

      - name: Set up Node
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          cache: 'yarn'

      - name: Install frontend dependencies
        run: |
          yarn install
          yarn global add eslint@8.56.0

      - name: Run Pronto frontend linters
        run: bundle exec pronto run -r eslint_npm -f github_status github_pr_review -c origin/${{ github.base_ref }} --exit-code
        env:
          PRONTO_PULL_REQUEST_ID: ${{ github.event.pull_request.number }}
          PRONTO_GITHUB_ACCESS_TOKEN: "${{ github.token }}"
