name: Track Deployment

on: [workflow_call]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Track Deployment
        uses: honeybadger-io/github-notify-deploy-action@v1
        with:
          api_key: ${{ secrets.HONEYBADGER_API_KEY }}
          environment: ${{ github.ref == 'refs/heads/main' && 'production' || github.ref == 'refs/heads/staging' && 'staging' || 'development' }}
