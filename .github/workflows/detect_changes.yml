name: Detect Changed Files

on:
  workflow_call:
    outputs:
      backend:
        description: "Whether backend files were changed"
        value: ${{ jobs.detect.outputs.backend }}
      frontend:
        description: "Whether frontend files were changed"
        value: ${{ jobs.detect.outputs.frontend }}

jobs:
  detect:
    name: Detect changed files
    runs-on: ubuntu-latest
    outputs:
      backend: ${{ steps.filter.outputs.backend }}
      frontend: ${{ steps.filter.outputs.frontend }}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            backend:
              - "app/**"
              - "spec/**"
              - "config/**"
              - "db/**"
              - "lib/**"
              - "Gemfile"
              - "Gemfile.lock"
              - ".ruby-version"
            frontend:
              - "frontend/**"
              - "package.json"
              - "yarn.lock"
              - "vite.config.js"
