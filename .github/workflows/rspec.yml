name: RSpec

on: [workflow_call]

jobs:
  rspec:
    name: Run RSpec tests
    runs-on: ubuntu-latest
    env:
      APP_HOME: https://test-host/
      APP_NAME: lexoffice-integration-test
      CENTRAL_EVENT_LOGGER_API_BASE_URL: https://testurl.com
      CENTRAL_EVENT_LOGGER_API_KEY: test_key
      CENTRAL_EVENT_LOGGER_API_SECRET: test_secret
      HOST_NAME: ${{ secrets.HOST_NAME }}
      LEXOFFICE_SITE: https://lexoffice-sandbox.grld.eu
      LEXOFFICE_API: https://api-sandbox.grld.eu
      MY_REDIS_URL: ${{ secrets.REDIS_URL }}
      RAILS_ENV: test
      REDIS_PROVIDER: MY_REDIS_URL
      SHOPIFY_CLIENT_API_KEY: ${{ secrets.SHOPIFY_CLIENT_API_KEY }}
      SHOPIFY_CLIENT_API_SECRET: ${{ secrets.SHOPIFY_CLIENT_API_SECRET }}
    services:
      postgres:
        image: postgres:16
        ports:
          - 5432:5432
        env:
          POSTGRES_PASSWORD: ${{ secrets.POSTGRES_PASSWORD }}
          POSTGRES_USER: ${{ secrets.POSTGRES_USER }}
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true

      - name: Set up Database
        run: |
          cp config/database.yml.github-actions config/database.yml
          bundle exec rake db:create db:schema:load
          bundle exec rake db:migrate RAILS_ENV=test
          bundle exec rake db:fixtures:load

      - name: Run Specs
        run: bundle exec rspec
