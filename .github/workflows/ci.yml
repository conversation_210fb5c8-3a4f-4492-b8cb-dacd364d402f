name: "CI/CD Workflow"

on:
  push:
    branches:
      - main
      - staging
  pull_request:
  workflow_dispatch:
jobs:
  detect_changes:
    uses: ./.github/workflows/detect_changes.yml

  rspec:
    needs: detect_changes
    if: needs.detect_changes.outputs.backend == 'true'
    uses: ./.github/workflows/rspec.yml
    secrets: inherit

  code_review:
    needs: detect_changes
    if: github.event_name == 'pull_request' &&
      (needs.detect_changes.outputs.backend == 'true' || needs.detect_changes.outputs.frontend == 'true')
    uses: ./.github/workflows/code_review.yml
    with:
      backend_changed: ${{ needs.detect_changes.outputs.backend }}
      frontend_changed: ${{ needs.detect_changes.outputs.frontend }}
    secrets: inherit

  track_deployment:
    needs: [rspec, code_review]
    if: always() &&
      github.event_name == 'push' &&
      (github.ref == 'refs/heads/staging' || github.ref == 'refs/heads/main') &&
      (needs.code_review.result == 'success' || needs.code_review.result == 'skipped') &&
      (needs.rspec.result == 'success' || needs.rspec.result == 'skipped')
    uses: ./.github/workflows/track_deployment.yml
    secrets: inherit
