# frozen_string_literal: true

source "https://rubygems.org"

ruby "3.2.7"

gem "activerecord-session_store"
gem "puma", "~> 6.0"

# TODO: 7.1.5.1 has issue with content security policy
gem "rails", "= 7.1.5"
gem "rails_autoscale_agent"
gem "rails-i18n", "~> 7.0", ">= 7.0.5"

gem "migration_data"
gem "pg"
gem "scenic"
gem "seed_dump"

gem "connection_pool", "~> 2.5"
gem "redis", "~> 4.7.1"
gem "redis-client", "~> 0.22.0"
gem "redlock", "~> 2.0"
gem "rest-client"
gem "sidekiq", "~> 7.3.9"

gem "audited", "~> 5.0" # Audit log for tracking ShopSettings Changes

gem "devise" # Support login via google oAUth
gem "oauth2", "1.4.7"
gem "omniauth"
gem "omniauth-google-oauth2"
gem "omniauth-lexoffice", "0.1.5"
gem "omniauth-rails_csrf_protection"
gem "rack-protection", "~> 2"

gem "google-cloud-storage"
gem "mini_magick"

# Frontend
gem "bootstrap-datepicker-rails"
gem "bootstrap-editable-rails"
gem "bootstrap_form", "2.7.0"
gem "bootstrap-material-design"
gem "bootstrap-sass"
gem "coffee-rails"
gem "font-awesome-rails"
gem "jbuilder", "~> 2.0"
gem "jquery-rails"
gem "sass-rails", "~> 5.0"
gem "table-for"
gem "turbolinks"
gem "twitter-bootstrap-rails"
gem "vite_rails", "3.0.14"
gem "vite_ruby", "3.3.4"
gem "will_paginate", "~> 3.3", ">= 3.3.1"

# live updates to the frontend
gem "actioncable"

# Shopify
gem "liquid"
gem "liquid-validator"
gem "shopify_api", "~> 14.6.0"
gem "shopify_api_retry"
gem "shopify_app", "~> 22.4.0"
gem "shopify_graphql", "~> 2.0"

gem "countries"
gem "gibbon"
gem "interactor", "~> 3.0"
gem "MailchimpTransactional", "~> 1.0", ">= 1.0.47"
gem "psych", "< 4" # YAML parser and emitter
gem "sdoc", "~> 2.4", group: :doc
gem "slack-notifier"
gem "suture"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "parallel"
gem "tzinfo-data", platforms: %i[windows jruby]
gem "zeitwerk", "~> 2.6" # efficient and thread-safe code loader for Ruby

gem "honeybadger" # Error_logging
gem "lograge" # better logging
gem "retriable" # exception handling

group :development, :test do
  # Access an IRB console on exception pages or by using <%= console %> in views
  gem "byebug"
  gem "db_fixtures_dump"
  gem "debase", "~> 0.2.9"
  gem "dotenv-rails"
  gem "factory_bot_rails"
  gem "faker"
  gem "parallel_tests"
  gem "pronto"
  gem "pronto-brakeman"
  gem "pronto-eslint_npm"
  gem "pronto-rails_best_practices"
  gem "pronto-rails_schema"
  gem "pronto-rubocop"
  gem "pry-nav"
  gem "pry-rails"
  gem "rails_layout"
  gem "rspec-rails"
end

group :development do
  gem "brakeman", require: false
  gem "fasterer", require: false
  gem "rails_best_practices", require: false
  gem "rubocop", "~> 1.72", require: false
  gem "rubocop-performance", require: false
  gem "rubocop-rails", require: false
  gem "scss-lint", require: false
  gem "web-console"
end

group :test do
  gem "capybara"
  gem "database_cleaner-active_record"
  gem "launchy"
  gem "mock_redis"
  gem "rails-controller-testing"
  gem "rspec-sidekiq"
  gem "simplecov"
  gem "webmock"
end

group :production do
  gem "rails_12factor"
end

gem "central_event_logger", git: "https://github.com/eshopguide/centralized-logging.git"
gem "cross_promotion_app", tag: "v0.3.0", github: "eshopguide/cross-promotion-app"
gem "shopify_billing", "1.1.0", github: "eshopguide/shopify_billing"
