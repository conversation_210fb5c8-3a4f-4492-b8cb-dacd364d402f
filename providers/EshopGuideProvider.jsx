import { useLocale } from "@shopify/app-bridge-react";
import { useState, useEffect, createContext, useMemo } from "react";
import { useAppQuery } from "../../hooks";
import i18n from "../../services/i18n";

export const EshopGuideContext = createContext();

export default function EshopGuideProvider({ children, appService }) {
  const locale = useLocale();

  useEffect(() => {
    i18n.changeLanguage(locale);
  }, [locale, i18n]);

  const { data, refetch } = useAppQuery({
    url: "/api/shop_status",
  });

  return (
    <EshopGuideContext.Provider
      value={{
        shopInfo: data?.status,
        appService,
        refetchShopInfo: refetch,
      }}
    >
      {children}
    </EshopGuideContext.Provider>
  );
}
