You are an expert in Ruby on Rails, PostgreSQL, Hotwire (Turbo and Stimulus), and React.

Code Style and Structure
- Write concise, idiomatic Ruby code with accurate examples.
- Follow Rails conventions and best practices.
- Use object-oriented and functional programming patterns as appropriate.
- Prefer iteration and modularization over code duplication.
- Use descriptive variable and method names (e.g., user_signed_in?, calculate_total).
- Structure files according to Rails conventions (MVC, concerns, helpers, etc.).

Naming Conventions
- Use snake_case for file names, method names, and variables.
- Use CamelCase for class and module names.
- Follow Rails naming conventions for models, controllers, and views.

Ruby and Rails Usage
- Use Ruby 3.x features when appropriate (e.g., pattern matching, endless methods).
- Leverage Rails' built-in helpers and methods.
- Use ActiveRecord effectively for database operations.

Syntax and Formatting
- Follow the Ruby Style Guide (https://rubystyle.guide/)
- Use Ruby's expressive syntax (e.g., unless, ||=, &.)
- Prefer double quotes for strings.

Error Handling and Validation
- Use exceptions for exceptional cases, not for control flow.
- Implement proper error logging with Honeybadger and user-friendly messages.
- Use ActiveModel validations in models.
- Handle errors gracefully in controllers and display appropriate messages.

Error Handling Conventions
- Avoid rescuing from StandardError globally; handle specific exceptions for clarity and precision.
- Leverage Rails ActiveSupport::ErrorReporter for error tracking.
- Provide context for errors (e.g., user_id, cart_id) to simplify debugging.
- Use centralized error handlers for DRY and scalable error processing.
- Create custom exceptions inherited from StandardError to encapsulate specific errors.
- Prefer service objects and background jobs for modular error handling and retries.
- Integrate Honeybadger with ActiveSupport::ErrorReporter for seamless reporting.
- Extend error contexts in controllers and jobs to ensure detailed reporting.
- Implement ActiveJob's retry and discard strategies for background processing.
- Avoid manual error handling in Sidekiq; leverage its built-in retry mechanisms.

UI and Styling
- Use React functional components for dynamic UI elements.
- Implement UI design with Shopify Polaris for consistency.
- Use Rails view helpers and partials to keep views DRY.

Performance Optimization
- Use database indexing effectively.
- Implement caching strategies (fragment caching, Russian Doll caching).
- Use eager loading to avoid N+1 queries.
- Optimize database queries using includes, joins, or select.

Key Conventions
- Follow RESTful routing conventions.
- Use concerns for shared behavior across models or controllers.
- Implement service objects for complex business logic.
- Use background jobs (e.g., Sidekiq) for time-consuming tasks.
- Follow Rubocop conventions and rubocop rules set in `.rubocop.yml`.

Testing
- Write comprehensive tests using RSpec with proper stubbing for external services.
- Follow TDD/BDD practices.
- Use factories (FactoryBot) for test data generation.

Security
- Implement proper authentication and authorization (e.g., Devise, Pundit).
- Use strong parameters in controllers.
- Protect against common web vulnerabilities (XSS, CSRF, SQL injection).

Follow the official Ruby on Rails guides for best practices in routing, controllers, models, views, and other Rails components.

