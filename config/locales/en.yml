en:
  settings:
    invoice_mail_subject_text_with_liquid: 'Your invoice to order {{name}}'
    invoice_mail_body_text: 'Thank you for your purchase. You can find the invoice attached to this mail in PDF format. We hope to welcome you as a customer again soon.'
    liquid_no_order_error: "The preview is created based on the last order in the store. If there is no order, no preview can be displayed."
  import:
    status:
      start: "Starting"
      queued: "Transferring Documents..."
      nothing_to_import: "No orders to import"
    message:
      plan_not_active: "Your plan is not active. Please activate your plan in the settings."
      already_running: "The import is already running."
      nothing_to_import: "No orders to import"
      not_allowed: "Please activate the import to start."
  notifications:
    slack:
      install: "NEW Lexware Office INSTALL"
      uninstall: "SHOP UNINSTALLED Lexware Office"
      import: "NEW Lexware Office IMPORT"
    mailchimp:
      new_install: "Welcome to Lexware Office with Shopify"
      uninstall: "Sorry to see you go"
      warn: "Lexware Office Shopify App - Action required"
      plan_mismatch: "Lexware Office Shopify App - Action required"
      plan_activation: "Almost done – activate your plan now!"
  error_mail:
    instructions: "We have detected that the connection of the app to Lexware Office needs to be renewed. All you have to do is click the button below and confirm the connection renewal. Of course, there are no costs associated with the update for you."
  activerecord:
    errors:
      models:
        shop_setting:
          attributes:
            title:
              expanded_too_long: "After expanding placeholders, the title exceeds the maximum length of %{max_length} characters (current length: %{current_length})"
  exceptions:
    account_expired: "Your Lexware Office plan has expired or needs an upgrade. Your Lexware Office user does not have the required permissions."
    account_number_already_exists: 'Connection problem. Please select the document and click on "transmit again."'
    billing_address: "Customer has more than one billing address."
    company_contactpersons_lastname: "Missing contact information of the company's customer."
    contact_cant_be_created: "Collective customers not allowed and creating customers is not activated."
    contact_cant_be_updated: "The Lexware Office contact cannot be updated."
    countrycode_is_not_valid: "The country of the customer is not filled in correctly."
    custom_smtp: "Your mail server data in Lexware Office is incorrect."
    customer_range: "The customer number range in Lexware Office is exhausted. Please contact Lexware Office support."
    customer_without_country: "The country of the customer is not filled in correctly."
    debitor_account_number_out_of_range: "The customer number range in Lexware Office is exhausted. Please contact Lexware Office support."
    distance_sales_destination_principle_gross: "Your tax settings are incorrect."
    email_limit: "The limit of 60 invoice emails per hour has been reached. Please retry the transmission later."
    insufficient_scope: "Your Lexware Office plan has expired or needs an upgrade. Your Lexware Office user does not have the required permissions."
    invalid_email: "The customer's email address is invalid."
    maintenance: 'Temporary maintenance work is taking place in Lexware Office. Please select the document and click on "transmit again."'
    missing_billing_address: "The customer's billing address contains an error."
    not_connected: "Your connection is not active. Please reconnect your Lexware Office account."
    oss: "Your tax settings are incorrect."
    payment_method_not_found: "The payment method could not be found."
    referenced_entity_missing: "There is no invoice for this credit note. Once the invoice has been transferred, you can try to transfer the document again."
    role_customer: "The email of the orderer is not registered as a customer in Lexware Office."
    tax_settings: "Your tax settings are incorrect."
    timeout: 'Technical error. Please select the document and click on "transmit again."'
    token_expired: "Your connection is not active. Please reconnect your Lexware Office account."
    unauthorized: "Your connection is not active. Please reconnect your Lexware Office account."
    unknown: "An unknown error has occurred."
    wrong_tax_rate: "Your tax settings are incorrect."
    pending_transaction: "Credit note will be created when the transaction is completed."
    eea_tax_settings: "Tax error! Delivery country is an EEA country and may not contain taxes. Please check your tax settings."
    voucher_title_length: "The title exceeds the maximum length of 25 characters. Please shorten the title."
  support:
    settings:
      create_invoices: "Create Invoices"
      mail_invoice: "Mail invoice to customer"
      invoice_mail_subject: "Invoice mail subject"
      invoice_mail_body: "Invoice mail body"
      credit_note_mail_subject: "Subject of the credit email"
      credit_note_mail_body: "Body of the credit email"
      invoice_pretext: "Invoice Pretext"
      invoice_posttext: "Invoice post text"
      invoice_title: "Title of the invoice"
      invoice_language: "Invoice language"
      createRefunds: "Create refunds"
      calculate_shipping_tax: "Automatic calculation of the shipping tax"
      refund_pretext: "Pretext of the credit note"
      refund_posttext: "Post text of the credit note"
      refund_title: "Credit note title"
      on_save_error: "An error has occurred while saving"
      liquid_error: "Liquid Error"
      shipping_types: "Display of the delivery/service types"
      enable_tender_transactions: "Enable synchronization of payment data"
      mark_due_immediately: "Mark due immediately"
      shipping_min_days: "Minimum number of shipping days"
      shipping_max_days: "Maximum number of shipping days"
      tah_creation: "Payment assignment proposal"
      use_SKUs: "Add SKUs to invoice"
      confirm_tax_settings: 'Confirm tax settings'
      order_tags: 'Exclude document creation via tags'
      email_tags: "Exclude document dispatch via tags"
    snippets:
      destroyed: "Snippet was successfully deleted."
      updated: "Snippet was successfully updated."
      created: "Snippet was successfully created."
      error: "An error has occurred. Please enter a store URL, an id and the code."
    beacons:
      updated: "Beacon message was successfully updated."
    coupon_code:
      destroyed: "Coupon code was successfully deleted."
  will_paginate:
    previous_label: "Last page"
    next_label: "Next page"
    page_gap: "..."
  cancelled_invoice:
    message: "Order Cancelled"
  sync_info:
    last_action:
      refund: "Pending transaction"
