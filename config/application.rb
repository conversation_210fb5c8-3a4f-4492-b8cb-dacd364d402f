# frozen_string_literal: true

require_relative 'boot'

require 'rails/all'

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module SevdeskConnector
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 7.1

    # Please, add to the `ignore` list any other `lib` subdirectories that do
    # not contain `.rb` files, or that should not be reloaded or eager loaded.
    # Common ones are `templates`, `generators`, or `middleware`, for example.
    config.autoload_lib(ignore: %w(assets tasks))

    config.assets.prefix = "/api/assets"

    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    # config.time_zone = "Central Time (US & Canada)"
    # config.eager_load_paths << Rails.root.join("extras")

    # Show SVG as images
    config.active_storage.content_types_to_serve_as_binary -= ['image/svg+xml']

    # Use YAML for column serialization.
    # To keep the historic behavior, you can set it to `YAML`, however it is
    # recommended to explicitly define the serialization method for each column
    # rather than to rely on a global default.
    # TODO: Remove after upgrade to Rails 7.2 and removal of audit-log gem
    config.active_record.default_column_serializer = 'YAML'
  end
end
