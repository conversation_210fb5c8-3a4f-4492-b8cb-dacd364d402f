# frozen_string_literal: true
class RedLockPool
  def self.instance
    @instance ||= new
  end

  def initialize
    @redis_client = RedisClient.new(url: ENV.fetch(ENV.fetch('REDIS_PROVIDER')))
    @pool = ConnectionPool.new(size: ENV.fetch('REDIS_POOL_SIZE', 30).to_i) do
      Redlock::Client.new([@redis_client])
    end
  end

  def shutdown(&block)
    @pool.shutdown(&block)
  end

  def with(&block)
    retries = 0
    begin
      @pool.with(&block)
    rescue RedisClient::ConnectionError
      # If connection is closed, create a new one and retry once
      if retries.zero?
        retries += 1
        @redis_client = RedisClient.new(url: ENV.fetch(ENV.fetch('REDIS_PROVIDER')))
        retry
      end
      raise
    end
  end
end
