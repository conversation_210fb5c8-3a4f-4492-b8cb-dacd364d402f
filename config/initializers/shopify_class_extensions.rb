# frozen_string_literal: true

module ShopifyAPI
  class Shop
    def self.current
      ShopifyAPI::Shop.all.first
    end
  end

  class RecurringApplicationCharge
    def self.current
      ShopifyAPI::RecurringApplicationCharge.all.find { |charge| charge.status == "active" }
    end
  end

  class Page
    def published_status
      if published_at.present?
        "published"
      else
        "unpublished"
      end
    end
  end

  class Refund
    def has_pending_transactions?
      transactions.any? { |x| x.status == "pending" }
    end

    def has_successful_transaction?
      transactions.any? { |x| x.status == "success" }
    end

    def successful_refund_transaction
      transactions.detect { |x| x.status == "success" && x.kind == "refund" }
    end

    def calculate_absolute_discount
      discount = 0
      refund_line_items.each do |line_item|
        line_item = line_item.deep_symbolize_keys
        price = line_item.dig(:line_item, :price).to_f
        quantity = line_item[:quantity].to_i
        subtotal = line_item[:subtotal].to_f
        discount += ((price * quantity) - subtotal)
      end
      discount.round(2)
    end

    def calculate_subtotal_amount
      total = 0
      refund_line_items.each do |line_item|
        line_item = line_item.deep_symbolize_keys
        total += line_item[:subtotal].to_f
      end
      total
    end

    def highest_tax_rate
      max_tax_rate = refund_line_items
        .flat_map { |line_item| line_item["line_item"]["tax_lines"] || [] }
        .map { |tax| tax["rate"].to_d }
        .max

      ((max_tax_rate || 0.to_d) * 100).round(2)
    end
  end

  class Fulfillment
    def highest_tax_rate
      max_tax_rate = line_items
        .flat_map do |item|
        (item["tax_lines"] || [])
          .map { |tax| tax["rate"].to_d }
      end
        .max

      ((max_tax_rate || 0.to_d) * 100).round(2)
    end
  end

  class LineItem
    def self.get_all_line_item_discounts(line_item, order)
      DiscountCalculator.calculate_line_item_discounts([line_item], order.discount_applications)
    end

    def self.apply_line_item_discount?(order, line_item)
      line_item[:discount_allocations].each do |discount_allocation|
        discount_allocation = discount_allocation.deep_symbolize_keys

        order.discount_applications.each_with_index do |discount_application, index|
          discount_application = discount_application.deep_symbolize_keys

          return true if discount_allocation[:discount_application_index] == index &&
            DiscountCalculator.valid_line_item_discount?(discount_application)
        end
      end

      false
    end

    def self.free_discount_with_zero_tax_rate?(line_item)
      return false if line_item[:taxable] == false

      line_item[:price].to_d == line_item[:discount_allocations]&.sum { |allocation| allocation[:amount].to_d } &&
        (line_item[:tax_lines].blank? ||
          line_item[:tax_lines]&.any? { |tax_line| tax_line[:rate].to_d.zero? } ||
          (line_item[:taxable] == true && line_item[:tax_lines].first[:price].to_d.zero?))
    end

    def self.taxable_with_no_price?(line_item)
      line_item[:taxable] == true && line_item[:price].to_d.zero?
    end
  end

  class Order
    def self.fetch_next_page
      ShopifyAPI::Order.all(page_info: next_page_info, limit: ENV.fetch("SHOPIFY_API_PAGE_SIZE", 250))
    end

    def self.all_in_store_in_batches(params, &)
      orders = ShopifyAPI::Order.all(processed_at_max: params[:processed_at_max],
        processed_at_min: params[:processed_at_min],
        order: "processed_at asc",
        status: "any", limit: ENV.fetch("SHOPIFY_API_PAGE_SIZE", 250))
      return unless orders.count.positive?

      orders.each(&)

      while ShopifyAPI::Order.next_page?
        orders = ShopifyAPI::Order.fetch_next_page

        orders.each(&)
      end
    end

    def get_title
      "Order #{name} - #{customer.first_name} #{customer.last_name} - #{total_price} #{currency}"
    end

    def get_body
      status = fulfillment_status.nil? ? "Unfulfilled" : fulfillment_status.humanize
      body = "##Status: #{status}
##Payment: #{financial_status.humanize}

#Billing Address

      #{billing_address.first_name} #{billing_address.last_name}
      #{billing_address.address1} #{billing_address.address2}
      #{billing_address.zip} #{billing_address.city}
      #{billing_address.country}

#Shipping Address
      #{shipping_address.first_name} #{shipping_address.last_name}
      #{shipping_address.address1} #{shipping_address.address2}
      #{shipping_address.zip} #{shipping_address.city}
      #{shipping_address.country}
#Line Items
"
      line_items.each do |item|
        status = item.fulfillment_status.nil? ? "Unfulfilled" : item.fulfillment_status
        body << "1. #{item.name} - #{item.quantity}x - #{item.price}, STATUS: **#{status}**
"
      end

      body
    end

    def refunded?
      financial_status == "refunded"
    end

    def partially_refunded?
      financial_status == "partially_refunded"
    end

    def cancelled_and_pending?
      cancelled_at.present? && financial_status == "pending"
    end

    def use_fulfillment?
      use_fulfill_criteria == true && fulfillments.present?
    end

    def no_total_outstanding?
      total_outstanding == "0.00"
    end

    def has_refund?
      refunds.present?
    end

    def has_taxes?
      line_items.any? { |li| li.symbolize_keys[:tax_lines].any? { |tl| tl.symbolize_keys[:rate].positive? } }
    end

    def zero_sum_taxes?
      line_items.all? { |li| li.symbolize_keys[:tax_lines].all? { |tl| tl.symbolize_keys[:price].to_f.zero? } }
    end

    def third_party_country?
      country_code = country_check
      return false if country_code.blank? || country_code[:province_code] == "NIR"

      iso_code = ISO3166::Country.new(country_code[:country_code])
      !iso_code.in_eu? || !iso_code.in_eea?
    end

    def get_payment_reference
      references = []
      t = transactions
      return name if t.nil?

      t.each do |k|
        (references << k.authorization.presence) || name
      end
      references.uniq.join(" / ")
    end

    def has_customer?
      respond_to?(:customer) && customer.is_a?(::ShopifyAPI::Customer)
    end

    def valid_email?
      respond_to?(:customer) && customer.is_a?(::ShopifyAPI::Customer) &&
        customer.email.present? && URI::MailTo::EMAIL_REGEXP.match?(customer.email)
    end

    def discounts_present?
      total_discounts.to_f > 0.0
    end

    def line_items_total_discount
      items_discount = DiscountCalculator.calculate_line_item_discounts(line_items, discount_applications)
      shipping_discount = DiscountCalculator.calculate_line_item_discounts(shipping_lines,
        discount_applications)

      (items_discount + shipping_discount).round(2)
    end

    # the order should at least have a deliverable product
    def at_least_one_delivery?
      line_items.any? { |line_item| line_item.symbolize_keys[:requires_shipping] }
    end

    # tries to extract the VAT number from the order.customer.note with a regular expression
    # then, if not found tries to get it from the order note_attributes
    def vat_number
      vat_from_customer || vat_from_note_attributes
    end

    def extend_order_with_transactions
      self.order_transactions = transactions
      self
    end

    def transactions
      @transactions ||= ShopifyAPI::Transaction.all(order_id: id)
    end

    def no_shipping_information?
      shipping_address.blank? && billing_address.blank?
    end

    def pos_order?
      source_name == "pos"
    end

    def tax_exempt?
      tax_exempt == true || customer&.tax_exempt == true
    end

    def order_tags_array
      tags.present? ? tags&.split(",")&.map(&:strip)&.map(&:downcase) : []
    end

    def mailing_excluded?(shop)
      return false unless shop.feature_enabled?("exclude_emails_by_tag")
      return false if shop.shop_setting.mail_exclusion_tags.blank? || tags.blank?

      exclusion_tags = shop.shop_setting.mail_exclusion_tags.split(",").map(&:strip).map(&:downcase)
      order_tags = tags.split(",").map(&:strip).map(&:downcase)

      (exclusion_tags & order_tags).any?
    end

    def highest_tax_rate
      max_tax_rate = line_items
        .flat_map do |item|
        (item["tax_lines"] || [])
          .map { |tax| tax["rate"].to_d }
      end
        .max

      ((max_tax_rate || 0.to_d) * 100).round(2)
    end

    def b2b?
      [self&.shipping_address, self&.billing_address].compact.any? { |address| address["company"].present? } &&
        vat_number.present?
    end

    def highest_applicable_tax_rate
      use_fulfillment? ? fulfillments.first.highest_tax_rate : highest_tax_rate
    end

    def address_to_use_for_billing(use_shipping_address_for_invoices)
      return billing_address&.symbolize_keys if shipping_address.nil?

      return shipping_address&.symbolize_keys if use_shipping_address_for_invoices && billing_address.nil?

      return nil if billing_address.nil?

      different_countries_in_addresses = shipping_address["country_code"] != billing_address["country_code"]
      use_shipping_address = use_shipping_address_for_invoices && different_countries_in_addresses

      use_shipping_address ? shipping_address&.symbolize_keys : billing_address&.symbolize_keys
    end

    private

    def vat_from_customer
      if has_customer? && !customer.note.nil?
        vat_numbers = customer.note.scan(/(?:VAT\s*ID\s*:\s*|VAT\s*:\s*|VAT\s*)?([A-Z]{2}[A-Z0-9]{8,12})/).map do |match|
          match[0]
        end
        if vat_numbers.present?
          vat_numbers.map! { |vat| vat.gsub(/\s/, "").upcase }
          return vat_numbers.first
        end
      end
      nil
    end

    def vat_from_note_attributes
      return nil unless respond_to?(:note_attributes)

      # loop through all notes and check for vatId and return the first one
      vat_attribute = note_attributes.find do |note|
        note["name"].casecmp("vatid").zero? || note["name"].casecmp("vat-id").zero?
      end
      vat_number = vat_attribute["value"] if vat_attribute.present?
      # checks vat_number for valid european VAT format
      if vat_number.present? && vat_number.match?(/\b([A-Za-z]{2,3}\s*[A-Za-z0-9]{9,14})\b/)
        vat_number.gsub!(/\s/, "")
        return vat_number.upcase
      end
      nil
    end

    def country_check
      address_to_check ||= [self&.shipping_address, self&.billing_address].find(&:present?)&.symbolize_keys
      return false if address_to_check.blank?

      address_to_check
    end
  end

  class Product
    def get_body
      inventory = variants.first.inventory_management ? variants.first.inventory_quantity : "not tracked"
      "#{ReverseMarkdown.convert body_html}
##Inventory: #{inventory}
##Price: #{variants.first.price}"
    end
  end

  class Customer
    sig { returns(T.nilable(T::Hash[T.untyped, T.untyped])) }
    attr_reader :billing_address
  end

  class TenderTransaction
    def self.fetch_next_page(**params)
      last_id = JSON.parse(Base64.decode64(next_page_info))["last_id"]
      all(since_id: last_id, **params)
    end
  end
end
