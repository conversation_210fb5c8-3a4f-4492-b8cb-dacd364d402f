# frozen_string_literal: true

Rails.application.config.middleware.use OmniAuth::Builder do
  provider :developer, { key: <PERSON>N<PERSON>['LEXOFFICE_KEY'], secret: ENV['LEXOFFICE_SECRET'] } if Rails.env.development?
  provider :lexoffice, { client_id: ENV['LEXOFFICE_KEY'], client_secret: ENV['LEXOFFICE_SECRET'],
                         site: ENV['LEXOFFICE_SITE'], iframe: true, scope: ENV['LEXOFFICE_SCOPE'],
                         provider_ignores_state: true,
                         client_options: { site: ENV['LEXOFFICE_SITE'],
                                           authorize_url: "#{ENV['LEXOFFICE_SITE']}/oauth2/authorize",
                                           token_url: "#{ENV['LEXOFFICE_SITE']}/oauth2/token",
                                           response_type: 'code' } }

  provider :google_oauth2,
           EN<PERSON>['GOOGLE_OAUTH_CLIENT_ID'],
           ENV['GOOGLE_OAUTH_CLIENT_SECRET'],
           path_prefix: '/support_users/auth'

  on_failure { |env| OmniauthCallbacksController.action(:failure).call(env) }

end
# allow get requests to make app work for safari users
OmniAuth.config.allowed_request_methods = %i[post get]
OmniAuth.config.logger = Rails.logger
