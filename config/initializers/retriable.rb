# This is the default settings for Retriable:
# Retry #	Min	Average	Max
# 1	0.25	0.5	0.75
# 2	0.375	0.75	1.125
# 3	0.563	1.125	1.688
# 4	0.844	1.688	2.531
# 5	1.266	2.531	3.797
# 6	1.898	3.797	5.695
# 7	2.848	5.695	8.543
# 8	4.271	8.543	12.814
# 9	6.407	12.814	19.222
# 10	stop	stop	stop

# so a single thread would be blocked max. 19 seconds

Retriable.configure do |c|
  c.contexts[:lexoffice_api] = {
    on: [RestClient::TooManyRequests, RestClient::BadGateway, RestClient::ServiceUnavailable,
         RestClient::GatewayTimeout, RestClient::InternalServerError],
    tries: ENV.fetch('LEXOFFICE_API_TRIES', 3).to_i,
  }
end