# frozen_string_literal: true

ShopifyApp.configure do |config|
  config.embedded_app = true
  config.after_authenticate_job = false
  config.shop_session_repository = "Shop"
  config.log_level = :info
  config.api_key = ENV.fetch("SHOPIFY_CLIENT_API_KEY", "").presence
  config.secret = ENV.fetch("SHOPIFY_CLIENT_API_SECRET", "").presence

  config.webhooks = [
    { topic: "app/uninstalled", path: "uninstall" },
    { topic: "shop/update", path: "api/webhooks/shop_update" },
    { topic: "app_subscriptions/update", path: "api/webhooks/app_subscriptions_update" }
  ]
end

ShopifyAPI::Context.setup(
  api_key: ENV.fetch("SHOPIFY_CLIENT_API_KEY", ""),
  api_secret_key: ENV.fetch("SHOPIFY_CLIENT_API_SECRET", ""),
  host: ENV.fetch("HOST", "https://#{ENV.fetch("HOST_NAME")}"),
  host_name: ENV.fetch("HOST_NAME"),
  scope: "read_orders,write_orders,read_all_orders",
  api_version: "2024-10",
  is_embedded: true,
  is_private: false,
  logger: Rails.logger
)

Rails.application.config.after_initialize do
  ShopifyApp::WebhooksManager.add_registrations
end
