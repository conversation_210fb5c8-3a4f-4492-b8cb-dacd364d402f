# frozen_string_literal: true

Rails.application.configure do
  config.lograge.enabled = true
  config.lograge.ignore_actions = ['Rails::HealthController#show']
  config.lograge.formatter = Lograge::Formatters::Json.new
  config.lograge.custom_options = lambda do |event|
    exceptions = %w[controller action format]
    params = event.payload[:params].except(*exceptions) rescue {}
    request_id = event.payload[:request].request_id rescue nil

    {
      params:,
      request_id:,
    }
  end
end
