# frozen_string_literal: true

# Patch for ActiveJob to apply the Rails filter parameters as defined in filter_parameter_logging.rb
# to job arguments as well.
Rails.application.config.after_initialize do
  require "active_job/log_subscriber"
  module ActiveJob
    class LogSubscriber < ActiveSupport::LogSubscriber
      private

      alias original_format format

      def format(arg)
        if arg.is_a?(Hash)
          parameter_filter = ActiveSupport::ParameterFilter.new(Rails.application.config.filter_parameters)
          parameter_filter.filter(arg.transform_values { |value| original_format(value) })
        else
          original_format(arg)
        end
      end
    end
  end
end
