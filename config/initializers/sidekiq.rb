#if Rails.env.development?
# require 'sidekiq/testing'
# Sidekiq::Testing.inline!
#end

class RedlockLockErrorMiddleware
  def call(worker, job, queue)
    begin
      yield
    rescue Redlock::LockError => e
      # Re-enqueue the job without incrementing retries
      worker.class.perform_in(rand(2..10).seconds, *job['args'])
    end
  end
end


class SidekiqMiddleware
  def call(worker, job, queue)
    worker.retry_count = job['retry_count'] if worker.respond_to?(:retry_count=)
    yield
  end
end

Sidekiq.configure_server do |config|
  config.server_middleware do |chain|
    chain.add SidekiqMiddleware
    chain.add RedlockLockErrorMiddleware
  end
end

# Temporary disable strict argument checking for Sidekiq jobs
# TODO: We should not disable this, but we need to refactor the jobs to use the JSON type arguments
Sidekiq.strict_args!(false)
