# Be sure to restart your server when you modify this file.
# Define an application-wide content security policy
# For further information see the following documentation
# https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy
initial_frame_ancestors = [:https, '*.myshopify.com', 'admin.shopify.com']

def current_domain
  @current_domain ||= (params[:shop] && ShopifyApp::Utils.sanitize_shop_domain(params[:shop])) ||
                      request.env['jwt.shopify_domain'] ||
                      session[:shopify_domain]
rescue StandardError
  nil
end

frame_ancestors = -> { current_domain ? [current_domain, 'admin.shopify.com'] : initial_frame_ancestors }
Rails.application.config.content_security_policy do |policy|
  policy.default_src(:https, :self)
  policy.style_src(:https, "'unsafe-inline'", 'cdn.shopifycloud.com')

  # Allow @vite/client to hot reload style changes in development
  policy.style_src *policy.style_src, :unsafe_inline if Rails.env.development?

  policy.script_src(:blob, :https, "'unsafe-inline'", 'cdn.shopifycloud.com cdn.loom.com')

  # Allow @vite/client to hot reload javascript changes in development
  policy.script_src *policy.script_src, :unsafe_eval, "http://#{ ViteRuby.config.host_with_port } ws://#{ ViteRuby.config.host_with_port }" if Rails.env.development?
  policy.connect_src *policy.connect_src, :self, :https, "ws://#{ ViteRuby.config.host_with_port }" if Rails.env.development?
  policy.connect_src *policy.connect_src, :self, :https, :wss, 'ws://localhost:3036', 'wss://lexoffice-shopify-3000.eu.ngrok.io', 'ws://127.0.0.1:3000' if Rails.env.development?

  # You may need to enable this in production as well depending on your setup.
  policy.script_src *policy.script_src, :blob if Rails.env.test?

  policy.img_src(:self, :https, :data, 'cdn.shopifycloud.com') unless Rails.env.development?
  policy.img_src(:self, :https, :http, :data, 'cdn.shopifycloud.com', 'localhost:3036') if Rails.env.development?
  policy.upgrade_insecure_requests(true)
  policy.frame_ancestors(frame_ancestors)
end
# If you are using UJS then enable automatic nonce generation
# Rails.application.config.content_security_policy_nonce_generator = -> request { SecureRandom.base64(16) }
# Set the nonce only to specific directives
# Rails.application.config.content_security_policy_nonce_directives = %w(script-src)
# Report CSP violations to a specified URI
# For further information see the following documentation:
# https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy-Report-Only
# Rails.application.config.content_security_policy_report_only = true
