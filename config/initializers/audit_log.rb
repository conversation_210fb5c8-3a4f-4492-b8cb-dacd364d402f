# frozen_string_literal: true

AuditLog.configure do
  # class name of you User model, default: 'User'
  self.user_class = 'SupportUser'
  # current_user method name in your Controller, default: 'current_user'
  self.current_user_method = 'current_support_user'
end

# for pagination to work in audit-log we need this
if defined?(WillPaginate)
  module WillPaginate
    module ActiveRecord
      module RelationMethods
        alias per per_page
        alias num_pages total_pages
      end
    end
  end
end

module ActiveRecord
  class Relation
    alias total_count count
  end
end
