# frozen_string_literal: true

# Be sure to restart your server when you modify this file.

# Configure parameters to be partially matched (e.g. passw matches password) and filtered from the log file.
# Use this to limit dissemination of sensitive information.
# See the ActiveSupport::ParameterFilter documentation for supported notations and behaviors.
Rails.application.config.filter_parameters += [
  :passw, :email, :secret, :token, :_key, :crypt, :salt, :certificate, :otp, :ssn,
  # sensitive user information
  :first_name, :last_name, :name, :phone, :address1, :address2, :city, :province, :zip, :country, :country_code,
  # ActiveJob-related fields (handled by ActiveJobFilterParameters)
  :event_value, :customer_info, :payload, :webhook
]
