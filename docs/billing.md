# Billing

## App Subscription & BillingPlan
Damit ein Shop aktiv ist, muss eine aktive AppSubscription vorhanden sein. Über das `Charge` Model kann eine Verbindung zwischen der AppSubscription und dem BillingPlan hergestellt werden:

![image](images/billing_plan.png)

```ruby
shop.plan_active? # -> shop.billing_plan.present?
```

> [!NOTE]
> Bei Deinstallation wird die Charge automatisch von Shopify gecanceled.

## One-Time Purchases (Imports)
Um zu überprüfen, ob das Import-Feature freigeschaltet ist, kann der Status der One-Time Purchases überprüft werden: 
```ruby
shop.one_time_purchases
```

> [!NOTE]
> One-Time Purchases bleiben auch nach Deinstallation aktiv, d.h. wenn ein Shop die App deinstalliert und dann wieder installiert, bleibt die Charge aktiv.

## Charge Creation
![image](images/charge-creation.png)

## Migration

1. Maintenance-Mode e<PERSON>, Background Worker ausschalten
2. Deployment
3. Import der Charges (Charge <-> BillingPlan Mapping)
```
$ rake charges:import_import_recurring_charges
```
4. Maintenance-Mode ausschalten
