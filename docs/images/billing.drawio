<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="26.0.11" pages="2">
  <diagram id="C5RBs43oDa-KdzZeNtuy" name="Billing Plan">
    <mxGraphModel dx="2074" dy="761" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-0" />
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-1" parent="WIyWlLk6GJQsqaUBKTNV-0" />
        <mxCell id="y0SHLgFNKLur0YQ0IW5T-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="WIyWlLk6GJQsqaUBKTNV-3" target="y0SHLgFNKLur0YQ0IW5T-0">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y0SHLgFNKLur0YQ0IW5T-4" value="AppSubscription" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="y0SHLgFNKLur0YQ0IW5T-3">
          <mxGeometry x="-0.1333" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-3" value="Shop.billing_plan" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="160" y="80" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="y0SHLgFNKLur0YQ0IW5T-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="y0SHLgFNKLur0YQ0IW5T-0" target="y0SHLgFNKLur0YQ0IW5T-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y0SHLgFNKLur0YQ0IW5T-0" value="Shopify Admin API" style="shape=image;html=1;verticalAlign=top;verticalLabelPosition=bottom;labelBackgroundColor=#ffffff;imageAspect=0;aspect=fixed;image=https://cdn3.iconfinder.com/data/icons/social-media-2068/64/_shopping-128.png" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="185" y="180" width="70" height="70" as="geometry" />
        </mxCell>
        <mxCell id="y0SHLgFNKLur0YQ0IW5T-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="y0SHLgFNKLur0YQ0IW5T-5" target="y0SHLgFNKLur0YQ0IW5T-15">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y0SHLgFNKLur0YQ0IW5T-18" value="Ja" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="y0SHLgFNKLur0YQ0IW5T-17">
          <mxGeometry x="-0.3" relative="1" as="geometry">
            <mxPoint x="5" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="y0SHLgFNKLur0YQ0IW5T-5" value="Active App&lt;br&gt;Subscription?" style="rhombus;whiteSpace=wrap;html=1;shadow=0;fontFamily=Helvetica;fontSize=12;align=center;strokeWidth=1;spacing=6;spacingTop=-4;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="170" y="310" width="100" height="100" as="geometry" />
        </mxCell>
        <mxCell id="y0SHLgFNKLur0YQ0IW5T-7" value="nil" style="html=1;dashed=0;whiteSpace=wrap;shape=mxgraph.dfd.start" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="180" y="480" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="y0SHLgFNKLur0YQ0IW5T-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0.5;entryDx=0;entryDy=-15;entryPerimeter=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="y0SHLgFNKLur0YQ0IW5T-5" target="y0SHLgFNKLur0YQ0IW5T-7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y0SHLgFNKLur0YQ0IW5T-9" value="Nein" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="y0SHLgFNKLur0YQ0IW5T-8">
          <mxGeometry x="-0.08" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="y0SHLgFNKLur0YQ0IW5T-15" value="charges" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=50;horizontalStack=0;rounded=1;fontSize=14;fontStyle=0;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;arcSize=4;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="330" y="310" width="110" height="100" as="geometry" />
        </mxCell>
        <mxCell id="y0SHLgFNKLur0YQ0IW5T-16" value="+ shopify_id&lt;br&gt;+ billing_plan_id" style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;html=1;" vertex="1" parent="y0SHLgFNKLur0YQ0IW5T-15">
          <mxGeometry y="50" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="y0SHLgFNKLur0YQ0IW5T-19" value="billing_plans" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=50;horizontalStack=0;rounded=1;fontSize=14;fontStyle=0;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;arcSize=4;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="480" y="310" width="110" height="100" as="geometry" />
        </mxCell>
        <mxCell id="y0SHLgFNKLur0YQ0IW5T-20" value="+ id&lt;br&gt;+ ..." style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;html=1;" vertex="1" parent="y0SHLgFNKLur0YQ0IW5T-19">
          <mxGeometry y="50" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="y0SHLgFNKLur0YQ0IW5T-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="y0SHLgFNKLur0YQ0IW5T-16" target="y0SHLgFNKLur0YQ0IW5T-20">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="4t_UroVMqTIiOHgFDwD1" name="Charge Creation">
    <mxGraphModel dx="2074" dy="761" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="JLRTLN0DkYLkn8_eKsVU-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="JLRTLN0DkYLkn8_eKsVU-3" target="JLRTLN0DkYLkn8_eKsVU-17">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="220" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="JLRTLN0DkYLkn8_eKsVU-2" value="Create Charge (Recurring / One Time)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="JLRTLN0DkYLkn8_eKsVU-1">
          <mxGeometry x="-0.1333" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="JLRTLN0DkYLkn8_eKsVU-3" value="BillingServices::CreateChargeService" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" vertex="1" parent="1">
          <mxGeometry x="105" y="80" width="230" height="40" as="geometry" />
        </mxCell>
        <mxCell id="JLRTLN0DkYLkn8_eKsVU-23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="JLRTLN0DkYLkn8_eKsVU-5" target="JLRTLN0DkYLkn8_eKsVU-22">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JLRTLN0DkYLkn8_eKsVU-24" value="Redirect" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="JLRTLN0DkYLkn8_eKsVU-23">
          <mxGeometry x="0.075" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="JLRTLN0DkYLkn8_eKsVU-5" value="Shopify Admin API" style="shape=image;html=1;verticalAlign=top;verticalLabelPosition=bottom;labelBackgroundColor=#ffffff;imageAspect=0;aspect=fixed;image=https://cdn3.iconfinder.com/data/icons/social-media-2068/64/_shopping-128.png" vertex="1" parent="1">
          <mxGeometry x="185" y="250" width="70" height="70" as="geometry" />
        </mxCell>
        <mxCell id="JLRTLN0DkYLkn8_eKsVU-12" value="charges" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=50;horizontalStack=0;rounded=1;fontSize=14;fontStyle=0;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;arcSize=4;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="360" y="140" width="110" height="100" as="geometry" />
        </mxCell>
        <mxCell id="JLRTLN0DkYLkn8_eKsVU-13" value="+ shopify_id&lt;br&gt;+ billing_plan_id" style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;html=1;" vertex="1" parent="JLRTLN0DkYLkn8_eKsVU-12">
          <mxGeometry y="50" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JLRTLN0DkYLkn8_eKsVU-14" value="billing_plans" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=50;horizontalStack=0;rounded=1;fontSize=14;fontStyle=0;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;arcSize=4;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="510" y="140" width="110" height="100" as="geometry" />
        </mxCell>
        <mxCell id="JLRTLN0DkYLkn8_eKsVU-15" value="+ id&lt;br&gt;+ ..." style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;html=1;" vertex="1" parent="JLRTLN0DkYLkn8_eKsVU-14">
          <mxGeometry y="50" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JLRTLN0DkYLkn8_eKsVU-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;" edge="1" parent="1" source="JLRTLN0DkYLkn8_eKsVU-13" target="JLRTLN0DkYLkn8_eKsVU-15">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JLRTLN0DkYLkn8_eKsVU-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="JLRTLN0DkYLkn8_eKsVU-17" target="JLRTLN0DkYLkn8_eKsVU-13">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="340" y="190" />
              <mxPoint x="340" y="190" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JLRTLN0DkYLkn8_eKsVU-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="JLRTLN0DkYLkn8_eKsVU-17" target="JLRTLN0DkYLkn8_eKsVU-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JLRTLN0DkYLkn8_eKsVU-17" value="Create Charge" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" vertex="1" parent="1">
          <mxGeometry x="170" y="170" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="JLRTLN0DkYLkn8_eKsVU-26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="JLRTLN0DkYLkn8_eKsVU-22" target="JLRTLN0DkYLkn8_eKsVU-25">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JLRTLN0DkYLkn8_eKsVU-22" value="Charge Confirmation Screen" style="shape=image;html=1;verticalAlign=top;verticalLabelPosition=bottom;labelBackgroundColor=#ffffff;imageAspect=0;aspect=fixed;image=https://cdn3.iconfinder.com/data/icons/social-media-2068/64/_shopping-128.png" vertex="1" parent="1">
          <mxGeometry x="185" y="400" width="70" height="70" as="geometry" />
        </mxCell>
        <mxCell id="JLRTLN0DkYLkn8_eKsVU-25" value="BillingServices::HandleChargeService" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" vertex="1" parent="1">
          <mxGeometry x="360" y="415" width="230" height="40" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
