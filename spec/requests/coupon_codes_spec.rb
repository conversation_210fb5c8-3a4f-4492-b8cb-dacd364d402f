 require 'rails_helper'

# This spec was generated by rspec-rails when you ran the scaffold generator.
# It demonstrates how one might use RSpec to test the controller code that
# was generated by Rails when you ran the scaffold generator.
#
# It assumes that the implementation code is generated by the rails scaffold
# generator. If you are using any extension libraries to generate different
# controller code, this generated spec may or may not pass.
#
# It only uses APIs available in rails and/or rspec-rails. There are a number
# of tools you can use to make these specs even more expressive, but we're
# sticking to rails and rspec-rails APIs to keep things simple and stable.

 RSpec.describe '/coupon_codes', type: :request do

   before do
     # Sign in the user
     sign_in support_user
   end

   # CouponCode. As you add validations to CouponCode, be sure to
   # adjust the attributes here as well.
   let(:valid_attributes) do
     { coupon_code: 'ZFABDG', redeemed: false, shop_id: 1, free_days: 10 }
   end

   let(:invalid_attributes) do
     { coupon_code: 'ZnABDG10', redeemed: false, shop_id: 1, free_days: 10 }
   end

   let(:support_user) { create(:support_user) }

   describe 'GET /index' do
     xit 'renders a successful response' do
       CouponCode.create! valid_attributes
       get coupon_codes_url, headers: headers
       expect(response).to be_successful
     end
   end

   describe 'POST /create' do
     context 'with valid parameters' do
       xit 'creates a new CouponCode' do
         expect do
           post '/coupon_codes/create', params: { number_of_coupons: 2, free_days: 10 }, headers: headers
         end.to change(CouponCode, :count).by(2)
       end
     end

     context 'with invalid parameters' do
       xit 'does not create a new CouponCode' do
         expect do
           post '/coupon_codes/create', params: { number_of_coupons: -2, free_days: 10 }, headers: headers
         end.to change(CouponCode, :count).by(0)
       end

       xit "renders a successful response (i.e. to display the 'new' template)" do
         post '/coupon_codes/create', params: { number_of_coupons: -2, free_days: 10 }, headers: headers
         expect(response.status).to match(302)
       end
     end
   end

   describe 'DELETE /destroy' do
     xit 'destroys the requested coupon_code' do
       coupon_code = CouponCode.create! valid_attributes
       expect do
         delete coupon_code_url(coupon_code), params: { id: coupon_code.id }, headers: headers
       end.to change(CouponCode, :count).by(-1)
     end
   end
 end
