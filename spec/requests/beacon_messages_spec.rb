# frozen_string_literal: true

require 'rails_helper'

# This spec was generated by rspec-rails when you ran the scaffold generator.
# It demonstrates how one might use RSpec to test the controller code that
# was generated by <PERSON>s when you ran the scaffold generator.
#
# It assumes that the implementation code is generated by the rails scaffold
# generator. If you are using any extension libraries to generate different
# controller code, this generated spec may or may not pass.
#
# It only uses APIs available in rails and/or rspec-rails. There are a number
# of tools you can use to make these specs even more expressive, but we're
# sticking to rails and rspec-rails APIs to keep things simple and stable.

RSpec.describe '/beacon_messages', type: :request do
  # BeaconMessage. As you add validations to BeaconMessage, be sure to
  # adjust the attributes here as well.
  let(:valid_attributes) do
    { name: 'Invoice Message', help_scout_id: '92953395-ec14-4f2b-a5b0-4f081cc8170c', new_feature_message: false,
      domain_name: 'invoice' }
  end

  let(:invalid_attributes) do
    { name: 1, help_scout_id: '92953395-ec14-4f2b-a5b0-4f081cc8170c', new_feature_message: false,
      domain_name: 'invoice' }
  end

  let(:headers) do
    { 'HTTP_AUTHORIZATION' => ActionController::HttpAuthentication::Basic.encode_credentials(ENV['SUPPORT_USERNAME'],
                                                                                             ENV['SUPPORT_PASSWORD']) }
  end

  describe 'GET /index' do
    xit 'renders a successful response' do
      BeaconMessage.create! valid_attributes
      get beacon_messages_url, headers: headers
      expect(response).to be_successful
    end
  end

  describe 'POST /create' do
    context 'with valid parameters' do
      xit 'creates a new BeaconMessage' do
        expect do
          post '/beacon_messages', params: { beacon_message: valid_attributes }, headers: headers, xhr: true
        end.to change(BeaconMessage, :count).by(1)
      end
    end

    context 'with invalid parameters' do
      xit 'does not create a new BeaconMessage' do
        expect do
          post '/beacon_messages', params: { beacon_message: invalid_attributes }, headers: headers, xhr: true
        end.to change(BeaconMessage, :count).by(0)
      end
    end
  end

  describe 'DELETE /destroy' do
    xit 'destroys the requested beacon_message' do
      beacon_message = BeaconMessage.create! valid_attributes
      expect do
        delete "/beacon_messages/#{beacon_message.id}", headers: headers, xhr: true
      end.to change(BeaconMessage, :count).by(-1)
    end
  end
end
