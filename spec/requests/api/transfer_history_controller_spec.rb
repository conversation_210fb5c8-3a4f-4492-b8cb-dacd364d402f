# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::TransferHistoryController, type: :request do
  let(:shop) { create(:shop) }

  before do
    mock_shopify_requests
    stub_shopify_authenticated(shop)
  end

  let!(:sync_infos) do
    create_list(:sync_info, 5, :invoice_created, shop:)
    create_list(:sync_info, 3, :credit_note_created, shop:)
  end
  let(:error_logs) do
    create_list(:error_log, 3, shop:)
  end

  describe 'GET /api/transfer_history' do
    it 'returns transfers based on the search, filter, sort_by, and sort_dir parameters' do
      get '/api/transfer_history',
          params: { search: sync_infos.first.shopify_order_name,
                    filter: sync_infos.first.target_type,
                    sortBy: 'shopify_order_names',
                    sortDir: 'asc' }

      json_response = response.parsed_body

      expect(response.status).to eq(200)
      expect(json_response['transfers'].size).to eq(1)
      expect(json_response['transfers'][0]['order_id']).to eq(sync_infos.first.shopify_order_name)
      expect(json_response['errors']).to eq(shop.errors?)
      expect(json_response['totalPages']).to be_present
    end
  end

  describe 'POST /api/retry_transfers' do
    it 'queues retried transfers with the correct time offset' do
      order_ids = error_logs.map(&:order_id)
      error_logs.each do |error_log|
        allow(error_log).to receive(:retry_job)
      end
      post '/api/retry_transfers', params: { order_ids: }

      json_response = response.parsed_body

      expect(response.status).to eq(200)
      expect(json_response['success']).to be true
      expect(json_response['job_count']).to eq(error_logs.size)
      expect(json_response['last_retry_time']).to be_present
    end
  end
end
