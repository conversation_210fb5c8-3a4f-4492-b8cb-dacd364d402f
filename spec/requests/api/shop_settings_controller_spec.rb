# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::ShopSettingsController, type: :request do
  let(:shop) { create(:shop, :with_shop_setting_with_tender_transactions, :with_transaction_setting) }

  before do
    mock_shopify_requests
    stub_shopify_authenticated(shop)
    stub_print_layouts
  end

  describe 'GET #shop_settings' do
    let!(:shop_setting) { shop.shop_setting }
    let!(:transaction_setting) { shop.transaction_setting }
    it 'renders shop settings and transaction settings' do
      get('/api/shop_settings')

      expect(JSON.parse(response.body)).to eq({
        beacon_messages: [],
        shop_setting: shop_setting.as_json,
        transaction_setting: transaction_setting.as_json,
        layouts: {}
      }.as_json)
    end
  end

  describe 'PUT #update' do
    let!(:shop_setting) { shop.shop_setting }
    let!(:transaction_setting) { shop.transaction_setting }
    context 'with valid parameters' do
      let(:params) do
        {
          shop_setting: {
            create_orders: false,
            create_invoices: false
          },
          transaction_setting: {
            enable_amazon: true,
            enable_credit_card: true,
            extra_accounts_info: { enable_vorkasse: 'true' }
          }
        }
      end

      it 'updates the shop settings and transaction settings' do
        post('/api/shop_settings', params:)

        shop_setting.reload
        transaction_setting.reload

        expect(response).to have_http_status(:success)
        expect(JSON.parse(response.body)).to eq({
          success: true,
          shop_setting: shop_setting.as_json,
          transaction_setting: transaction_setting.as_json
        }.as_json)

        expect(shop_setting.create_orders).to eq(params[:shop_setting][:create_orders])
        expect(shop_setting.create_invoices).to eq(params[:shop_setting][:create_invoices])
        expect(transaction_setting.enable_amazon).to eq(params[:transaction_setting][:enable_amazon])
        expect(transaction_setting.enable_credit_card).to eq(params[:transaction_setting][:enable_credit_card])
        expect(transaction_setting.extra_accounts_info['enable_vorkasse'])
          .to eq(params[:transaction_setting][:extra_accounts_info][:enable_vorkasse])
      end

      it 'enqueues a FinancialAccountJob' do
        post('/api/shop_settings', params:)
        expect(FinancialAccountJob).to have_enqueued_sidekiq_job(shop.id)
      end
    end

    context 'with invalid parameters' do
      let(:params) do
        {
          shop_setting: {
            create_orders: nil,
            create_invoices: false
          },
          transaction_setting: {
            enable_amazon: true,
            enable_credit_card: true,
            extra_accounts_info: { enable_vorkasse: 'true' }
          }
        }
      end

      it 'returns a 422 status code and does not update shop settings and transaction settings' do
        post('/api/shop_settings', params:)

        shop_setting.reload
        transaction_setting.reload

        expect(JSON.parse(response.body)).to eq({
          success: true,
          shop_setting: shop_setting.as_json,
          transaction_setting: transaction_setting.as_json
        }.as_json)

        expect(shop_setting.create_orders).to eq(shop_setting.create_orders)
        expect(shop_setting.create_invoices).to eq(false)
        expect(transaction_setting.enable_amazon).to eq(true)
        expect(transaction_setting.enable_credit_card).to eq(true)
      end
    end
  end
end
