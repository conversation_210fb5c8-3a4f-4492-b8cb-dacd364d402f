# frozen_string_literal: true

require 'rails_helper'
# TODO: implement tests
RSpec.describe 'Api::Imports', type: :request do
  before do
    mock_shopify_requests
    login(shop)
  end

  let(:shop) { create(:shop, :with_shop_setting_with_tender_transactions, :with_transaction_setting) }

  describe 'POST /import' do
    xit 'returns http success' do
      post '/api/import/import'
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST /count' do
    xit 'returns http success' do
      post '/api/import/count'
      expect(response).to have_http_status(:success)
    end
  end
end
