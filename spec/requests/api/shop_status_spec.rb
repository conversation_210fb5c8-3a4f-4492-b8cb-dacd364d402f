# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::ShopStatus', type: :request do
  before do
    stub_shopify_shop_current
    stub_shopify_authenticated(shop)
    allow_any_instance_of(GetOrderTags).to receive(:call).and_return([])

    allow_any_instance_of(Lexoffice::Profile).to receive(:get_info).and_return(
      { 'companyName' => 'Test Company',
        'organizationId' => shop.lexoffice_organization_id,
        'taxType' => shop.lexoffice_tax_type,
        'smallBusiness' => shop.lexoffice_small_business,
        'connectionId' => shop.lexoffice_connection_id,
        'distanceSalesPrinciple' => 'not defined' }
    )
  end

  describe 'GET /api/shop_status' do
    before do
      allow(Shop).to receive(:find_or_create_new).and_return(shop)
      allow(shop).to receive(:billing_plan).and_return(billing_plan)
      allow(shop).to receive(:import_unlocked?).and_return(true)
    end

    let(:shop) { create(:shop, :with_shop_setting) }
    let(:billing_plan) { create(:billing_plan, plan_type: 'recurring') }
    let(:status_response) do
      { 'status' => { 'billing' => { 'plan_active' => true,
                                     'billing_plan_id' => shop.billing_plan.id,
                                     'billing_plan_name' => shop.billing_plan.name,
                                     'billing_plan_price' => shop.billing_plan.price.to_s,
                                     'import_unlocked' => shop.import_unlocked?,
                                     'remaining_trial_days' => shop.remaining_trial_days,
                                     'trial_ends_on' => shop.trial_ends_on.to_s,
                                     'plan_mismatch_since' => shop.plan_mismatch_since,
                                     'features' => shop.billing_plan.features,
                                      'legacy' => shop.billing_plan.is_legacy },
                      'service' => { 'connected_to_service' => true,
                                     'connection_info' => { 'lexoffice_organization_id' => nil,
                                                            'lexoffice_organization_name' => 'Test Company',
                                                            'lexoffice_tax_type' => shop.lexoffice_tax_type,
                                                            'lexoffice_small_business' => shop.lexoffice_small_business,
                                                            'lexoffice_connection_id' => shop.lexoffice_connection_id,
                                                            'connection_established_at' => shop
                                                              .connection_established_at.iso8601(3) } },
                      'settings' => { 'invoice_creation_enabled' => shop.shop_setting.create_invoices,
                                      'refund_creation_enabled' => shop.shop_setting.create_refunds,
                                      'tax_settings_confirmed' => shop.shop_setting.confirm_tax_settings },
                      'id' => shop.id,
                      'import' => { 'import_running' => false,
                                    'import_job_id' => nil },
                      'available_plans' => { 'recurring' => [{ 'id' => shop.billing_plan.id,
                                                               'name' => shop.billing_plan.name,
                                                               'price' => shop.billing_plan.price.to_s,
                                                               'features' => I18n.t(shop.billing_plan.features),
                                                               'type' => 'recurring' }] },
                      'orders_tags' => [],
                      'shop_domain' => shop.shopify_domain,
                      'install_date' => shop.install_date } }
    end
    it 'returns a success response with JSON status' do
      get api_shop_status_path, params: { locale: 'en' }, headers: { Accept: 'application/json' }
      expect(response).to be_successful
      expect(response.content_type).to include('application/json')
      status = status_response
      expect(response.parsed_body).to eq(status)
    end
  end
end
