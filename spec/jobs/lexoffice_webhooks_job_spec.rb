# frozen_string_literal: true

require 'rails_helper'
require 'sidekiq/testing'
Sidekiq::Testing.fake!

RSpec.describe LexofficeWebhooksJob, type: :job do
  let(:shop) { create(:shop) }
  let(:event_subscription_endpoint) { instance_double('Lexoffice::EventSubscription') }
  let(:hooks) do
    %i[register_token_revoked_hook
       register_invoice_status_changed_hook
       register_credit_note_status_changed_hook]
  end

  before do
    allow(Shop).to receive(:find).and_return(shop)
    allow(shop).to receive(:refresh_token_if_expired)
    allow(Lexoffice::EventSubscription).to receive(:new).and_return(event_subscription_endpoint)
    hooks.each do |hook|
      allow(event_subscription_endpoint).to receive(hook)
    end
  end

  describe '#perform' do
    it 'calls refresh_token_if_expired on the shop' do
      described_class.new.perform(shop.id)
      expect(shop).to have_received(:refresh_token_if_expired)
    end

    it 'creates a new Lexoffice::EventSubscription with the shop token' do
      described_class.new.perform(shop.id)
      expect(Lexoffice::EventSubscription).to have_received(:new).with(shop.lexoffice_token)
    end

    it 'calls each hook on the event subscription endpoint' do
      described_class.new.perform(shop.id)
      hooks.each do |hook|
        expect(event_subscription_endpoint).to have_received(hook)
      end
    end

    context 'when a RestClient::Conflict error is raised' do
      before do
        allow(event_subscription_endpoint).to receive(hooks.first).and_raise(RestClient::Conflict)
        allow(Rails.logger).to receive(:debug)
      end

      it 'logs the error' do
        described_class.new.perform(shop.id)
        expect(Rails.logger).to have_received(:debug)
      end
    end
  end
end
