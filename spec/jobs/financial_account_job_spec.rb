# frozen_string_literal: true

require "rails_helper"

RSpec.describe FinancialAccountJob, type: :job do
  let(:shop) { create(:shop, :with_transaction_setting) }
  let(:transaction_setting) { shop.transaction_setting }
  let(:lexoffice_token) { "token123" }
  let(:account_types) { %w[amazon apple_pay] }

  before do
    allow(Shop).to receive(:includes).with(:transaction_setting).and_return(Shop)
    allow(Shop).to receive(:find).with(shop.id).and_return(shop)
    allow(shop).to receive(:lexoffice_token?).and_return(true)
    allow(shop).to receive(:lexoffice_token).and_return(lexoffice_token)
    allow(shop).to receive(:shopify_domain).and_return("test.myshopify.com")
    allow(shop).to receive(:transaction_setting).and_return(transaction_setting)
    allow(transaction_setting).to receive(:extra_accounts_info).and_return({})
    stub_const("FinancialAccountJob::ALL_TYPES", account_types)
    allow(transaction_setting).to receive(:send).and_call_original
    allow(transaction_setting).to receive(:send).with("enable_amazon").and_return(true)
    allow(transaction_setting).to receive(:send).with("enable_apple_pay").and_return(false)
    allow(transaction_setting).to receive(:send).with("enable_credit_card").and_return(false)
    allow(transaction_setting).to receive(:send).with("enable_google_pay").and_return(false)
    allow(transaction_setting).to receive(:send).with("enable_klarna").and_return(false)
    allow(transaction_setting).to receive(:send).with("enable_samsung_pay").and_return(false)
    allow(transaction_setting).to receive(:send).with("enable_shopify_pay").and_return(false)
    allow(transaction_setting).to receive(:send).with("enable_klarna_pay_later").and_return(false)
    allow(transaction_setting).to receive(:send).with("enable_sofort").and_return(false)
    allow(transaction_setting).to receive(:send).with("enable_other").and_return(false)
    allow_any_instance_of(FinancialAccountJob).to receive(:set_error_context)
  end

  describe "#perform" do
    it "calls FindOrEnsureFinancialAccountService with correct arguments when lexoffice_token is present" do
      expect(FindOrEnsureFinancialAccountService).to receive(:call).with(
        transaction_setting, lexoffice_token, array_including("amazon")
      )
      described_class.new.perform(shop.id)
    end

    it "does not call service if lexoffice_token is missing" do
      allow(shop).to receive(:lexoffice_token?).and_return(false)
      expect(FindOrEnsureFinancialAccountService).not_to receive(:call)
      described_class.new.perform(shop.id)
    end

    it "sets error context with correct tags" do
      allow(FindOrEnsureFinancialAccountService).to receive(:call)
      expect_any_instance_of(FinancialAccountJob).to receive(:set_error_context).with(
        user_id: shop.shopify_domain, tags: "lexoffice_financial_account_job"
      )
      described_class.new.perform(shop.id)
    end

    it "reports error to Rails.error if StandardError is raised" do
      allow(FindOrEnsureFinancialAccountService).to receive(:call).and_raise(StandardError.new("fail"))
      expect(Rails.error).to receive(:report).with(instance_of(StandardError))
      described_class.new.perform(shop.id)
    end

    it "re-raises Redlock::LockError and does not report to Rails.error" do
      redlock_error = Class.new(StandardError) { def self.to_s = "Redlock::LockError" }.new
      allow(FindOrEnsureFinancialAccountService).to receive(:call).and_raise(redlock_error)
      expect { described_class.new.perform(shop.id) }.to raise_error(redlock_error.class)
    end
  end

  describe "#account_types" do
    it "returns enabled account types and extra accounts" do
      job = described_class.new
      allow(transaction_setting).to receive(:send).with("enable_amazon").and_return(true)
      allow(transaction_setting).to receive(:send).with("enable_apple_pay").and_return(true)
      allow(transaction_setting).to receive(:extra_accounts_info).and_return({ "enable_custom" => true })
      result = job.send(:account_types, transaction_setting)
      expect(result).to include("amazon", "apple_pay", "custom")
    end

    it "returns only enabled account types if no extra accounts" do
      job = described_class.new
      allow(transaction_setting).to receive(:send).with("enable_amazon").and_return(true)
      allow(transaction_setting).to receive(:send).with("enable_apple_pay").and_return(false)
      allow(transaction_setting).to receive(:extra_accounts_info).and_return({})
      result = job.send(:account_types, transaction_setting)
      expect(result).to eq(["amazon"])
    end
  end
end
