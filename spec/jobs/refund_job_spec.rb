# frozen_string_literal: true

# spec/jobs/refund_job_spec.rb
require 'rails_helper'

RSpec.describe RefundJob, type: :job do
  let(:shop) { create(:shop) }
  let(:order) { build(:order, :with_refund) }
  let(:refund) { order.refunds.first }
  let(:error) { StandardError.new('Pending transaction error') }
  let(:shop_id) { shop.id }
  let(:order_id) { order.id }
  let(:refund_id) { refund.id }
  let(:order_name) { order.name }

  before do
    WebMock.stub_request(:any, /appsreporting\.eu\.ngrok\.io/).to_return(status: 200, body: '', headers: {})
    allow(SyncInfo).to receive(:find_or_create_by!).and_call_original
    allow(Honeybadger).to receive(:notify)
    allow(ErrorLog).to receive(:handleException)
  end

  describe '#handle_pending_transaction' do
    it 'creates or finds a SyncInfo record' do
      expect do
        subject.send(:handle_pending_transaction, error, shop_id, refund, order_id, order_name)
      end.to change { SyncInfo.count }.by(1)

      sync_info = SyncInfo.last
      expect(sync_info.shop_id.to_i).to eq(shop_id)
      expect(sync_info.shopify_id.to_i).to eq(refund_id)
      expect(sync_info.shopify_order_id.to_i).to eq(order_id)
      expect(sync_info.last_action).to eq('Pending Transaction')
      expect(sync_info.target_type).to eq('Refund')
      expect(sync_info.shopify_order_name).to eq(order_name)
      expect(sync_info.extra_infos).to eq({ voucher_title: 'Pending Transaction', status: 'pending' })
    end

    it 'notifies Honeybadger' do
      subject.send(:handle_pending_transaction, error, shop_id, refund, order_id, order_name)
    end

    it 'handles the exception with ErrorLog' do
      subject.send(:handle_pending_transaction, error, shop_id, refund, order_id, order_name)
      expect(ErrorLog).to have_received(:handleException).with(error, shop_id, refund, order_id)
    end
  end
end
