# frozen_string_literal: true

require 'rails_helper'

Sidekiq::Testing.server_middleware do |chain|
  chain.add SidekiqMiddleware
end

RSpec.describe SendMailJob, type: :job do
  include ActiveJob::TestHelper

  let(:shop_setting) { FactoryBot.create(:shop_setting, send_invoice_mail: true) }
  before do
    allow_any_instance_of(SyncRule).to receive(:handle_webhooks).and_return(nil)
    allow(Redis).to receive(:new).and_return(MockRedis.new)
    allow(SyncInfo).to receive(:find).with(sync_info.id).and_return(sync_info)
    allow(ShopifyAPI::Order).to receive(:find).and_return(build(:order))
  end

  let(:sync_info) do
    FactoryBot.create(:sync_info, shop: FactoryBot.create(:shop, :with_lexoffice, :with_shop_setting, shop_setting:),
                                  invoice_mail_sent: false)
  end
  let!(:shop) { sync_info.shop }

  it 'enqueues job in the doctasks queue' do
    expect(SendMailJob).to be_processed_in(:doctasks)
  end

  it 'has retry set to 10 times' do
    expect(SendMailJob.sidekiq_options_hash['retry']).to eq(10)
  end

  it 'should raise error if retry_count is lower than 10' do
    allow_any_instance_of(Lexoffice::MailSenderService).to receive(:call).and_raise(RestClient::TooManyRequests)
    sync_info.shop.shop_setting.update(send_invoice_mail: true)
    expect do
      SendMailJob.new.perform(sync_info.id)
    end.to raise_error(RestClient::TooManyRequests)
  end

  it 'should log and send error to Honeybadger when retry_count is equal to or bigger than 10' do
    allow_any_instance_of(Lexoffice::MailSenderService).to receive(:call).and_raise(RestClient::TooManyRequests)
    Honeybadger::Backend::Test.notifications.clear

    sync_info.shop.shop_setting.update(send_invoice_mail: true)

    expect do
      # Set retry count to 10

      Sidekiq::Testing.inline! do
        job = SendMailJob.new
        job.retry_count = 10
        job.perform(sync_info.id)
      end

      # Important: `Honeybadger.flush` ensures that asynchronous notifications
      # are delivered before the test's remaining expectations are verified.
      Honeybadger.flush
    end.to change(Honeybadger::Backend::Test.notifications[:notices], :size).by(1)

    # should have only one error
    expect(Honeybadger::Backend::Test.notifications[:notices].size).to eq(1)

    exception = 'RestClient::TooManyRequests: Too Many Requests'
    expect(Honeybadger::Backend::Test.notifications[:notices].first.error_message).to eq(exception)
  end
end
