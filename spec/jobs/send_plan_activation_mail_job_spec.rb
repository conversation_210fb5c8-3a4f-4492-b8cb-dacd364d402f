# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SendPlanActivationMailJob, type: :job do
  describe '#perform' do
    let(:shop) { create(:shop) }

    before do
      allow(Shop).to receive(:find).and_return(shop)
    end

    context 'when shop plan is not active' do
      before do
        allow(shop).to receive(:plan_active?).and_return(false)
      end

      it 'enqueues a notification job with correct parameters' do
        expect(NotificationsJob).to receive(:perform_async)

        described_class.new.perform(shop.id)
      end
    end

    context 'when shop plan is already active' do
      before do
        allow(shop).to receive(:plan_active?).and_return(true)
      end

      it 'does not enqueue a notification job' do
        expect(NotificationsJob).not_to receive(:perform_async)

        described_class.new.perform(shop.id)
      end
    end
  end
end
