# frozen_string_literal: true

# spec/jobs/send_plan_mismatch_notification_job_spec.rb

require 'rails_helper'

RSpec.describe SendPlanMismatchNotificationJob, type: :job do
  let(:shop) { create(:shop, plan_mismatch_since: 3.days.ago) }

  describe '#perform' do
    before do
      allow(Shop).to receive(:find).with(shop.id).and_return(shop)
    end

    context 'when shop has a plan mismatch' do
      before do
        allow(shop).to receive(:plan_mismatch_since?).and_return(true)
      end

      it 'enqueues NotificationsJob' do
        allow(Rails.env).to receive(:production?).and_return(true)
        expect(NotificationsJob).to receive(:perform_async)

        described_class.new.perform(shop.id)
      end
    end

    context 'when shop does not have a plan mismatch' do
      before do
        allow(shop).to receive(:plan_mismatch_since?).and_return(false)
      end

      it 'does not enqueue NotificationsJob' do
        expect(NotificationsJob).not_to receive(:perform_async)

        described_class.new.perform(shop.id)
      end
    end
  end
end
