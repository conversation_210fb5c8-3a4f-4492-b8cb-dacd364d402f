# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SyncOrderJob do
  let(:shop) { create(:shop, :with_shop_setting) }

  before do
    WebMock.stub_request(:any, /appsreporting\.eu\.ngrok\.io/).to_return(status: 200, body: '', headers: {})
    Sidekiq::Testing.inline!
    allow_any_instance_of(SyncHelper::SyncHelper).to receive(:sync_entity).and_return(true)
    allow_any_instance_of(SyncRule).to receive(:handle_webhooks).and_return(true)
    allow_any_instance_of(SyncRule).to receive(:delete_webhooks).and_return(true)
    allow_any_instance_of(ShopSetting).to receive(:handle_rules).and_return(true)
    allow_any_instance_of(Shop).to receive(:billing_plan).and_return(create(:billing_plan))
    allow(ShopifyAPI::Order).to receive(:find).and_return(build(:order))
    SyncRule.delete_all
  end

  context 'when called from import' do
    before do
      shop.shop_setting.update(excludePOS: false, invoice_timing: 'orders/create')
      create(:sync_rule,
             shop:,
             live_sync: true,
             shopify_entity_type: 'Order',
             target_type: 'Invoice',
             webhooks: 'orders/create')
    end

    it 'should call sync_entity with action orders/create' do
      expect_any_instance_of(SyncHelper::SyncHelper).to receive(:sync_entity)
      SyncOrderJob.perform_async('orders/create', '1234567489', shop.id, true)
    end

    it 'should call sync_entity with action orders/fulfilled' do
      expect_any_instance_of(SyncHelper::SyncHelper).to receive(:sync_entity)
      SyncOrderJob.perform_async('orders/fulfilled', '1234567489', shop.id, true)
    end
  end

  context 'when called from live webhook' do
    context 'when shop has invoice_timing set to orders/fulfilled' do
      before do
        shop.shop_setting.update(excludePOS: false, invoice_timing: 'orders/fulfilled')
      end

      context 'and receives orders/create webhook' do
        before do
          create(:sync_rule,
                 shop:,
                 live_sync: true,
                 shopify_entity_type: 'Order',
                 target_type: 'Invoice',
                 webhooks: 'orders/create')
        end

        context 'and the order is not fulfilled' do
          before do
            allow(ShopifyAPI::Order).to receive(:find).and_return(build(:order, fulfillment_status: nil))
          end

          it 'does not call sync_entity' do
            expect_any_instance_of(SyncHelper::SyncHelper).not_to receive(:sync_entity)
            SyncOrderJob.perform_async('orders/create', '1234567489', shop.id)
          end
        end

        context 'and the order is fulfilled' do
          before do
            allow(ShopifyAPI::Order).to receive(:find).and_return(build(:order, fulfillment_status: 'fulfilled'))
          end

          it 'calls sync_entity' do
            expect_any_instance_of(SyncHelper::SyncHelper).to receive(:sync_entity)
            SyncOrderJob.perform_async('orders/create', '1234567489', shop.id)
          end
        end
      end

      context 'and receives orders/fulfilled webhook' do
        before do
          create(:sync_rule,
                 shop:,
                 live_sync: true,
                 shopify_entity_type: 'Order',
                 target_type: 'Invoice',
                 webhooks: 'orders/fulfilled')
        end

        context 'and the order is fulfilled' do
          before do
            allow(ShopifyAPI::Order).to receive(:find).and_return(build(:order, fulfillment_status: 'fulfilled'))
          end

          it 'calls sync_entity' do
            expect_any_instance_of(SyncHelper::SyncHelper).to receive(:sync_entity)
            SyncOrderJob.perform_async('orders/fulfilled', '1234567489', shop.id)
          end
        end
      end
    end

    context 'when shop has invoice_timing set to orders/create' do
      before do
        allow_any_instance_of(SyncHelper::SyncHelper).to receive(:sync_entity).and_return(true)
        shop.shop_setting.update(excludePOS: false, invoice_timing: 'orders/create')
      end

      context 'when shop has an orders/create SyncRule' do
        before do
          create(:sync_rule,
                 shop:,
                 live_sync: true,
                 shopify_entity_type: 'Order',
                 target_type: 'Invoice',
                 webhooks: 'orders/create')
        end

        it 'calls sync_entity with action orders/create' do
          expect_any_instance_of(SyncHelper::SyncHelper).to receive(:sync_entity)
          SyncOrderJob.perform_async('orders/create', '1234567489', shop.id)
        end

        it 'does not call sync_entity with action orders/fulfilled' do
          expect_any_instance_of(SyncHelper::SyncHelper).not_to receive(:sync_entity)
          SyncOrderJob.perform_async('orders/fulfilled', '1234567489', shop.id)
        end
      end

      context 'when shop has an orders/fulfilled SyncRule' do
        before do
          create(:sync_rule,
                 shop:,
                 live_sync: true,
                 shopify_entity_type: 'Order',
                 target_type: 'Invoice',
                 webhooks: 'orders/fulfilled')
        end

        it 'does not call sync_entity with action orders/create' do
          expect_any_instance_of(SyncHelper::SyncHelper).not_to receive(:sync_entity)
          SyncOrderJob.perform_async('orders/create', '1234567489', shop.id)
        end

        it 'calls sync_entity with action orders/fulfilled' do
          expect_any_instance_of(SyncHelper::SyncHelper).to receive(:sync_entity)
          SyncOrderJob.perform_async('orders/fulfilled', '1234567489', shop.id)
        end
      end
    end

    context 'when shop has an order SyncRule with excludePOS and a POS order comes in' do
      before do
        create(:sync_rule,
               shop_id: shop.id,
               live_sync: true,
               shopify_entity_type: 'Order',
               target_type: 'Invoice',
               webhooks: 'orders/create')

        allow(ShopifyAPI::Order).to receive(:find).and_return(build(:order,
                                                                    source_name: 'pos',
                                                                    fulfillment_status: 'fulfilled'))
      end

      it 'does not call sync_entity when excludePOS is true' do
        shop.shop_setting.update(excludePOS: true)
        expect_any_instance_of(SyncHelper::SyncHelper).not_to receive(:sync_entity)
        SyncOrderJob.perform_async('orders/create', '1234567489', shop.id)
      end

      it 'calls sync_entity when excludePOS is false' do
        shop.shop_setting.update(excludePOS: false)
        expect_any_instance_of(SyncHelper::SyncHelper).to receive(:sync_entity).at_least(:once)
        SyncOrderJob.perform_async('orders/create', '1234567489', shop.id)
      end
    end
  end

  context 'error handling' do
    before do
      allow(shop).to receive(:connected_to_lexoffice?).and_return(true)
    end

    context 'when a ShopifyAPI::Errors::HttpResponseError with code 429 occurs' do
      let(:http_response) { ShopifyAPI::Clients::HttpResponse.new(code: 429, body: '', headers: {}) }
      let(:shopify_error) { ShopifyAPI::Errors::HttpResponseError.new(response: http_response) }

      before do
        allow(ShopifyAPI::Order).to receive(:find).and_raise(shopify_error)
      end

      it 're-raises the Shopify 429 error' do
        expect do
          SyncOrderJob.new.perform('orders/create', '1234567489', shop.id)
        end.to(raise_error do |error|
          expect(error.class.to_s).to include('ShopifyAPI::Errors::HttpResponseError')
          expect(error.code).to eq(429)
        end)
      end
    end

    context 'when other exceptions occur' do
      let(:other_error) { StandardError.new('Some other error') }

      before do
        allow(ShopifyAPI::Order).to receive(:find).and_raise(other_error)
        allow(ErrorLog).to receive(:handleException)
        allow(Rails.error).to receive(:report)
      end

      it 'handles the error without re-raising' do
        expect do
          SyncOrderJob.new.perform('orders/create', '1234567489', shop.id)
        end.not_to raise_error

        expect(ErrorLog).to have_received(:handleException).with(other_error, shop.id, anything, '1234567489')
        expect(Rails.error).to have_received(:report).with(other_error)
      end
    end
  end

  context 'sidekiq retries exhausted' do
    let(:job_args) { ['orders/create', '1234567489', shop.id, import_job] }
    let(:exception) { StandardError.new('Test exception') }

    before do
      allow(ErrorLog).to receive(:handleException)
    end

    context 'when import_job is true' do
      let(:import_job) { true }

      it 'calls ErrorLog.handleException' do
        SyncOrderJob.sidekiq_retries_exhausted_block.call({ 'args' => job_args }, exception)
        expect(ErrorLog).to have_received(:handleException).with(exception, shop.id, nil, '1234567489')
      end
    end
  end

  context 'when running as an import job' do
    let(:order_id) { '123456789' }
    let(:error_message) { 'Test import error' }

    before do
      shop.shop_setting.update(excludePOS: false, invoice_timing: 'orders/create')
      create(:sync_rule,
             shop:,
             live_sync: true,
             shopify_entity_type: 'Order',
             target_type: 'Invoice',
             webhooks: 'orders/create')

      allow(Shop).to receive(:includes).and_return(Shop)
      allow(Shop).to receive(:find).and_return(shop)
      allow(shop).to receive(:connected_to_lexoffice?).and_return(true)
      allow(shop).to receive(:with_shopify_session).and_raise(StandardError.new(error_message))
    end

    it 'creates an error log and reraises the error' do
      expect(ErrorLog).to receive(:handleException).with(
        instance_of(StandardError),
        shop.id,
        nil,
        order_id
      )

      expect do
        described_class.new.perform('orders/create', order_id, shop.id, true)
      end.to raise_error(StandardError, error_message)
    end
  end
end
