# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DocStatusJob, type: :job do
  let(:job) { described_class.new }
  let(:doc_type) { 'Invoice' }
  let(:doc_id) { SecureRandom.uuid }
  let(:shop) { create(:shop) }
  let!(:sync_info) { create(:sync_info, target_id: doc_id, shop:, target_type: "Invoice") }
  let(:doc_endpoint) { double(Lexoffice::Invoice) }

  describe '#perform' do
    it 'calls #process_status_change' do
      expect(job).to receive(:process_status_change)
      job.perform(doc_type, doc_id)
    end
  end

  describe '#process_status_change' do
    before do
      allow(SyncInfo).to receive(:find_by).and_return(sync_info)
      allow("Lexoffice::#{doc_type}".constantize).to receive(:new).and_return(doc_endpoint)
      allow(sync_info).to receive(:add_doc_properties)
      allow(shop).to receive(:refresh_token_if_expired)
      allow(doc_endpoint).to receive(:find).with(doc_id)
    end

    context 'when sync_info is not found' do
      let(:sync_info) { nil }

      it 'should return nil' do
        expect(job.process_status_change(doc_type, doc_id)).to be_nil
      end
    end

    context 'when shop is not found or lexoffice_token is nil' do
      it 'returns nil if shop is nil' do
        allow(sync_info).to receive(:shop).and_return(nil)
        expect(job.process_status_change(doc_type, doc_id)).to be_nil
      end

      it 'returns nil if lexoffice_token is nil' do
        allow(shop).to receive(:lexoffice_token).and_return(nil)
        expect(job.process_status_change(doc_type, doc_id)).to be_nil
      end
    end

    context 'when RestClient raises a NotFound error' do
      it 'notifies Honeybadger about the error' do
        allow(doc_endpoint).to receive(:find).with(doc_id).and_raise(RestClient::NotFound)
        expect(Honeybadger).to receive(:notify)
        job.process_status_change(doc_type, doc_id)
      end
    end

    context 'when RestClient raises an Unauthorized error' do
      it 'notifies Honeybadger about the error' do
        allow(doc_endpoint).to receive(:find).with(doc_id).and_raise(RestClient::Unauthorized)
        expect(Honeybadger).to receive(:notify)
        job.process_status_change(doc_type, doc_id)
      end
    end

    it 'calls appropriate methods to process and inspect doc changes' do
      expect(shop).to receive(:refresh_token_if_expired)
      expect(doc_endpoint).to receive(:find).with(doc_id)
      expect(sync_info).to receive(:add_doc_properties)
      job.process_status_change(doc_type, doc_id)
    end
  end

  describe "#find_sync_info" do
    it "returns the sync_info with the correct target_id and target_type" do
      expect(job.send(:find_sync_info, doc_type, doc_id)).to eq(sync_info)
    end

    it "returns nil if no sync_info is found" do
      expect(job.send(:find_sync_info, "NonExistentType", doc_id)).to be_nil
    end
  end
end
