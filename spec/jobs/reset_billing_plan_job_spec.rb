# frozen_string_literal: true

# spec/jobs/reset_billing_plan_job_spec.rb

require 'rails_helper'

RSpec.describe ResetBillingPlanJob, type: :job do
  let(:shop) { create(:shop) }

  describe '#perform' do
    before do
      allow(Shop).to receive(:find).with(shop.id).and_return(shop)
    end

    context 'when plan_mismatch_since is set' do
      before do
        allow(shop).to receive(:plan_mismatch_since?).and_return(true)
      end

      it 'calls the ResetBillingPlanService' do
        expect(BillingServices::ResetBillingPlanService).to receive(:call).with(shop:)
        described_class.new.perform(shop.id)
      end
    end

    context 'when shop plan is not mismatched' do
      before do
        allow(shop).to receive(:plan_mismatch_since?).and_return(false)
      end

      it 'does not call the ResetBillingPlanService' do
        expect(BillingServices::ResetBillingPlanService).not_to receive(:call)
        described_class.new.perform(shop.id)
      end
    end
  end
end
