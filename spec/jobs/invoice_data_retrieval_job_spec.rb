# frozen_string_literal: true

require 'rails_helper'

RSpec.describe InvoiceDataRetrievalJob do
  let(:shop) { create(:shop) }
  let(:sync_info) { create(:sync_info, :invoice_created, shop:, target_id: '123') }
  let(:invoice) { create(:lexoffice_invoice) }

  before do
    Sidekiq::Testing.inline!
    allow(shop).to receive(:connected_to_lexoffice?).and_return(true)
    allow(shop).to receive(:plan_active?).and_return(true)
    allow(Shop).to receive(:find).with(shop.id).and_return(shop)
    # stub request for all invoices
    allow(RestClient).to receive(:get).and_return(invoice.to_json)
  end

  describe '#perform' do
    it 'retrieves invoice data for synced documents and updates SyncInfo extra_infos' do
      allow(sync_info).to receive(:doc_created?).and_return(true)
      allow_any_instance_of(Lexoffice::Base).to receive(:find).with(sync_info.target_id).and_return(invoice)
      expect_any_instance_of(SyncInfo).to receive(:update).with(
        extra_infos: {
          voucher_title: invoice[:voucherNumber],
          status: invoice[:voucherStatus],
          total_price: invoice.dig(:totalPrice, :totalGrossAmount),
          document_id: invoice.dig(:files, :documentFileId)
        }
      )

      described_class.perform_async(shop.id)
    end
  end
end
