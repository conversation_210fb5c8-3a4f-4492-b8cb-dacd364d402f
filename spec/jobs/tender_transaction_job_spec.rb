# frozen_string_literal: true

# spec/jobs/tender_transaction_job_spec.rb
require 'rails_helper'
Sidekiq::Testing.fake!

# rubocop:disable Style/ OpenStructUse
RSpec.describe TenderTransactionJob, type: :job do
  let(:shop_setting) { create(:shop_setting, excludePOS: false, enable_tender_transactions: true) }
  let(:shop) { create(:shop, :with_transaction_setting, shop_setting:) }
  let(:transaction_attr) { { 'payment_method' => 'credit_card', 'amount' => '100.0', 'order_id' => '12345' }.to_json }
  let(:shop_id) { shop.id }
  let(:locale) { 'de' }
  let(:order) { build(:order, fulfillment_status: 'fulfilled') }
  let(:log_entity) do
    OpenStruct.new(id: JSON.parse(transaction_attr)['id'], name: nil, shopify_type: 'tender_transaction',
                   retry_params: transaction_attr)
  end

  before do
    allow(Shop).to receive(:includes).and_return(Shop)
    allow(Shop).to receive(:find).and_return(shop)
    allow(shop).to receive(:connected_to_lexoffice?).and_return(true)
    allow(shop).to receive(:lexoffice_token?).and_return(true)
    allow(shop).to receive(:refresh_token_if_expired)
    allow(shop).to receive(:billing_plan).and_return(create(:billing_plan))
    allow(FindOrCreatePendingRefundService).to receive(:call)
    allow(FindAndMapExtraFinancialAccountsService).to receive(:call).and_return('mapped_payment_method')
    allow(FindOrEnsureFinancialAccountService).to receive(:call)
    allow(FindOrCreateFinancialTransactionService).to receive(:call)
    allow(Rails.error).to receive(:set_context)
    allow(Rails.error).to receive(:report)
    allow(ErrorLog).to receive(:handleException)
    allow(ShopifyAPI::Order).to receive(:find).and_return(order)
  end

  subject(:perform_job) do
    described_class.new.perform(transaction_attr, shop_id, false, locale)
  end

  it 'sets the locale' do
    expect(I18n).to receive(:locale=).with(locale).at_least(:once)
    perform_job
  end

  it 'sets the error context' do
    expect(Rails.error).to receive(:set_context)
      .with(hash_including(user_id: shop.shopify_domain, tags: 'tender_transaction'))
    perform_job
  end

  it 'handles exception and logs error without re-raising' do
    allow(shop).to receive(:connected_to_lexoffice?).and_raise(StandardError.new('Test error'))
    expect(Rails.error).to receive(:report).with(instance_of(StandardError))
    expect(ErrorLog).to receive(:handleException).with(instance_of(StandardError), shop_id, instance_of(OpenStruct),
                                                       '12345')
    expect { perform_job }.not_to raise_error
  end

  it 'returns if payment method is paypal' do
    transaction_attr = { 'payment_method' => 'paypal' }.to_json
    expect(described_class.new.perform(transaction_attr, shop_id)).to be_nil
  end

  it 'calls FindOrCreatePendingRefundService for negative amounts' do
    shop.shop_setting.update(enable_tender_transactions: false)
    transaction_attr = { 'payment_method' => 'credit_card', 'amount' => '-100.0', 'order_id' => '12345' }.to_json
    expect(FindOrCreatePendingRefundService).to receive(:call).with('12345', '-100.0', shop)
    described_class.new.perform(transaction_attr, shop_id)
  end

  it 'calls FindAndMapExtraFinancialAccountsService for extra payment methods' do
    shop.shop_setting.update(enable_tender_transactions: true)
    allow(shop).to receive(:connected_to_lexoffice?).and_return(true)
    transaction_attr = { 'payment_method' => 'extra_method', 'amount' => '100.0', 'order_id' => '12345' }.to_json
    expect(FindAndMapExtraFinancialAccountsService).to receive(:call).with(100.0, shop, instance_of(ShopifyAPI::Order))
    described_class.new.perform(transaction_attr, shop_id)
  end

  it 'calls FindOrEnsureFinancialAccountService' do
    shop.shop_setting.update(enable_tender_transactions: true)
    expect(FindOrEnsureFinancialAccountService).to receive(:call).with(shop.transaction_setting.reload,
                                                                       shop.lexoffice_token, ['credit_card'])
    described_class.new.perform(transaction_attr, shop_id)
  end

  it 'calls FindOrCreatePendingRefundService for negative amounts' do
    transaction_attr = { 'payment_method' => 'credit_card', 'amount' => '-100.0', 'order_id' => '12345' }.to_json
    expect(FindOrCreatePendingRefundService).to receive(:call).with('12345', '-100.0', shop)
    described_class.new.perform(transaction_attr, shop_id)
  end

  it 'notifies the error reporter on error' do
    allow(shop).to receive(:connected_to_lexoffice?).and_raise(StandardError.new('Test error'))
    expect(Rails.error).to receive(:report).with(instance_of(StandardError))

    expect { described_class.new.perform(transaction_attr, shop_id) }.not_to raise_error
  end

  it 'handles exception and logs error' do
    allow(shop).to receive(:connected_to_lexoffice?).and_raise(StandardError.new('Test error'))
    expect(ErrorLog).to receive(:handleException).with(instance_of(StandardError), shop_id, instance_of(OpenStruct),
                                                       '12345')

    expect { described_class.new.perform(transaction_attr, shop_id) }.not_to raise_error
  end

  context 'when a StandardError occurs' do
    let(:error) { StandardError.new('Some error') }

    before do
      allow(shop).to receive(:with_shopify_session).and_raise(error)
    end

    it 'notifies the error reporter and calls ErrorLog.handleException' do
      expect(Rails.error).to receive(:report).with(error)
      expect(ErrorLog).to receive(:handleException).with(error, shop_id, instance_of(OpenStruct), '12345')
      expect { perform_job }.not_to raise_error
    end
  end

  context 'when a ShopifyAPI::Errors::HttpResponseError with code 429 occurs' do
    let(:http_response) { ShopifyAPI::Clients::HttpResponse.new(code: 429, body: '', headers: {}) }
    let(:shopify_error) { ShopifyAPI::Errors::HttpResponseError.new(response: http_response) }

    before do
      allow(ShopifyAPI::Order).to receive(:find).and_raise(shopify_error)
    end

    it 'raises the exception to trigger a retry' do
      expect { described_class.new.perform(transaction_attr, shop_id) }
        .to raise_error(ShopifyAPI::Errors::HttpResponseError) do |error|
        expect(error.code).to eq(429)
        expect(error.class.to_s).to include('ShopifyAPI::Errors::HttpResponseError')
      end
    end
  end

  context 'when sidekiq retries are exhausted' do
    let(:job) { { 'args' => [transaction_attr, shop_id, false, locale] } }
    let(:http_response) { ShopifyAPI::Clients::HttpResponse.new(code: 429, body: '', headers: {}) }
    let(:shopify_error) { ShopifyAPI::Errors::HttpResponseError.new(response: http_response) }

    it 'notifies the error reporter and calls ErrorLog.handleException' do
      expect(Rails.error).to receive(:report).with(
        shopify_error,
        context: hash_including(
          user_id: shop.id,
          transaction_id: JSON.parse(transaction_attr)['id'],
          order_id: JSON.parse(transaction_attr)['order_id'],
          tags: 'too_many_requests_tender_transactions'
        )
      )
      expect(ErrorLog).to receive(:handleException).with(shopify_error, shop.id, log_entity,
                                                         JSON.parse(transaction_attr)['order_id'])
      described_class.sidekiq_retries_exhausted_block.call(job, shopify_error)
    end

    it 'calls ErrorLog.handleException in sidekiq_retries_exhausted block' do
      described_class.sidekiq_retries_exhausted_block.call(job, shopify_error)
      expect(ErrorLog).to have_received(:handleException).with(shopify_error, shop.id, log_entity,
                                                               JSON.parse(transaction_attr)['order_id'])
    end
  end

  context 'when running as an import job' do
    let(:shop) { create(:shop) }
    let(:transaction_id) { '123456789' }
    let(:order_id) { '987654321' }
    let(:error_message) { 'Test import error' }
    let(:transaction_attr) do
      {
        id: transaction_id,
        order_id: order_id,
        amount: '10.00',
        payment_method: 'credit_card'
      }.to_json
    end

    before do
      allow(Shop).to receive(:find).and_return(shop)
      allow(shop).to receive(:connected_to_lexoffice?).and_raise(StandardError.new(error_message))
    end

    it 'creates an error log and reraises the error' do
      expect(ErrorLog).to receive(:handleException).with(
        instance_of(StandardError),
        shop.id,
        instance_of(OpenStruct),
        order_id
      )

      expect {
        described_class.new.perform(transaction_attr, shop.id, true)
      }.to raise_error(StandardError, error_message)
    end
  end
  # rubocop:enable Style/ OpenStructUse
end
