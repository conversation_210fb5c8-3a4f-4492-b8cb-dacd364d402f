# frozen_string_literal: true

def stub_token_refresh
  stub_request(:post, "#{ENV.fetch('LEXOFFICE_SITE', nil)}/oauth2/token").to_return(status: 200, body: {
    access_token: SecureRandom.hex(15),
    expires_in: 1.hour.to_i
  }.to_json)
end

def stub_get_credit_note(credit_note)
  stub_request(:get, /credit-notes/).to_return(status: 200, body: credit_note.to_json)
end

def stub_get_finance_account(id)
  stub_request(:get, "#{ENV.fetch('LEXOFFICE_API', nil)}/v1/finance/accounts/#{id}").to_return(status: 200, body: {
    'financialAccountId' => '5fd91df8-b6ef-4807-a630-ede718'
  }.to_json)
end
