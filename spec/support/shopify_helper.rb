# frozen_string_literal: true

def stub_shopify_requests(shop_name)
  stub_request(:delete, /webhooks.*json/).to_return(status: 200)
  stub_request(:get, /webhooks.json/)
    .to_return(status: 200, body: load_webhooks(shop_name), headers: {})
  stub_request(:post, /webhooks.json/)
    .to_return(status: 200, body: load_webhooks("#{shop_name}_post"), headers: {})

  stub_request(:get, /shop.json/)
    .to_return(status: 200, body: build(:shop).to_json, headers: {})

  stub_request(:get, /transactions.json/)
    .to_return(status: 200, body: '{}', headers: {})

  stub_request(:any, /mandrillapp.com/)
    .to_return(status: 200, body: '{}', headers: {})

  stub_request(:any, /orders/)
    .to_return(status: 200, body: '{}', headers: {})

  stub_request(:get, /Refund.json/)
    .to_return(status: 200, body: '{}', headers: {})

  stub_request(:get, %r{orders/count.json})
    .to_return(status: 200, body: '{"count":269}', headers: {})
end

def stub_print_layouts
  stub_request(:get, /print-layouts/)
    .to_return(status: 200, body: '{}', headers: {})
end

def mock_shopify_requests
  allow_any_instance_of(SyncRule).to receive(:handle_webhooks).and_return(true)
  allow_any_instance_of(SyncRule).to receive(:delete_webhooks).and_return(true)
  stub_shopify_shop_current
end

def register_shopify_factories
  [
    ShopifyAPI::RecurringApplicationCharge,
    ShopifyAPI::Currency
    # Add other ShopifyAPI classes here...
  ].each do |klass|
    factory_object = FactoryBot.build(klass.name.demodulize.underscore.to_sym)
    allow(klass).to receive(:new).and_return(factory_object)
    allow(klass).to receive(:find).and_return(factory_object) if klass.respond_to?(:find)
    allow_any_instance_of(klass).to receive(:save!).and_return(factory_object)
  end
end

RSpec.configure do |config|
  config.before(:each) do
    # stub needed to register the factories, can be overridden per spec for a specific shop
    stub_shopify_authenticated
    register_shopify_factories
  end
end
