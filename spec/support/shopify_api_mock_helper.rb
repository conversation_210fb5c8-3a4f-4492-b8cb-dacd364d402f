# frozen_string_literal: true

module ShopifyApiMockHelper
  # rubocop:disable Metrics/MethodLength
  def mock_shopify_shop_api(attributes = {})
    default_attributes = {
      email: "#{SecureRandom.hex(4)}@example.com",
      name: 'Test Shop',
      country: 'DE',
      shop_owner: '<PERSON>',
      plan_name: 'enterprise',
      plan_display_name: 'Shopify Plus',
      customer_email: nil,
      money_format: '${{amount}}',
      money_with_currency_format: '${{amount}} USD',
      currency: 'USD',
      timezone: '(GMT-05:00) Eastern Time (US & Canada)',
      iana_timezone: 'America/New_York',
      domain: nil,
      province: nil,
      address1: nil,
      zip: nil,
      city: nil,
      source: nil,
      phone: nil,
      latitude: nil,
      longitude: nil,
      primary_locale: 'en',
      address2: nil,
      created_at: nil,
      updated_at: nil,
      country_code: 'US',
      country_name: 'United States',
      weight_unit: 'kg',
      province_code: nil,
      taxes_included: nil,
      tax_shipping: nil,
      county_taxes: nil,
      eligible_for_payments: true,
      requires_extra_payments_agreement: false,
      password_enabled: nil,
      has_storefront: true,
      finances: true,
      primary_location_id: nil,
      checkout_api_supported: true,
      multi_location_enabled: true,
      setup_required: false,
      pre_launch_enabled: false,
      enabled_presentment_currencies: ['USD'],
      transactional_sms_disabled: false
    }

    mock = double('ShopifyAPI::Shop', default_attributes.merge(attributes))
    mock.as_null_object
  end
  # rubocop:enable Metrics/MethodLength

  def stub_shopify_shop_current(attributes = {})
    mock_shop = mock_shopify_shop_api(attributes)
    allow(ShopifyAPI::Shop).to receive(:current).and_return(mock_shop)
    mock_shop
  end
end

RSpec.configure do |config|
  config.include ShopifyApiMockHelper
end
