# frozen_string_literal: true

# shopify login helper methods are heavily inspired by KIRILL PLATONOV
# see https://kirillplatonov.com/posts/testing-shopify-apps-in-rails/
def login(shop)
  clear_login

  OmniAuth.config.test_mode = true
  OmniAuth.config.add_mock(:shopify,
    provider: "shopify",
    uid: shop.shopify_domain,
    credentials: { token: shop.shopify_token })

  Rails.application.env_config["omniauth.auth"] = OmniAuth.config.mock_auth[:shopify]
  Rails.application.env_config["omniauth.params"] = { shop: shop.shopify_domain }
  Rails.application.env_config["jwt.shopify_domain"] = shop.shopify_domain

  post "/auth/shopify"
  follow_redirect!
end

def clear_login
  Rails.application.env_config.delete("omniauth.auth")
  Rails.application.env_config.delete("omniauth.params")
  Rails.application.env_config.delete("jwt.shopify_domain")
end

def stub_shopify_authenticated(shop = nil)
  shopify_domain = shop&.shopify_domain || "test.myshopify.com"
  ShopifyAPI::Context.deactivate_session
  ShopifyAPI::Auth::Session.new(id: "1", shop: shopify_domain).tap do |session|
    # allow(Shop).to receive(:find_by).and_return(nil)
    # allow(Shop).to receive(:find_by).with(hash_including(shopify_domain: shop.shopify_domain)).and_return(shop)
    allow(ShopifyApp::SessionRepository).to receive(:load_session).and_return(session)
    allow(ShopifyAPI::Utils::SessionUtils).to receive(:current_session_id).and_return(session.id)
    ShopifyAPI::Context.activate_session(session)
  end
end

def prevent_shopify_requests(shop = nil)
  allow_any_instance_of(SyncRule).to receive(:handle_webhooks).and_return(nil)
  # allow(HandleRulesService).to receive(:call).and_return(nil)
  # allow_any_instance_of(Shop).to receive(:update_shop_info).and_return(nil)
  allow(ShopifyAPI::Shop).to receive(:current).and_return(double(name: "Test",
    country: "Test Country",
    shop_owner: "Test Owner",
    email: "<EMAIL>"))
  allow(Shop).to receive(:find_or_create_new).and_return(shop) if shop.present?
  allow(ShopifyAPI::Webhook).to receive(:all).and_return(nil)

  return if shop.blank?

  allow_any_instance_of(ShopifyApp::EnsureInstalled).to receive(:installed_shop_session)
    .and_return(double({ url: shop.shopify_domain,
                         site: "spec",
                         token: SecureRandom.hex(16),
                         api_version: "2024-10" }))
end

RSpec.configure do |config|
  config.before(:each) do
    prevent_shopify_requests
  end
end
