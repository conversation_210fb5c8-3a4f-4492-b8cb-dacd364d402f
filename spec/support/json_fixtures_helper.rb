# frozen_string_literal: true

def load_json_fixture(name)
  file = File.open("spec/fixtures/json/#{name}.json")
  json = file.read
  file.close
  json
end

def load_transaction(name)
  load_json_fixture("transactions/#{name}")
end

def load_tender_transaction(name)
  load_json_fixture("tender_transactions/#{name}")
end

def load_webhooks(webhook_name)
  load_json_fixture("webhooks/#{webhook_name}")
end
