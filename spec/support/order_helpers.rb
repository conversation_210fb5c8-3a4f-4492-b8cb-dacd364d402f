# frozen_string_literal: true

module OrderHelpers
  def build_discounted_order
    build(
      :order,
      :with_shipping_lines,
      :with_discounted_line_items,
      :with_discount_applications,
      total_discounts: '25.00',
      discount_amounts: [15.0, 20.0, 5.0],
      discount_applications_data: [
        {
          type: 'discount_code',
          value: '15.0',
          value_type: 'fixed_amount',
          target_type: 'line_item',
          target_selection: 'all',
          allocation_method: 'each',
          code: 'SUMMERSALE'
        }
      ]
    )
  end
end

RSpec.configure do |config|
  config.include OrderHelpers
end
