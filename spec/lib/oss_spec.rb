# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Oss do
  let(:shop_settings) { shop.shop_setting }
  let(:order) do
    build(:order, :with_line_items, :with_shipping_address, :with_billing_address, :with_customer)
  end
  let(:shop) { FactoryBot.create(:shop, :with_shop_setting, distance_sales_principle: 'destination', country: 'DE') }
  let(:order_before_oss) do
    build(:order, :with_customer, :with_billing_address, :with_line_items, :with_shipping_address,
          processed_at: '2021-06-20T05:26:14-04:00')
  end
  let(:contact) { JSON.parse(load_json_fixture('lexoffice_contacts/oss_contact')) }
  let(:oss_check) { Oss.new(order, shop, contact, shop_settings) }

  before do
    # stub_token_refresh
    stub_shopify_authenticated(shop)
    allow(ShopifyAPI::Order).to receive(:find).and_return(order)
    allow(ShopifyAPI::Customer).to receive(:find).and_return(nil)
    allow_any_instance_of(Shop).to receive(:refresh_token_if_expired).and_return(true)
    order.shipping_address['country_code'] = 'IT'
    order.billing_address['country_code'] = 'AT'
  end

  describe '#is_oss_relevant?' do
    it 'should return true' do
      expect(oss_check.is_oss_relevant?).to be_truthy
    end
  end

  describe '#has_shop_lexoffice_dsp_set?' do
    before do
      shop.lexoffice_small_business = true
      shop.distance_sales_principle = 'not defined'
    end

    it 'should not be relevant' do
      expect(oss_check.is_oss_relevant?).to be_falsey
    end

    it 'should not have shop lexoffice dsp set' do
      expect(oss_check.lexoffice_dsp_set?).to be_falsey
    end
  end

  describe '#is_destination_country_oss_relevant?' do
    before do
      shop.country = 'DE'
    end

    context 'shipping_address.country_code is DE' do
      before { order.shipping_address['country_code'] = 'DE' }

      it 'should return false' do
        expect(oss_check.destination_country_oss_relevant?).to be_falsey
      end
    end

    context 'shipping_address.country_code is IT' do
      before { order.shipping_address['country_code'] = 'IT' }

      it 'should return true' do
        expect(oss_check.destination_country_oss_relevant?).to be_truthy
      end
    end

    context 'shipping_address country is NIR' do
      before { order.shipping_address['province_code'] = 'NIR' }

      it 'should return true' do
        expect(oss_check.destination_country_oss_relevant?).to be_truthy
      end
    end

    context 'shipping_address.country_code is CH' do
      before { order.shipping_address['country_code'] = 'CH' }

      it 'should return false' do
        expect(oss_check.destination_country_oss_relevant?).to be_falsey
      end
    end

    context 'shipping_address.country_code is US' do
      before { order.shipping_address['country_code'] = 'US' }

      it 'should return false' do
        expect(oss_check.destination_country_oss_relevant?).to be_falsey
      end
    end

    context 'shop country is not set' do
      before { shop.country = nil }

      it 'should return false' do
        expect(oss_check.destination_country_oss_relevant?).to be_falsey
      end
    end
  end

  describe '#is_customer_tax_exempt?' do
    it 'should return false' do
      expect(oss_check.customer_tax_exempt?).to be_falsey
    end
  end

  describe '#is_made_after_oss?' do
    it 'should return true' do
      expect(oss_check.made_after_oss?).to be_truthy
    end
  end

  describe '#has_at_least_one_delivery?' do
    it 'should return true' do
      expect(order.at_least_one_delivery?).to be_truthy
    end
  end

  describe '#get_tax_sub_type' do
    context 'distanceSales' do
      before do
        shop.distance_sales_principle = 'origin'
      end

      let(:result) { oss_check.get_tax_sub_type }

      it 'should be equal to distanceSales' do
        expect(result).to eq 'distanceSales'
      end
    end

    context 'electronicServices' do
      before do
        order.line_items.each do |line_item|
          line_item[:requires_shipping] = false
        end
      end

      let(:result) { oss_check.get_tax_sub_type }

      it 'should be equal to electronicServices' do
        expect(result).to eq 'electronicServices'
      end
    end
  end

  describe '#is_made_after_oss?' do
    it 'should return true' do
      expect(oss_check.made_after_oss?).to be_truthy
    end

    it 'should return false' do
      oss_check = Oss.new(order_before_oss, shop, contact, shop_settings)
      expect(oss_check.made_after_oss?).to be_falsey
    end
  end
  # TODO: Write tests for north ireland checks
end
