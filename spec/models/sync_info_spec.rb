# frozen_string_literal: true

require "rails_helper"

RSpec.describe SyncInfo, type: :model do
  describe "retry_job" do
    let(:shop) { create(:shop, :with_shop_setting) }
    let(:sync_info) { create(:sync_info, shop:, target_type:) }

    context "when target_type is Invoice" do
      let(:target_type) { "Invoice" }

      it "enqueues Sync<PERSON>rderJob with correct arguments" do
        expect(SyncOrderJob).to receive(:perform_async).with(shop.shop_setting.invoice_timing, sync_info.shopify_id,
          sync_info.shop_id)
        sync_info.retry_job
      end
    end

    context "when target_type is Refund" do
      let(:target_type) { "Refund" }

      it "enqueues RefundJob with correct arguments if shopify_order_id is present" do
        sync_info.update(shopify_order_id: SecureRandom.random_number(9_223_372_036_854_775))
        expect(RefundJob).to receive(:perform_async).with("refunds/create", sync_info.shopify_id,
          sync_info.shopify_order_id,
          sync_info.shop_id)
        sync_info.retry_job
      end

      it "does not enqueue RefundJob if shopify_order_id is not present" do
        sync_info.update(shopify_order_id: nil)
        expect(RefundJob).not_to receive(:perform_async)
        sync_info.retry_job
      end
    end

    context "when target_type is Transaction" do
      let(:target_type) { "Transaction" }
      let(:transaction) do
        double("transaction", paymentMethod: "Credit Card", amount: 100, remoteReference: "ref123",
          processedAt: Time.zone.now)
      end

      before do
        allow(Shopify::SingleTenderTransactionService).to receive(:call).and_return(transaction)
      end

      it "enqueues TenderTransactionJob with correct arguments" do
        transaction_args = {
          order_id: sync_info.shopify_order_id,
          payment_method: transaction.paymentMethod,
          amount: transaction.amount,
          id: sync_info.shopify_id,
          remote_reference: transaction.remoteReference,
          processed_at: transaction.processedAt
        }
        expect(TenderTransactionJob).to receive(:perform_async).with(transaction_args.to_json, sync_info.shop_id)
        sync_info.retry_job
      end
    end

    context "when target_type is not Invoice, Refund, or Transaction" do
      let(:target_type) { "Other" }

      it "does not enqueue any job" do
        expect(SyncOrderJob).not_to receive(:perform_async)
        expect(RefundJob).not_to receive(:perform_async)
        expect(TenderTransactionJob).not_to receive(:perform_async)
        sync_info.retry_job
      end
    end
  end
end
