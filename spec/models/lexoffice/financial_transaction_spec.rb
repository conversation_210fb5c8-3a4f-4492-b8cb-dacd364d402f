# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Lexoffice::FinancialTransaction, type: :model do
  describe 'initialize' do
    let(:auth_token) { SecureRandom.hex(15) }
    let(:transaction_setting) { create(:transaction_setting) }
    let(:shop) { transaction_setting.shop }
    # TODO: use order factory when available
    let(:order) { build(:order, :with_billing_address) }
    let(:tender_transaction) { JSON.parse(load_tender_transaction('credit_card_tender_transaction')) }
    let(:financial_transaction) do
      Lexoffice::FinancialTransaction.new(auth_token,
                                          transaction_setting,
                                          tender_transaction,
                                          order)
    end
    let(:pos_order) { build(:order, email: nil) }
    let(:financial_transaction_pos) do
      Lexoffice::FinancialTransaction.new(auth_token,
                                          transaction_setting,
                                          tender_transaction,
                                          pos_order)
    end
    it 'should hide sensitive information when calling to_honeybadger_context' do
      sender = financial_transaction.data.first[:recipientOrSenderName]
      expect(financial_transaction.to_honeybadger_context[:lexoffice_request][:externalReference]).to eq '*****'
      expect(financial_transaction.to_honeybadger_context[:lexoffice_request][:recipientOrSenderEmail]).to eq '*****'
      expect(financial_transaction.to_honeybadger_context[:lexoffice_request][:recipientOrSenderName]).to eq '*****'
      # original object should not be changed
      expect(financial_transaction.data.first[:recipientOrSenderName]).to eq sender
    end

    it 'should set null to optional attributes with pos orders' do
      expect(financial_transaction_pos.data.first[:recipientOrSenderName]).to eq nil
      expect(financial_transaction_pos.data.first[:recipientOrSenderEmail]).to eq nil
    end
  end
end
