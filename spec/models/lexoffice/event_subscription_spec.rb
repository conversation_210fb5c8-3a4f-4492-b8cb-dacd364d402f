# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Lexoffice::EventSubscription do
  subject(:event_subscription) { described_class.new('TESTTOKEN') }

  let(:mock_response) { build(:event_subscription) }

  before do
    stub_request(:post, %r{#{ENV.fetch('LEXOFFICE_API')}/v1/.*})
      .to_return(body: mock_response.to_json, status: 200)
  end

  describe '#register_token_revoked_hook' do
    it 'prepares the data for token revoked event registration' do
      event_subscription.register_token_revoked_hook
      expect(event_subscription.instance_variable_get(:@data)).to eq(
        {
          eventType: 'token.revoked',
          callbackUrl: "#{ENV.fetch('APP_HOME', nil)}lexoffice_token_revoked"
        }
      )
    end
  end

  describe '#register_invoice_status_changed_hook' do
    it 'prepares the data for invoice status changed event registration' do
      event_subscription.register_invoice_status_changed_hook
      expect(event_subscription.instance_variable_get(:@data)).to eq(
        {
          eventType: 'invoice.status.changed',
          callbackUrl: "#{ENV.fetch('APP_HOME', nil)}invoice_status_changed"
        }
      )
    end
  end

  describe '#register_credit_note_status_changed_hook' do
    it 'prepares the data for credit note status changed event registration' do
      event_subscription.register_credit_note_status_changed_hook
      expect(event_subscription.instance_variable_get(:@data)).to eq(
        {
          eventType: 'credit-note.status.changed',
          callbackUrl: "#{ENV.fetch('APP_HOME', nil)}credit_note_status_changed"
        }
      )
    end
  end
end
