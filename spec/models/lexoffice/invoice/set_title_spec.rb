# frozen_string_literal: true

require "rails_helper"

RSpec.describe Lexoffice::Invoice, "#set_title" do
  let(:shop) { create(:shop) }
  let(:invoice) { Lexoffice::Invoice.new(shop.lexoffice_token) }

  before do
    invoice.data = {}
  end

  context "with valid title" do
    it "sets the title after removing expanding characters" do
      title = "Invoice & Order #123"
      expected_title = "Invoice Order #123"

      invoice.set_title(title)
      expect(invoice.data[:title]).to eq(expected_title)
    end

    it "handles titles with special characters" do
      title = "Invoice €50 & Tax"
      expected_title = "Invoice 50 Tax"

      invoice.set_title(title)
      expect(invoice.data[:title]).to eq(expected_title)
    end

    it "handles titles with exactly 25 characters after sanitization" do
      title = "A" * 25

      invoice.set_title(title)
      expect(invoice.data[:title]).to eq(title)
    end

    it "preserves spaces in titles" do
      title = "Short with spaces"
      expected_title = "Short with spaces"

      invoice.set_title(title)
      expect(invoice.data[:title]).to eq(expected_title)
    end
  end

  context "with invalid title" do
    it "raises TitleTooLongError when title exceeds 25 characters after sanitization" do
      title = "A" * 26

      expect do
        invoice.set_title(title)
      end.to raise_error(TitleTooLongError)
    end

    it "includes the sanitized title in the error message" do
      title = "A" * 26

      expect do
        invoice.set_title(title)
      end.to raise_error(TitleTooLongError, /#{title}/)
    end

    it "includes the maximum length in the error message" do
      title = "A" * 26

      expect do
        invoice.set_title(title)
      end.to raise_error(TitleTooLongError, /25/)
    end
  end
end
