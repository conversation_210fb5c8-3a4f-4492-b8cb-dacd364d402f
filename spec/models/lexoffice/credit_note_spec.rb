# frozen_string_literal: true

require "rails_helper"

RSpec.describe Lexoffice::CreditNote, type: :model do
  before do
    stub_shopify_authenticated(shop)
  end
  let(:shop) { create(:shop, :with_shop_setting) }
  let(:shop_settings) { shop.shop_setting }
  let(:order) do
    build(:order, :with_mixed_line_item_taxes_and_no_shipping_line_tax, :with_refund)
  end
  let(:shopify_refund) do
    build(:refund) do |refund|
      refund.refund_line_items = [build(:refund_line_item) do |refund_line_item|
                                    refund_line_item["line_item"]["name"] = Faker::Lorem.characters(number: 300)
                                  end]
      refund.transactions = [build(:transaction, amount: "0.60")]
      refund.order_adjustments = [build(:order_adjustments, kind: "refund_discrepancy")]
    end
  end
  let(:shopify_partial_refund_paypal_and_coupon) do
    order_adjustment = build(:order_adjustments, kind: "refund_discrepancy")

    attributes_for_refund = {
      id: 882_052_038_920,
      transactions: [build(:transaction, amount: "10"), build(:transaction, amount: "25")],
      order_adjustments: [order_adjustment]
    }

    build(:refund, attributes_for_refund)
  end
  let(:shopify_net_refund) { build(:refund) }
  let(:credit_note) { Lexoffice::CreditNote.new(shop.lexoffice_token) }
  let(:free_product_refund) do
    build(:refund) do |refund|
      refund.refund_line_items = [build(:refund_line_item) do |refund_line_item|
        refund_line_item["line_item"] = {
          "name" => Faker::Lorem.characters(number: 300),
          "price" => "19.99",
          "taxable" => true,
          "tax_lines" => [{ "rate" => 0.00, "price" => "0.00" }],
          "discount_allocations" => [{ "amount" => "19.99" }]
        }
      end]
      # Add another line item with the highest tax rate that should be used
      refund.refund_line_items << build(:refund_line_item) do |refund_line_item|
        refund_line_item["line_item"] = {
          "name" => Faker::Lorem.characters(number: 10),
          "price" => "10.00",
          "tax_lines" => [{ "rate" => 0.19, "price" => "1.90" }],
          "discount_allocations" => []
        }
      end
    end
  end

  describe "#add_line_items" do
    before do
      credit_note.data = {
        "lineItems" => [],
        taxConditions: {
          taxType: "gross"
        }
      }
    end

    it "should add line item" do
      credit_note.add_line_items(shopify_refund.refund_line_items, shopify_refund)
      expect(credit_note.data["lineItems"].length).to eq 1
    end

    it "should add line item with limited name (cut to 255 chars)" do
      credit_note.add_line_items(shopify_refund.refund_line_items, shopify_refund)
      expect(credit_note.data["lineItems"].first[:name].length).to eq 255
    end

    it "should tax the highest tax rate when there is a 100% discount and 0.0 tax rate" do
      credit_note.add_line_items(free_product_refund.refund_line_items, free_product_refund)
      highest_tax_rate = free_product_refund.highest_tax_rate
      expect(highest_tax_rate).to eq(BigDecimal("19.00"))
      expect(credit_note.data["lineItems"].first[:unitPrice][:taxRatePercentage]).to eq(19.00)
    end
  end

  describe "#handle_amount_discrepancy" do
    context "when gross" do
      before do
        credit_note.data = {
          voucherDate: Time.parse(shopify_refund.processed_at).iso8601(3),
          address: nil,
          totalPrice: {
            currency: "EUR"
          },
          taxConditions: {
            taxType: "gross"
          }
        }

        credit_note.handle_amount_discrepancy(shopify_refund, true, order)
      end

      it "line items should be greater than 0" do
        expect(credit_note.data["lineItems"].size).to be > 0
        expect(credit_note.data["lineItems"].first[:unitPrice][:taxRatePercentage]).to eq(19)
      end

      it "should be equal to 0.6" do
        expect(credit_note.data["lineItems"].first[:unitPrice]["grossAmount"]).to eq 0.6
      end
    end

    context "when net" do
      before do
        credit_note.tax_rates_counts = {
          7 => 0,
          19 => 2
        }
        credit_note.data = {
          voucherDate: Time.parse(shopify_net_refund.processed_at).iso8601(3),
          address: nil,
          totalPrice: {
            currency: "EUR"
          },
          taxConditions: {
            taxType: "net"
          }
        }
        credit_note.handle_amount_discrepancy(shopify_refund, true, nil)
      end

      it "line items should be 1" do
        expect(credit_note.data["lineItems"].size).to be == 1
      end
    end

    context "when tax free" do
      before do
        credit_note.tax_rates_counts = {}
        credit_note.data = {
          voucherDate: Time.parse(shopify_net_refund.processed_at).iso8601(3),
          address: nil,
          totalPrice: {
            currency: "EUR"
          },
          taxConditions: {
            taxType: "net"
          }
        }
        credit_note.handle_amount_discrepancy(shopify_refund, true, nil)
      end

      it "line items should be 1" do
        expect(credit_note.data["lineItems"].size).to be == 1
        expect(credit_note.data["lineItems"].first[:unitPrice][:taxRatePercentage]).to eq(0)
      end
    end

    context "when gateway partial refund" do
      before do
        credit_note.tax_rates_counts = {
          7 => 0,
          19 => 2
        }
        credit_note.data = {
          voucherDate: Time.parse(shopify_partial_refund_paypal_and_coupon.processed_at).iso8601(3),
          address: nil,
          totalPrice: {
            currency: "EUR"
          },
          taxConditions: {
            taxType: "net"
          }
        }
        credit_note.handle_amount_discrepancy(shopify_partial_refund_paypal_and_coupon, true, nil)
      end

      it "line items should be 0" do
        expect(credit_note.data["lineItems"].size).to eq 1
        expect(credit_note.data["lineItems"].first[:unitPrice]["netAmount"].to_i).to eq 35
      end
    end

    context "when discrepancy taxed" do
      context "same weight" do
        before do
          credit_note.tax_rates_counts = {
            7 => 3,
            19 => 3
          }
          credit_note.data = {
            voucherDate: Time.parse(shopify_net_refund.processed_at).iso8601(3),
            address: nil,
            totalPrice: {
              currency: "EUR"
            },
            taxConditions: {
              taxType: "gross"
            }
          }
          credit_note.handle_amount_discrepancy(order.refunds.first, true, order)
        end
        it "should be equal to 19" do
          expect(credit_note.data["lineItems"].first[:unitPrice][:taxRatePercentage]).to eq 19
        end
      end

      context "different weight" do
        before do
          credit_note.tax_rates_counts = {
            19 => 1,
            7.000000000000001 => 3
          }
          credit_note.data = {
            voucherDate: Time.parse(shopify_net_refund.processed_at).iso8601(3),
            address: nil,
            totalPrice: {
              currency: "EUR"
            },
            taxConditions: {
              taxType: "gross"
            }
          }
          credit_note.handle_amount_discrepancy(order.refunds.first, true, order)
        end
        it "should be equal to 7" do
          expect(credit_note.data["lineItems"].first[:unitPrice][:taxRatePercentage]).to eq 7.0
        end
      end
    end
  end

  describe "#handle_shipping_refund" do
    before do
      credit_note.data = {
        "lineItems" => [],
        taxConditions: {
          taxType: "gross"
        }
      }
    end

    let(:order) { build(:order, :with_shipping_lines, :with_refund) }
    let(:refund) { order.refunds.first }

    it "should add line items" do
      credit_note.handle_shipping_refund(order, refund)
      expect(credit_note.data["lineItems"].length).to be > 0
    end
  end

  describe "#data_from_refund_and_order" do
    context "when building address" do
      let(:order) { build(:order) }
      let(:shop_settings) { build(:shop_setting, use_shipping_address_for_invoices: true) }
      let(:refund) { build(:refund) }

      it "delegates address building to AddressBuilderService" do
        expect(Lexoffice::AddressBuilderService).to receive(:call)
          .with(order, shop_settings.use_shipping_address_for_invoices)

        credit_note.data_from_refund_and_order(refund, order, shop_settings)
      end
    end
  end

  describe "#determine_tax_rate" do
    it "returns valid tax rate for 7%" do
      adjustment = { "tax_amount" => 7.0, "amount" => 100.0 }
      expect(credit_note.determine_tax_rate(adjustment)).to eq(7)
    end

    it "returns valid tax rate for 19%" do
      adjustment = { "tax_amount" => 19.0, "amount" => 100.0 }
      expect(credit_note.determine_tax_rate(adjustment)).to eq(19)
    end

    it "returns 0 for invalid tax rate" do
      adjustment = { "tax_amount" => 5.0, "amount" => 100.0 }
      expect(credit_note.determine_tax_rate(adjustment)).to eq(0)
    end
  end

  describe "#get_gross" do
    it "calculates gross amount correctly" do
      adjustment = { tax_amount: 19.0, amount: 100.0 }
      expect(credit_note.get_gross(adjustment)).to eq(-119.0)
    end
  end

  describe "#set_contact" do
    let(:refund) { build(:refund) }
    let(:order) { build(:order) }
    let(:shop_settings) { build(:shop_setting) }

    before do
      allow(credit_note).to receive(:should_skip_address?).and_return(true)
      credit_note.data_from_refund_and_order(refund, order, shop_settings)
    end

    it "does not raise NoMethodError when setting contact if address is skipped" do
      expect { credit_note.set_contact("contact_id") }.not_to raise_error
      expect(credit_note.instance_variable_get(:@data)[:address][:contactId]).to eq("contact_id")
    end
  end
end
