# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Lexoffice::Contact, type: :model do
  let(:shop) { create(:shop, :with_shop_setting) }
  let(:contact_endpoint) { Lexoffice::Contact.new(shop.lexoffice_token) }

  before do
    OpenSSL::SSL::SSLContext.new :TLSv1
  end

  describe '#find_or_create' do
    let(:id) { SecureRandom.hex(12) }
    let(:contact) do
      order = build(:order, :with_customer, :with_billing_address)
      contact_endpoint.find_or_create(order.customer.attributes.merge(order.billing_address.attributes), 'net')
    end

    before do
      stub_request(:get, "#{ENV.fetch('LEXOFFICE_API')}/v1/contacts?email=<EMAIL>'")
        .to_return(status: 200, body: { id:, totalElements: 2, content: [{ id:, roles: 'customer' }] }.to_json)
      stub_request(:put, "#{ENV.fetch('LEXOFFICE_API')}/v1/contacts/#{id}")
        .to_return(status: 200, body: { id:, roles: 'vendor' }.to_json, headers: {})
      stub_request(:get, "#{ENV.fetch('LEXOFFICE_API')}/v1/contacts/#{id}")
        .to_return(status: 200, body: { content: [{ id:, roles: 'vendor' }] }.to_json, headers: {})
    end

  end

  describe '#find_by_email' do
    let(:result) { contact_endpoint.find_by_email '<EMAIL>' }

    before do
      stub_request(:get, "#{ENV.fetch('LEXOFFICE_API')}/v1/contacts?email=<EMAIL>")
        .to_return(status: 200, body: { totalElements: 3 }.to_json, headers: {})
    end
    it 'result should have more than 0 total elements' do
      expect(result['totalElements']).to be > 0
    end
  end

  describe '#exists?' do
    context 'when contact exist' do
      let(:result) do
        contact_endpoint.exists? '<EMAIL>'
      end
      before do
        stub_request(:get, "#{ENV.fetch('LEXOFFICE_API')}/v1/contacts?email=<EMAIL>")
          .to_return(status: 200, body: { totalElements: 3 }.to_json, headers: {})
      end
      it 'should return true' do
        expect(result).to be_truthy
      end
    end
  end
end
