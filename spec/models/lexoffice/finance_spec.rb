# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Lexoffice::Finance, type: :model do
  describe 'initialize' do
    let(:financial_account) { Lexoffice::Finance.new(SecureRandom.hex(15)) }

    it 'should have type and external reference' do
      expect(financial_account).not_to be_nil
    end
  end

  describe '#find_by_id' do
    let(:financial_account) { Lexoffice::FinancialAccount.new(SecureRandom.hex(15), 'Credit card') }
    let(:transaction_settings) { create(:transaction_setting) }
    let(:id) { transaction_settings.credit_card_account_id }

    before do
      stub_get_finance_account(id)
    end

    context 'when the request is successful' do
      it 'returns the financial account data' do
        result = financial_account.find_by_id(id) # rubocop:disable Rails/DynamicFindBy
        expect(result).to include('financialAccountId' => '5fd91df8-b6ef-4807-a630-ede718')
      end
    end

    xcontext 'when too many requests error is raised' do
      xit 'retries and eventually returns the financial account data' do
        # TODO: Test Retriable logic
      end
    end
  end
end
