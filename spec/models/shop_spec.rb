# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Shop, type: :model do
  describe '#update_shop_info' do
    before do
      allow(ShopifyAPI::Shop).to receive(:current).and_return(double(name: 'Test',
                                                                     country: 'Test Country',
                                                                     shop_owner: 'Test Owner',
                                                                     email: '<EMAIL>'))
    end
    let(:shop) { create(:shop) }

    let(:response) { shop.update_shop_info }

    it 'should activate billing plan' do
      expect { response }.to change(shop.reload, :name).and change(shop.reload, :country)
    end
  end

  describe '#add_or_update_access_scopes' do
    let(:shop) { create(:shop, access_scopes: '') }
    let(:scopes) do
      [
        double(handle: 'read_products'),
        double(handle: 'write_orders')
      ]
    end

    before do
      allow(ShopifyAPI::AccessScope).to receive(:all).and_return(scopes)
    end

    it 'updates access_scopes when they differ from current scopes' do
      expect do
        shop.add_or_update_access_scopes
      end.to change { shop.reload.access_scopes }.to('read_products,write_orders')
    end

    it 'does not update access_scopes when they are the same' do
      shop.update(access_scopes: 'read_products,write_orders')

      expect do
        shop.add_or_update_access_scopes
      end.not_to(change { shop.reload.access_scopes })
    end
  end

  describe '#admin_url' do
    let(:shop) { create(:shop) }

    it 'returns the admin url' do
      expect(shop.admin_url).to eq("https://#{shop.shopify_domain}/admin/apps/#{ENV.fetch('SHOPIFY_CLIENT_API_KEY')}")
    end
  end

  describe '#errors?' do
    let(:shop_with_errors) { create(:shop, :with_errors) }
    let(:shop_without_errors) { create(:shop) }

    it 'returns true if the shop has errors in log' do
      expect(shop_with_errors.errors?).to be_truthy
    end

    it 'returns false if the shop has no errors in log' do
      expect(shop_without_errors.errors?).to be_falsey
    end
  end

  describe '#vat_free?' do
    let(:shop) { create(:shop) }

    context 'when lexoffice_small_business is true' do
      before { shop.update(lexoffice_small_business: true) }

      it 'returns true' do
        expect(shop.vat_free?).to be true
      end
    end

    context 'when lexoffice_tax_type is vatfree' do
      before { shop.update(lexoffice_small_business: false, lexoffice_tax_type: 'vatfree') }

      it 'returns true' do
        expect(shop.vat_free?).to be true
      end
    end

    context 'when neither condition is true' do
      before { shop.update(lexoffice_small_business: false, lexoffice_tax_type: 'standard') }

      it 'returns false' do
        expect(shop.vat_free?).to be false
      end
    end
  end

  describe '#api_version' do
    let(:shop) { create(:shop) }

    it 'returns the ShopifyAPI Context api_version' do
      api_version = '2023-01'
      allow(ShopifyAPI::Context).to receive(:api_version).and_return(api_version)

      expect(shop.api_version).to eq(api_version)
    end
  end

  describe '#execute_graphql_query' do
    let(:shop) { create(:shop) }
    let(:session) { double('ShopifyAPI::Session') }
    let(:client) { double('ShopifyAPI::Clients::Graphql::Admin') }

    before do
      allow(shop).to receive(:with_shopify_session).and_yield(session)
      allow(ShopifyAPI::Clients::Graphql::Admin).to receive(:new).with(session:).and_return(client)
    end

    it 'yields a GraphQL client with proper session' do
      expect { |b| shop.execute_graphql_query(&b) }.to yield_with_args(client)
    end
  end

  describe '#reset_lexoffice_connection' do
    let(:shop) { create(:shop) }

    it 'calls the OAuthRevokeService' do
      expect(OAuthRevokeService).to receive(:call).with(shop)
      shop.reset_lexoffice_connection
    end
  end

  describe '#mark_for_connection_reset' do
    let(:shop) { create(:shop, connection_needs_auth_refresh: false) }

    it 'sets connection_needs_auth_refresh to true' do
      expect { shop.mark_for_connection_reset }
        .to change { shop.reload.connection_needs_auth_refresh }
        .from(false)
        .to(true)
    end
  end

  describe '#send_mail_for_connection_reset' do
    let(:shop) { create(:shop, last_error_mail_sent: nil) }

    before do
      allow(shop).to receive(:with_shopify_session).and_yield
      allow(shop).to receive(:to_json).and_return('{"id":1}')
    end

    context 'when no error mail has been sent yet' do
      it 'enqueues a notification job and updates last_error_mail_sent' do
        expect(NotificationsJob)
          .to receive(:perform_async)
          .with(shop.to_json, 'warn', 'email', I18n.t('error_mail.instructions'))
        expect { shop.send_mail_for_connection_reset }.to change { shop.reload.last_error_mail_sent }.from(nil)
      end
    end

    context 'when error mail has been sent recently' do
      before do
        shop.update(last_error_mail_sent: 1.hour.ago)
        allow(ENV).to receive(:[]).with('ERROR_MAIL_INTERVAL_HOURS').and_return('24')
      end

      it 'does not send another mail' do
        expect(NotificationsJob).not_to receive(:perform_async)
        shop.send_mail_for_connection_reset
      end
    end

    context 'when error mail interval has passed' do
      before do
        shop.update(last_error_mail_sent: 25.hours.ago)
        allow(ENV).to receive(:[]).with('ERROR_MAIL_INTERVAL_HOURS').and_return('24')
      end

      it 'sends another mail and updates last_error_mail_sent' do
        expect(NotificationsJob).to receive(:perform_async)
        expect { shop.send_mail_for_connection_reset }.to(change { shop.reload.last_error_mail_sent })
      end
    end
  end

  describe '#has_running_import?' do
    let(:shop) { create(:shop) }

    context 'when there are running jobs' do
      before do
        JobStatus.create!(
          shop_id: shop.id,
          running: true,
          guid: SecureRandom.uuid
        )
      end

      it 'returns true' do
        expect(shop.has_running_import?).to be true
      end
    end

    context 'when there are no running jobs' do
      before do
        JobStatus.create!(
          shop_id: shop.id,
          running: false,
          guid: SecureRandom.uuid
        )
      end

      it 'returns false' do
        expect(shop.has_running_import?).to be false
      end
    end
  end

  describe '#refresh_token_if_expired' do
    let(:shop) { create(:shop) }
    let(:service_instance) { instance_double(OAuthRefreshTokenService) }

    before do
      allow(OAuthRefreshTokenService).to receive(:new).and_return(service_instance)
      allow(service_instance).to receive(:call)
    end

    context 'when lexoffice tokens are present' do
      before do
        shop.update(lexoffice_token: 'token', lexoffice_refresh_token: 'refresh_token')
      end

      it 'calls the OAuthRefreshTokenService' do
        expect(OAuthRefreshTokenService).to receive(:new).with(shop, false)
        expect(service_instance).to receive(:call)
        shop.refresh_token_if_expired
      end

      it 'passes force flag to service' do
        expect(OAuthRefreshTokenService).to receive(:new).with(shop, true)
        shop.refresh_token_if_expired(true)
      end
    end

    context 'when lexoffice tokens are missing' do
      before do
        shop.update(lexoffice_token: nil, lexoffice_refresh_token: nil)
      end

      it 'does not call the OAuthRefreshTokenService' do
        expect(OAuthRefreshTokenService).not_to receive(:new)
        shop.refresh_token_if_expired
      end
    end
  end

  describe '#connected_to_lexoffice?' do
    let(:shop) { create(:shop) }

    context 'when properly connected' do
      before do
        shop.update(
          lexoffice_token: 'token',
          lexoffice_refresh_token: 'refresh_token',
          connection_needs_auth_refresh: false
        )
      end

      it 'returns true' do
        expect(shop.connected_to_lexoffice?).to be true
      end
    end

    context 'when tokens are missing' do
      before do
        shop.update(
          lexoffice_token: nil,
          lexoffice_refresh_token: 'refresh_token',
          connection_needs_auth_refresh: false
        )
      end

      it 'returns false' do
        expect(shop.connected_to_lexoffice?).to be false
      end
    end

    context 'when connection needs refresh' do
      before do
        shop.update(
          lexoffice_token: 'token',
          lexoffice_refresh_token: 'refresh_token',
          connection_needs_auth_refresh: true
        )
      end

      it 'returns false' do
        expect(shop.connected_to_lexoffice?).to be false
      end
    end
  end

  describe '#should_run_invoice_info_job?' do
    let(:shop) { create(:shop, invoice_info_job_ran: false) }

    before do
      allow(shop).to receive(:connected_to_lexoffice?).and_return(true)
      allow(shop).to receive(:plan_active?).and_return(true)
    end

    it 'returns true when all conditions are met' do
      expect(shop.should_run_invoice_info_job?).to be true
    end

    it 'returns false when not connected to lexoffice' do
      allow(shop).to receive(:connected_to_lexoffice?).and_return(false)
      expect(shop.should_run_invoice_info_job?).to be false
    end

    it 'returns false when plan is not active' do
      allow(shop).to receive(:plan_active?).and_return(false)
      expect(shop.should_run_invoice_info_job?).to be false
    end

    it 'returns false when invoice info job has already run' do
      shop.update(invoice_info_job_ran: true)
      expect(shop.should_run_invoice_info_job?).to be false
    end
  end

  describe '#add_to_mailchimp_subscribers' do
    let(:shop) { create(:shop) }

    before do
      allow(shop).to receive(:plan_active?).and_return(true)
    end

    context 'in production environment' do
      before do
        allow(Rails.env).to receive(:production?).and_return(true)
      end

      it 'enqueues a MailchimpSubscribeJob' do
        expect(MailchimpSubscribeJob).to receive(:perform_async).with(shop.id)
        shop.add_to_mailchimp_subscribers
      end
    end

    context 'in non-production environment' do
      before do
        allow(Rails.env).to receive(:production?).and_return(false)
      end

      it 'does not enqueue a MailchimpSubscribeJob' do
        expect(MailchimpSubscribeJob).not_to receive(:perform_async)
        shop.add_to_mailchimp_subscribers
      end
    end
  end

  describe '#after_activate_one_time_purchase' do
    let(:shop) { create(:shop) }

    before do
      allow(shop).to receive(:schedule_plan_activation_mail)
    end

    it 'updates import_unlocked_at to current time' do
      current_time = Time.zone.now
      allow(Time.zone).to receive(:now).and_return(current_time)

      expect(shop).to receive(:update!).with(import_unlocked_at: current_time)

      shop.after_activate_one_time_purchase
    end

    context 'when in production environment' do
      before do
        allow(Rails.env).to receive(:production?).and_return(true)
      end

      it 'schedules a notifications job' do
        expect(NotificationsJob).to receive(:perform_async).with(anything, 'import', 'notification')

        shop.after_activate_one_time_purchase
      end
    end

    context 'when not in production environment' do
      before do
        allow(Rails.env).to receive(:production?).and_return(false)
      end

      it 'does not schedule a notifications job' do
        expect(NotificationsJob).not_to receive(:perform_async)

        shop.after_activate_one_time_purchase
      end
    end
  end
end
