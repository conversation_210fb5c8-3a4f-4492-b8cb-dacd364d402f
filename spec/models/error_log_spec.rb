# frozen_string_literal: true

require 'rails_helper'
require 'sidekiq/testing'

RSpec.describe ErrorLog, type: :model do
  let(:shop) { create(:shop, :with_transaction_setting) }
  let!(:default_error_type) { create(:error_type, signal: 'Unknown') }
  before :each do
    stub_shopify_authenticated(shop)
    stub_token_refresh
    allow(Redis).to receive(:new).and_return(MockRedis.new)
    allow_any_instance_of(SyncRule).to receive(:handle_webhooks).and_return(nil)
    allow_any_instance_of(ShopSetting).to receive(:handle_rules).and_return(nil)
  end

  it 'assigns a default error_type_id if none is present before save' do
    error_log = ErrorLog.new(shop:, shopify_id: '12345', shopify_type: 'ShopifyAPI::Order')

    expect(error_log.error_type_id).to be_nil
    error_log.save
    expect(error_log.error_type_id).to eq(default_error_type.id)
  end

  it 'does not overwrite an existing error_type_id' do
    error_type = create(:error_type, signal: 'CustomError')
    error_log = ErrorLog.new(shop:, shopify_id: '12345', shopify_type: 'ShopifyAPI::Order', error_type:)

    error_log.save
    expect(error_log.error_type_id).to eq(error_type.id)
  end

  context 'failing TenderTransactionJob' do
    let(:tender_transaction_attr) { load_tender_transaction('amazon_tender_transaction') }

    let(:shop_settings) { create(:shop_setting, enable_tender_transactions: true, excludePOS: false) }

    let(:order_response) { build(:order, id: JSON.parse(tender_transaction_attr)['order_id']) }
    let(:order_id) { order_response.id }

    before(:example) do
      shop.shop_setting = shop_settings
      stub_shopify_authenticated(shop)
      expect(FindOrEnsureFinancialAccountService).to receive(:call).and_raise(StandardError)
      Sidekiq::Testing.inline! do
        TenderTransactionJob.perform_inline(tender_transaction_attr, shop.id)
      end
      allow(ShopifyAPI::Order).to receive(:find).with(id: order_id).and_return(order_response)
    end
    xit 'should have the correct order_id, shopify_type and retry params' do
      log_entry = ErrorLog.first
      expect(log_entry.order_id).to eq order_id.to_s
      expect(log_entry.shopify_type).to eq 'tender_transaction'
      expect(log_entry.retry_params).to eq(tender_transaction_attr)
    end

    xit 'should enqueue the job again' do
      expect(TenderTransactionJob).to receive(:perform_at).once
      log_entry = ErrorLog.first
      log_entry.retry_job
    end
  end

  context 'TenderTransactionJob for POS Order' do
    let(:tender_transaction_attr) { load_tender_transaction('amazon_tender_transaction') }
    let(:shop_settings) { create(:shop_setting, enable_tender_transactions: true, excludePOS: true, shop:) }
    let(:order_response) { build(:order, :pos_order, id: JSON.parse(tender_transaction_attr)['order_id']) }

    before do
      stub_shopify_authenticated(shop)
      allow(ShopifyAPI::Order).to receive(:find).with(id: order_response.id).and_return(order_response)
      Sidekiq::Testing.inline! do
        TenderTransactionJob.perform_inline(tender_transaction_attr, shop_settings.shop_id)
      end
    end
    it 'should be skipped' do
      # here the job is expected to return after the pos check.
      expect(FindOrEnsureFinancialAccountService).not_to receive(:call)
    end
  end

  context 'failing TahCreationJob' do
    let(:tender_transaction_attr) { load_tender_transaction('amazon_tender_transaction') }
    let(:shop_settings) { create(:shop_setting, enable_tah_creation: true, shop:) }
    let(:sync_info_target_id) { SecureRandom.uuid }
    let(:order_response) { build(:order, id: JSON.parse(tender_transaction_attr)['order_id']) }
    let(:order_id) { order_response.id }

    before(:example) do
      allow(ShopifyAPI::Order).to receive(:find).and_return(order_response)
      expect(ShopifyAPI::Transaction).to receive(:all).and_raise(StandardError)
      TahCreationJob.perform_inline(shop_settings.shop_id, '*********', '#1234', sync_info_target_id,
                                    { kind: 'invoice' })
    end
    it 'should have the correct order_id, shopify_type and retry params' do
      log_entry = ErrorLog.first
      expect(log_entry.order_id.to_i).to eq 123_456_789
      expect(log_entry.shopify_type).to eq 'transaction_assignment_hint'
      expect(log_entry.retry_params['sync_info_target_id']).to eq(sync_info_target_id)
      expect(log_entry.retry_params['arguments']['kind']).to eq('invoice')
    end

    it 'should enqueue the job again' do
      expect(TahCreationJob).to receive(:perform_at).once
      log_entry = ErrorLog.first
      log_entry.retry_job
    end
  end

  context 'failing SyncOrderJob' do
    let(:shop_settings) { create(:shop_setting, create_orders: true, shop:) }
    let(:order_response) { build(:order) }
    let(:order_id) { order_response.id }

    before do
      # to raise error after order has been fetched
      allow_any_instance_of(SyncOrderJob).to receive(:find_rules).and_raise(StandardError)
      Rails.logger.info order_response.to_json
      allow(ShopifyAPI::Order).to receive(:find).with(id: order_id).and_return(order_response)
    end

    before(:example) do
      SyncOrderJob.perform_inline('order/create', order_id, shop_settings.shop_id)
    end
    it 'should have the correct order_id and shopify_type' do
      log_entry = ErrorLog.first
      expect(log_entry.order_id.to_i).to eq order_id
      expect(log_entry.shopify_type).to eq 'ShopifyAPI::Order'
    end

    it 'should enqueue the job again' do
      expect(SyncOrderJob).to receive(:perform_at).once
      log_entry = ErrorLog.first
      log_entry.retry_job
    end
  end

  context 'failing RefundJob' do
    let(:order_response) { build(:order, financial_status: 'refunded') }
    let(:refund_response) { build(:refund) }
    let(:order_id) { order_response.id }
    let(:refund_id) { refund_response.id }

    before do
      allow(ShopifyAPI::Refund).to receive(:find).with(id: refund_id, order_id:).and_return(refund_response)
      allow(ShopifyAPI::Order).to receive(:find).with(id: order_id).and_return(order_response)
      allow(ExclusionTagsService).to receive(:call).and_return(false)
    end

    let(:shop_settings) { create(:shop_setting, create_refunds: true, shop:) }

    before do
      # to raise error after order has been fetched
      allow_any_instance_of(ShopifyAPI::Refund).to receive(:has_pending_transactions?).and_raise(StandardError)
    end

    before(:example) do
      RefundJob.perform_inline('refund/create', refund_id, order_id, shop_settings.shop_id)
    end
    it 'should have the correct order_id and shopify_type' do
      log_entry = ErrorLog.first
      expect(log_entry.shopify_id).to eq refund_id.to_s
      expect(log_entry.order_id.to_i).to eq order_id
      expect(log_entry.shopify_type).to eq 'ShopifyAPI::Refund'
    end

    it 'should enqueue the job again' do
      expect(RefundJob).to receive(:perform_at).once
      log_entry = ErrorLog.first
      log_entry.retry_job
    end
  end
end
