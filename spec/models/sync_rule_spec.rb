require 'rails_helper'
require 'sidekiq/testing'

RSpec.describe SyncRule, type: :model do

  before :each do
    @shop = create(:shop, :with_shop_setting)
    stub_shopify_requests("davesdevelopmentstore")
    stub_request(:get, /orders.json/).
      to_return(status: 200, body: load_webhooks("davesdevelopmentstore"), headers: {})
    stub_request(:get, /products.json/).
      to_return(status: 200, body: "{}", headers: {})
    stub_request(:get, /pages.json/).
      to_return(status: 200, body: "{}", headers: {})
    stub_request(:post, "https://test.myshopify.com/admin/webhooks.json").
      to_return(status: 201, body: '{
  "webhook": {
    "id": 987911635,
    "address": "http:\/\/whatever.hostname.com\/",
    "topic": "orders\/create",
    "created_at": "2016-11-09T13:46:44-05:00",
    "updated_at": "2016-11-09T13:46:44-05:00",
    "format": "json",
    "fields": [
    ],
    "metafield_namespaces": [
    ]
  }
}', headers: {})

  end

  describe '#get_webhook_topics' do
    before do
      @shop.with_shopify_session do
        @specific_order_rule = SyncRule.create(:shopify_entity_type => 'Order',
                                               :live_sync => true,
                                               :shop => @shop,
                                               :webhooks => 'orders/fulfilled')
      end
    end

    it 'should have correct length' do
      expect(@specific_order_rule.find_webhook_topics.length).to eq 1
    end
  end
end
