# frozen_string_literal: true

require "rails_helper"

RSpec.describe StringSanitizer do
  # Create a test class that includes the concern
  let(:test_class) do
    Class.new do
      include StringSanitizer
    end
  end

  let(:instance) { test_class.new }

  describe "#limit_address_field" do
    context "with nil or blank input" do
      it "returns nil for nil input" do
        expect(instance.limit_address_field(nil)).to be_nil
      end

      it "returns nil for empty string" do
        expect(instance.limit_address_field("")).to be_nil
      end

      it "returns nil for blank string" do
        expect(instance.limit_address_field("   ")).to be_nil
      end
    end

    context "with short input" do
      it "returns the original string" do
        input = "Short address"
        expect(instance.limit_address_field(input)).to eq(input)
      end
    end

    context "with long input" do
      it "truncates to 100 characters" do
        input = "A" * 150
        expect(instance.limit_address_field(input).length).to be <= 100
      end
    end

    context "with HTML special characters" do
      it "properly encodes and preserves special characters" do
        input = "Address with & and < symbols"
        expected = "Address with & and < symbols"
        expect(instance.limit_address_field(input)).to eq(expected)
      end

      it "handles truncation near special characters" do
        # Create a string that would be truncated right before a special character
        input = "#{"A" * 99}<more text"
        result = instance.limit_address_field(input)

        # Should include only the first 99 characters
        expect(result).to eq("A" * 99)
      end

      it "handles truncation right after special characters" do
        # Create a string with special characters near the truncation point
        input = "#{"A" * 95}& < > \"more text"
        result = instance.limit_address_field(input)

        # Should include the special characters properly
        expected = "#{"A" * 95}&"
        expect(result).to eq(expected)
      end
    end

    context "with special characters" do
      it "handles special characters correctly" do
        input = 'Address with special chars: äöüß€@#$%^&*()'
        expect(instance.limit_address_field(input)).to eq(input)
      end
    end
  end

  describe "#limit_product_title" do
    context "with nil or blank input" do
      it "returns empty string for nil input" do
        expect(instance.limit_product_title(nil)).to eq("")
      end

      it "returns empty string for empty string" do
        expect(instance.limit_product_title("")).to eq("")
      end

      it "returns empty string for blank string" do
        expect(instance.limit_product_title("   ")).to eq("")
      end
    end

    context "with short input" do
      it "returns the original string" do
        input = "Short product title"
        expect(instance.limit_product_title(input)).to eq(input)
      end
    end

    context "with long input" do
      it "truncates to 255 characters" do
        input = "A" * 300
        expect(instance.limit_product_title(input).length).to be <= 255
      end
    end

    context "with HTML special characters" do
      it "properly encodes and preserves special characters" do
        input = "Product with & and < symbols"
        expected = "Product with & and < symbols"
        expect(instance.limit_product_title(input)).to eq(expected)
      end

      it "handles truncation near special characters" do
        # Create a string that would be truncated right before a special character
        input = "#{"A" * 254}&more text"
        result = instance.limit_product_title(input)

        # Should include only the first 254 characters
        expect(result).to eq("A" * 254)
      end

      it "handles truncation right after special characters" do
        # Create a string with special characters near the truncation point
        input = "#{"A" * 250}& < > \"more text"
        result = instance.limit_product_title(input)

        # Should include the special characters properly
        expected = "#{"A" * 250}&"
        expect(result).to eq(expected)
      end
    end

    context "with special characters" do
      it "handles special characters correctly" do
        input = 'Product with special chars: äöüß€@#$%^&*()'
        expect(instance.limit_product_title(input)).to eq(input)
      end
    end
  end

  describe "#remove_expanding_chars" do
    context "with nil or blank input" do
      it "returns nil for nil input" do
        expect(instance.remove_expanding_chars(nil)).to be_nil
      end

      it "returns nil for empty string" do
        expect(instance.remove_expanding_chars("")).to be_nil
      end

      it "returns nil for blank string" do
        expect(instance.remove_expanding_chars("   ")).to be_nil
      end
    end

    context "with expanding characters" do
      it "removes HTML special characters" do
        input = 'Text with & < > " \' symbols'
        expected = "Text with symbols"
        expect(instance.remove_expanding_chars(input)).to eq(expected)
      end

      it "removes special currency and symbol characters" do
        input = "Special chars: ©€®£¢§™"
        expected = "Special chars: "
        expect(instance.remove_expanding_chars(input)).to eq(expected)
      end

      it "preserves spaces" do
        input = "Text with spaces"
        expected = "Text with spaces"
        expect(instance.remove_expanding_chars(input)).to eq(expected)
      end
    end

    context "with non-expanding characters" do
      it "preserves alphanumeric characters" do
        input = "abc123XYZ"
        expect(instance.remove_expanding_chars(input)).to eq(input)
      end

      it "preserves non-expanding special characters" do
        input = "Text-with_non.expanding!characters?"
        expect(instance.remove_expanding_chars(input)).to eq(input)
      end

      it "preserves non-Latin characters" do
        input = "äöüßÄÖÜ日本語"
        expect(instance.remove_expanding_chars(input)).to eq(input)
      end
    end

    context "with mixed content" do
      it "correctly processes mixed expanding and non-expanding characters" do
        input = 'Product: "Super Widget" (€29.99) & Tax'
        expected = "Product: Super Widget (29.99) Tax"
        expect(instance.remove_expanding_chars(input)).to eq(expected)
      end
    end
  end

  describe "private #truncate_html_safe" do
    # Test the private method through the public methods

    context "with complex HTML special characters at truncation boundary" do
      it "handles complex special characters correctly" do
        # Create a string with various special characters
        input = "#{"A" * 95}&<>\"''"
        result = instance.limit_address_field(input)

        # Should include the special characters properly
        expected = "#{"A" * 95}&"
        expect(result).to eq(expected)
      end
    end

    context "with multiple consecutive special characters" do
      it "preserves consecutive special characters" do
        input = "Text with &<> consecutive special characters"
        expected = "Text with &<> consecutive special characters"
        expect(instance.limit_address_field(input)).to eq(expected)
      end
    end
  end
end
