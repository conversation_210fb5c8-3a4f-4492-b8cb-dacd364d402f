# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DiscountCalculator do
  describe '.calculate_discounts' do
    let(:discount_applications) { [{ allocation_method: 'each' }, { allocation_method: 'across' }] }

    context 'when items have discount allocations with matching discount applications' do
      let(:items) do
        [
          {
            discount_allocations: [
              { amount: '5.00', discount_application_index: 0 },
              { amount: '3.00', discount_application_index: 1 }
            ]
          },
          {
            discount_allocations: [
              { amount: '2.50', discount_application_index: 0 }
            ]
          }
        ]
      end

      it 'calculates the total discounts for items with allocation method each' do
        total_discounts = described_class.calculate_line_item_discounts(items, discount_applications)
        expect(total_discounts).to eq(7.50)
      end
    end

    context 'when items have no discount allocations' do
      let(:items) { [{}, {}] }

      it 'returns 0.0' do
        total_discounts = described_class.calculate_line_item_discounts(items, discount_applications)
        expect(total_discounts).to eq(0.0)
      end
    end

    context 'when items have discount allocations but no matching discount applications' do
      let(:items) do
        [
          {
            discount_allocations: [
              { amount: '5.00', discount_application_index: 2 }
            ]
          }
        ]
      end

      it 'returns 0.0' do
        total_discounts = described_class.calculate_line_item_discounts(items, discount_applications)
        expect(total_discounts).to eq(0.0)
      end
    end

    context 'when all discount allocations are for allocation methods other than each' do
      let(:items) do
        [
          {
            discount_allocations: [
              { amount: '5.00', discount_application_index: 1 }
            ]
          }
        ]
      end

      it 'returns 0.0' do
        total_discounts = described_class.calculate_line_item_discounts(items, discount_applications)
        expect(total_discounts).to eq(0.0)
      end
    end

    context 'when items are empty' do
      let(:items) { [] }

      it 'returns 0.0' do
        total_discounts = described_class.calculate_line_item_discounts(items, discount_applications)
        expect(total_discounts).to eq(0.0)
      end
    end
  end

  describe '.valid_free_shipping_line_item?' do
    let(:item) { { discounted_price: '10.00', price: '10.00', discount_allocations: [{ amount: '10.00' }] } }

    context 'when the discount application is for free shipping with 100% value' do
      let(:discount_application) do
        { target_type: 'shipping_line', value: '100.0', value_type: 'percentage' }
      end

      it 'returns true' do
        result = described_class.valid_free_shipping_line_item?(discount_application, item)
        expect(result).to be true
      end
    end

    context 'when the discount application is not for free shipping' do
      let(:discount_application) do
        { target_type: 'line_item', value: '50.0', value_type: 'percentage' }
      end

      it 'returns false' do
        result = described_class.valid_free_shipping_line_item?(discount_application, item)
        expect(result).to be false
      end
    end

    context 'when the discounted price matches the item price' do
      let(:discount_application) do
        { target_type: 'shipping_line', value: '50.0', value_type: 'percentage' }
      end

      it 'returns true' do
        result = described_class.valid_free_shipping_line_item?(discount_application, item)
        expect(result).to be true
      end
    end

    context 'when the discounted price does not match the item price' do
      let(:item) { { discounted_price: '5.00', price: '10.00', discount_allocations: [{ amount: '5.00' }] } }
      let(:discount_application) do
        { target_type: 'shipping_line', value: '90.0', value_type: 'percentage' }
      end

      it 'returns false' do
        result = described_class.valid_free_shipping_line_item?(discount_application, item)
        expect(result).to be false
      end
    end

    context 'when the item has no discount allocations' do
      let(:item) { { discounted_price: '10.00', price: '10.00', discount_allocations: [] } }
      let(:discount_application) do
        { target_type: 'shipping_line', value: '50.0', value_type: 'percentage' }
      end

      it 'returns false' do
        result = described_class.valid_free_shipping_line_item?(discount_application, item)
        expect(result).to be false
      end
    end
  end
end
