# frozen_string_literal: true

require "rails_helper"

RSpec.describe ModelErrorReporting do
  let(:dummy_class) { Class.new { include ModelErrorReporting } }
  let(:instance) { dummy_class.new }
  let(:exception) { RestClient::InternalServerError.new("Internal Server Error", 500) }

  describe "#rescue_with_error_context" do
    before do
      allow(Retriable).to receive(:with_context).and_yield
      allow(Rails.error).to receive(:set_context)
      allow(Rails.error).to receive(:report)
    end

    context "when RestClient::ExceptionWithResponse is raised" do
      let(:api_url) { "https://api.example.com/resource" }
      let(:error_response) { { message: "Error detail" }.to_json }

      before do
        stub_request(:any, api_url).to_return(
          status: 500,
          body: error_response,
          headers: { "Content-Type" => "application/json" }
        )
      end

      it "notifies the error reporter with context and error_message" do
        expect do
          instance.rescue_with_error_context do
            RestClient.get(api_url)
          end
        end.to raise_error(RestClient::InternalServerError)

        expect(Rails.error).to have_received(:set_context).with(hash_including(:lexoffice_response, :extra_info))
        expect(Rails.error).to have_received(:report)
      end
    end

    context "for non-RestClient::ExceptionWithResponse exceptions" do
      it "does not notify the error reporter" do
        expect do
          instance.rescue_with_error_context do
            raise StandardError, "Some error"
          end
        end.to raise_error(StandardError)

        expect(Rails.error).not_to have_received(:report)
      end
    end
  end
end
