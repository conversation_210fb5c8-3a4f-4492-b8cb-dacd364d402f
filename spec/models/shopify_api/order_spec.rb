# frozen_string_literal: true

require "rails_helper"

RSpec.describe ShopifyAPI::Order, type: :model do
  before :each do
    stub_token_refresh

    @shop = create(:shop, :with_shop_setting)
    @shop_domain = "https://#{@shop.shopify_domain}"
    @shop_settings = @shop.shop_setting
    stub_shopify_authenticated(@shop)
  end

  describe "#has_refund?" do
    context "order_with_partial_discount" do
      let(:order) { build(:order, :with_refund) }
      it "should return true" do
        expect(order.has_refund?).to be_truthy
      end
    end
    context "order_without_refund" do
      let(:order) do
        build(:order, :with_refund) do |order|
          order.refunds = []
        end
      end

      it "should return false" do
        expect(order.has_refund?).to be_falsey
      end
    end
    context "order_with_full_refund" do
      let(:order) { build(:order, :with_refund) }

      it "should return false" do
        expect(order.has_refund?).to be_truthy
      end
    end
  end

  describe "#has_taxes?" do
    context "order_with_positive_tax_rates" do
      let(:order) { build(:order, :with_line_items, :with_mixed_line_item_taxes_and_no_shipping_line_tax) }

      it "should return true" do
        expect(order.has_taxes?).to be_truthy
      end
    end
    context "order_with_zero_tax_rate" do
      let(:order) { build(:order, :with_line_items, :with_zero_tax_rate) }

      it "should return false" do
        expect(order.has_taxes?).to be_falsey
      end
    end
  end

  describe "#zero_sum_taxes?" do
    context "order_with_zero_price_taxes" do
      let(:order) { build(:order, :with_line_items, :with_zero_price_tax) }

      it "should return true" do
        expect(order.zero_sum_taxes?).to be_truthy
      end
    end
  end

  # TODO: is this test necessary
  describe "#get_refunds" do
    before do
      @orders = []
      @orders << build(:order, :with_refund)
      @orders << build(:order, :with_refund)
      @orders << build(:order, :with_refund)
    end
    it "orders should be instances of ShopifyAPI::Refund" do
      @orders.each do |order|
        next unless order.has_refund?

        order.refunds.each do |refund|
          expect(refund).to be_instance_of ShopifyAPI::Refund
        end
      end
    end
  end

  describe "valid_email?" do
    let(:order_without_email) do
      build(:order, :with_line_items, :with_customer, line_items_count: 2) do |order|
        order.email = nil
        order.customer.default_address[:address1] = nil
        order.customer.email = nil
      end
    end
    let(:order_with_email) { build(:order, :with_customer) }
    context "order_without_email" do
      it "should return false" do
        expect(order_without_email.valid_email?).to be_falsey
      end
    end
    context "order_with_email" do
      it "should return true" do
        expect(order_with_email.valid_email?).to be_truthy
      end
    end
  end

  describe "vat_number" do
    let(:order_without_vat_number) do
      build(:order, :with_line_items, :with_customer, line_items_count: 2)
    end
    let(:order_with_vat_number) do
      build(:order, :with_billing_address, :with_customer)
    end

    context "when vat_from_customer is present" do
      let(:customer) { build(:shopify_customer, note: +"VAT GB123456789") }

      it "returns the vat number" do
        allow(order_with_vat_number).to receive(:customer) { customer }
        expect(order_with_vat_number.vat_number).to eq "GB123456789"
      end
    end

    context "when vat_from_note_attributes is present" do
      let(:note_attributes) { [{ "name" => "vatId", "value" => +"DE123456789" }] }
      it "returns the vat number" do
        allow(order_with_vat_number).to receive(:known_attributes) { ["note_attributes"] }
        allow(order_with_vat_number).to(receive(:note_attributes).and_return(note_attributes))
        expect(order_with_vat_number.vat_number).to eq "DE123456789"
      end
    end

    context "when neither vat_from_customer nor vat_from_note_attributes is present" do
      it "returns nil" do
        allow(order_without_vat_number).to receive(:has_customer?) { false }
        allow(order_without_vat_number).to receive(:note_attributes) { [] }

        expect(order_without_vat_number.vat_number).to be_nil
      end
    end
  end

  describe "#mailing_excluded?" do
    let(:shop) { create(:shop, :with_shop_setting) }
    let(:order) { build(:order, :with_line_items, tags: "exclude, b2b") }

    before do
      allow(shop).to receive(:shop_setting).and_return(shop.shop_setting)
      allow(shop).to receive(:billing_plan).and_return(billing_plan)
      allow(shop.shop_setting).to receive(:mail_exclusion_tags).and_return("exclude,and,some,other,tags")
    end

    context "when shop has exclude_emails_by_tag feature disabled" do
      let(:billing_plan) { create(:billing_plan, features: []) }

      before do
        allow(shop.shop_setting).to receive(:mail_exclusion_tags).and_return("exclude")
      end

      it "should return false" do
        expect(order.mailing_excluded?(shop)).to be_falsey
      end
    end

    context "when shop has exclude_emails_by_tag feature enabled" do
      let(:billing_plan) { create(:billing_plan, features: ["exclude_emails_by_tag"]) }

      context "when order contains excluded tags" do
        it "should return true" do
          expect(order.mailing_excluded?(shop)).to be_truthy
        end
      end

      context "when order has no exclusion tags" do
        let(:order) { build(:order, :with_line_items, tags: "not_excluded_tag") }

        it "should return false" do
          expect(order.mailing_excluded?(shop)).to be_falsey
        end
      end
    end
  end

  describe "#b2b?" do
    let(:order) { build(:order) }

    context "when the order has a company name and VAT number" do
      before do
        allow(order).to receive(:shipping_address).and_return({ "company" => "Test Company" })
        allow(order).to receive(:billing_address).and_return(nil)
        allow(order).to receive(:vat_number).and_return("DE123456789")
      end

      it "returns true" do
        expect(order.b2b?).to be_truthy
      end
    end

    context "when the order has no company name" do
      before do
        allow(order).to receive(:shipping_address).and_return({ "company" => nil })
        allow(order).to receive(:billing_address).and_return(nil)
        allow(order).to receive(:vat_number).and_return("DE123456789")
      end

      it "returns false" do
        expect(order.b2b?).to be_falsey
      end
    end

    context "when the order has no VAT number" do
      before do
        allow(order).to receive(:shipping_address).and_return({ "company" => "Test Company" })
        allow(order).to receive(:billing_address).and_return(nil)
        allow(order).to receive(:vat_number).and_return(nil)
      end

      it "returns false" do
        expect(order.b2b?).to be_falsey
      end
    end

    context "when the order has no company name and no VAT number" do
      before do
        allow(order).to receive(:shipping_address).and_return({ "company" => nil })
        allow(order).to receive(:billing_address).and_return(nil)
        allow(order).to receive(:vat_number).and_return(nil)
      end

      it "returns false" do
        expect(order.b2b?).to be_falsey
      end
    end
  end

  describe "#highest_tax_rate" do
    context "with mixed tax rates" do
      let(:order) { build(:order, :with_mixed_line_item_taxes_and_no_shipping_line_tax) }

      it "returns the highest tax rate" do
        expect(order.highest_tax_rate).to eq(19.0)
      end
    end

    context "with single tax rate" do
      let(:order) { build(:order, :with_positive_tax_rate) }

      it "returns the tax rate" do
        expect(order.highest_tax_rate).to eq(19.0)
      end
    end

    context "with no tax lines" do
      let(:order) do
        build(:order, :with_line_items) do |order|
          order.line_items.each do |line_item|
            line_item["tax_lines"] = []
          end
        end
      end

      it "returns 0" do
        expect(order.highest_tax_rate).to eq(0.0)
      end
    end
  end

  describe "#highest_applicable_tax_rate" do
    context "when using fulfillment" do
      let(:order) do
        build(:order, :with_line_items) do |order|
          order.fulfillments = [
            build(:fulfillment) do |fulfillment|
              fulfillment.line_items = [
                build(:line_item, tax_lines: [build(:tax_line, rate: 0.19)])
              ]
            end
          ]
        end
      end

      before do
        allow(order).to receive(:use_fulfillment?).and_return(true)
      end

      it "returns the highest tax rate from fulfillment" do
        expect(order.highest_applicable_tax_rate).to eq(19.0)
      end
    end

    context "when not using fulfillment" do
      let(:order) { build(:order, :with_positive_tax_rate) }

      before do
        allow(order).to receive(:use_fulfillment?).and_return(false)
      end

      it "returns the highest tax rate from order" do
        expect(order.highest_applicable_tax_rate).to eq(19.0)
      end
    end
  end

  describe "#discounts_present?" do
    context "when order has discounts" do
      let(:order) { build(:order, total_discounts: "10.0") }

      it "returns true" do
        expect(order.discounts_present?).to be_truthy
      end
    end

    context "when order has no discounts" do
      let(:order) { build(:order, total_discounts: "0.0") }

      it "returns false" do
        expect(order.discounts_present?).to be_falsey
      end
    end
  end

  describe "#address_to_use_for_billing" do
    let(:order) { build(:order, :with_billing_address, :with_shipping_address) }

    context "when shipping_address is nil" do
      let(:order_without_shipping) { build(:order, :with_billing_address) }

      it "returns billing_address" do
        expect(order_without_shipping.address_to_use_for_billing(true)).to eq(order_without_shipping.billing_address
                                                                                                    .symbolize_keys)
      end
    end

    context "when use_shipping_address_for_invoices is true" do
      context "when billing and shipping countries are different" do
        let(:order_with_different_countries) do
          build(:order, :with_billing_address, :with_shipping_address) do |order|
            order.billing_address["country_code"] = "DE"
            order.shipping_address["country_code"] = "FR"
          end
        end

        it "returns shipping_address" do
          expect(order_with_different_countries.address_to_use_for_billing(true)).to eq(order_with_different_countries
            .shipping_address.symbolize_keys)
        end
      end

      context "when billing and shipping countries are the same" do
        let(:order_with_same_countries) do
          build(:order, :with_billing_address, :with_shipping_address) do |order|
            order.billing_address["country_code"] = "DE"
            order.shipping_address["country_code"] = "DE"
          end
        end

        it "returns billing_address" do
          expect(order_with_same_countries.address_to_use_for_billing(true)).to eq(order_with_same_countries
            .billing_address.symbolize_keys)
        end
      end

      context "when shipping_address is nil" do
        let(:order_without_shipping) { build(:order, :with_billing_address) }

        it "returns billing_address" do
          expect(order_without_shipping.address_to_use_for_billing(true)).to eq(order_without_shipping.billing_address
            .symbolize_keys)
        end
      end

      context "when billing_address is nil" do
        let(:order_without_billing) { build(:order, :with_shipping_address) }

        it "returns shipping_address" do
          expect(order_without_billing.address_to_use_for_billing(true)).to eq(order_without_billing.shipping_address
            .symbolize_keys)
        end
      end

      context "when both billing_address and shipping_address are nil" do
        let(:order_without_addresses) { build(:order) }

        it "returns nil" do
          expect(order_without_addresses.address_to_use_for_billing(true)).to be_nil
        end
      end
    end

    context "when use_shipping_address_for_invoices is false" do
      context "when billing and shipping countries are different" do
        let(:order_with_different_countries) do
          build(:order, :with_billing_address, :with_shipping_address) do |order|
            order.billing_address["country_code"] = "DE"
            order.shipping_address["country_code"] = "FR"
          end
        end

        it "returns billing_address" do
          expect(order_with_different_countries.address_to_use_for_billing(false)).to eq(order_with_different_countries
            .billing_address.symbolize_keys)
        end
      end

      context "when shipping_address is nil" do
        let(:order_without_shipping) { build(:order, :with_billing_address) }

        it "returns billing_address" do
          expect(order_without_shipping.address_to_use_for_billing(false)).to eq(order_without_shipping.billing_address
            .symbolize_keys)
        end
      end

      context "when billing_address is nil" do
        let(:order_without_billing) { build(:order, :with_shipping_address) }

        it "returns nil" do
          expect(order_without_billing.address_to_use_for_billing(false)).to be_nil
        end
      end

      context "when both billing_address and shipping_address are nil" do
        let(:order_without_addresses) { build(:order) }

        it "returns nil" do
          expect(order_without_addresses.address_to_use_for_billing(false)).to be_nil
        end
      end
    end
  end
end
