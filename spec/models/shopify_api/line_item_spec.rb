# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ShopifyAPI::LineItem, type: :model do
  describe 'apply_line_item_discount?' do
    let(:order) { build_discounted_order }

    context 'when discount application matches allocation method "each"' do
      let(:line_item) { { id: 1, discount_allocations: [{ discount_application_index: 0 }] } }

      it 'returns true' do
        expect(ShopifyAPI::LineItem.apply_line_item_discount?(order, line_item)).to be true
      end
    end

    context 'when discount application does not match allocation method "each"' do
      let(:line_item) { { id: 1, discount_allocations: [{ discount_application_index: 1 }] } }

      it 'returns false' do
        expect(ShopifyAPI::LineItem.apply_line_item_discount?(order, line_item)).to be false
      end
    end

    context 'when line_item has no discount allocations' do
      let(:line_item) { { id: 1, discount_allocations: [] } }

      it 'returns false' do
        expect(ShopifyAPI::LineItem.apply_line_item_discount?(order, line_item)).to be false
      end
    end
  end

  describe '.free_discount_with_zero_tax_rate?' do
    context 'when line item has 100% discount and zero tax rate' do
      let(:line_item) do
        {
          price: '10.00',
          taxable: true,
          tax_lines: [{ rate: 0.00, price: '0.00' }],
          discount_allocations: [{ amount: '10.00' }]
        }
      end

      it 'returns true' do
        expect(ShopifyAPI::LineItem.free_discount_with_zero_tax_rate?(line_item)).to be true
      end
    end

    context 'when line item has partial discount' do
      let(:line_item) do
        {
          price: '10.00',
          taxable: true,
          tax_lines: [{ rate: 0.00, price: '0.00' }],
          discount_allocations: [{ amount: '5.00' }]
        }
      end

      it 'returns false' do
        expect(ShopifyAPI::LineItem.free_discount_with_zero_tax_rate?(line_item)).to be false
      end
    end

    context 'when line item has no discount' do
      let(:line_item) do
        {
          price: '10.00',
          taxable: true,
          tax_lines: [{ rate: 0.00, price: '0.00' }],
          discount_allocations: []
        }
      end

      it 'returns false' do
        expect(ShopifyAPI::LineItem.free_discount_with_zero_tax_rate?(line_item)).to be false
      end
    end

    context 'when line item has non-zero tax rate' do
      let(:line_item) do
        {
          price: '10.00',
          taxable: true,
          tax_lines: [{ rate: 0.19, price: '1.90' }],
          discount_allocations: [{ amount: '10.00' }]
        }
      end

      it 'returns false' do
        expect(ShopifyAPI::LineItem.free_discount_with_zero_tax_rate?(line_item)).to be false
      end
    end

    context 'when line item has no tax lines' do
      let(:line_item) do
        {
          price: '10.00',
          taxable: true,
          tax_lines: [],
          discount_allocations: [{ amount: '10.00' }]
        }
      end

      it 'returns true' do
        expect(ShopifyAPI::LineItem.free_discount_with_zero_tax_rate?(line_item)).to be true
      end
    end
  end
end
