# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ShopSetting, type: :model do
  before :each do
    stub_token_refresh
    @shop = create(:shop, :with_shop_setting)
  end

  context 'refund rule create' do
    context 'when create refunds equals true' do
      before do
        @settings = @shop.shop_setting
        @settings.create_refunds = true
        @settings.save
      end

      let(:refund_rule) do
        SyncRule.find_by(shop: @settings.shop, shopify_entity_type: 'Refund', target_type: 'Refund',
                         target_action: 'Create', live_sync: true)
      end

      it 'should be not nil' do
        expect(refund_rule).not_to be_nil
      end
    end

    context 'when create refunds equals false' do
      before do
        @settings = @shop.shop_setting
        @settings.create_refunds = false
        @settings.save
      end

      let(:refund_rule) do
        SyncRule.find_by(shop: @settings.shop, shopify_entity_type: 'Refund', target_type: 'Refund',
                         target_action: 'Create', live_sync: true)
      end

      it 'should be not nil' do
        expect(refund_rule).to be_nil
      end
    end

    describe 'liquid validation' do
      before do
        @settings = @shop.shop_setting
        @settings.invoice_title = 'Invoice for {{name}}'
        @settings.save
      end

      it 'should have correct title' do
        expect(@settings.invoice_title).to eq 'Invoice for {{name}}'
      end
    end

    describe 'if send_mail require mark_due_immediately' do
      before do
        @settings = @shop.shop_setting
        @settings.send_invoice_mail = false
        @settings.mark_due_immediately = false
        @settings.send_invoice_mail = true
        @settings.save
      end

      it 'should be true' do
        expect(@settings.mark_due_immediately).to be_truthy
      end
    end
  end

  context 'ensure tender transactions enabled' do
    let(:shop_setting) { build(:shop_setting, enable_tender_transactions: false, enable_tah_creation: true) }

    before { shop_setting.save }

    it 'set enable_tender_transaction to true' do
      expect(shop_setting.enable_tender_transactions).to be_truthy
    end
  end
end
