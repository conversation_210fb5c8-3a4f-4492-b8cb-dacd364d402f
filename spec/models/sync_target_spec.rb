# frozen_string_literal: true

require 'rails_helper'

RSpec.shared_examples 'returns nil' do
  it 'returns nil' do
    expect(response).to be_nil
  end
end

RSpec.describe SyncTarget, type: :model do
  let(:shop) { create(:shop, :with_shop_setting) }
  let(:sync_target) { described_class.new(shop, nil) }
  let(:rule) { build(:sync_rule) }
  let(:order) do
    build(:order, :with_billing_address, :with_shipping_address, :with_line_items, :with_customer, :with_shipping_lines,
          :with_fulfillments) do |order|
      order.line_items.each_with_index do |line_item, _index|
        line_item[:name] = Faker::Lorem.characters
        line_item[:title] = Faker::Lorem.characters
      end

      order.shipping_lines.each_with_index do |shipping_line_item, _index|
        shipping_line_item[:title] = Faker::Lorem.characters
      end
    end
  end
  let(:order_with_no_address_and_no_email) do
    build(:order, :with_line_items, :with_customer, :with_shipping_address, :with_billing_address,
      line_items_count: 2) do |order|
      order.email = nil
      order.customer.default_address[:address1] = nil
      order.customer.email = nil
    end
  end
  let(:fake_mail_contact) { JSON.parse(load_json_fixture('lexoffice_contacts/contact_with_fake_email')) }
  let(:order_response) do
    build(:order, :with_billing_address, :with_shipping_address, :with_customer, :with_line_items)
  end

  before do
    stub_shopify_authenticated(shop)
    Sidekiq::Testing.fake!
    stub_token_refresh
    allow(Redis).to receive(:new).and_return(MockRedis.new)
    allow(ShopifyAPI::Order).to receive(:find).and_return(order_response)
    allow(shop).to receive(:billing_plan).and_return(create(:billing_plan))
    allow_any_instance_of(CreateOrGetContactService).to receive(:call).and_return(SecureRandom.hex(12))
    allow_any_instance_of(Lexoffice::Contact).to receive(:find_by_id).and_return(SecureRandom.hex(12))
    stub_request(:any, /.*#{order_response.id}.json/).to_return(status: 200, body: order_response.to_json, headers: {})
    stub_request(:get, /invoice/).to_return(status: 200, body: invoice_response.to_json, headers: {})
    stub_get_credit_note(create(:lexoffice_credit_note,
                                id: invoice_response[:id],
                                voucherStatus: invoice_response[:voucherStatus],
                                voucherNumber: invoice_response[:voucherNumber],
                                totalPrice: invoice_response[:totalPrice],
                                files: invoice_response[:files]))
    allow_any_instance_of(Shop).to receive(:shopify_plan).and_return(
      OpenStruct.new(displayName: 'Shopify Basic', partnerDevelopment: false, shopifyPlus: false)
    )
  end

  describe '#handle_refund' do
    let(:credit_note) { Lexoffice::CreditNote.new(shop.lexoffice_token) }
    let(:refund) { build(:refund) }
    let(:response) { sync_target.handle_refund(refund) }
    let(:sync_info) do
      create(:sync_info, id: 1001, shopify_id: refund.id, shop:, target_type: 'Refund', last_action: 'Job started')
    end
    # TODO: maybe an extra factory for credit notes
    let(:invoice_response) { FactoryBot.build(:lexoffice_credit_note) }

    context 'with lexoffice token' do
      let(:refund) do
        build(:refund, order_id: order_response.id, prefix_options: { order_id: order_response.id })
      end
      let(:response) { sync_target.handle_refund(refund) }

      before do
        credit_note.data_from_refund_and_order(refund, order, shop.shop_setting)
        allow_any_instance_of(Shop).to receive(:billing_plan).and_return(double(features: [], name: 'test'))
      end

      context 'with transactions' do
        before do
          allow_any_instance_of(CreditNoteDataBuilderService).to receive(:call).and_return([credit_note, sync_info])
          stub_request(:post, /credit-notes/).to_return(status: 200, body: invoice_response.to_json)
        end

        it 'creates a credit note' do
          expect(response).to eq invoice_response[:id]
          expect(sync_info.last_action).to eq 'Created'
        end

        it ' creates a sync_info with a populated extra_infos field' do
          response
          expect(sync_info.extra_infos).to eq({ voucher_title: invoice_response[:voucherNumber],
                                                status: invoice_response[:voucherStatus],
                                                total_price: invoice_response.dig(:totalPrice, :totalGrossAmount),
                                                document_id: invoice_response.dig(:files, :documentFileId) })
        end

        it 'does not queue a mail job' do
          expect(SendMailJob).to_not have_enqueued_sidekiq_job(sync_info.id)
        end

        it 'does not queue a tender transaction job' do
          expect(TahCreationJob).to_not have_enqueued_sidekiq_job(shop.to_s, refund.id, sync_info.target_id)
        end

        context 'with mailing enabled' do
          before { shop.shop_setting.update(send_invoice_mail: true) }

          it 'queues a mail job' do
            expect(response).to eq invoice_response[:id]
            expect(SendMailJob).to have_enqueued_sidekiq_job(sync_info.id)
          end
        end

        context 'with tender transactions enabled' do
          before do
            shop.shop_setting.update(enable_tah_creation: true)
          end

          it 'queues a tender transaction job' do
            expect(response).to eq invoice_response[:id]
            expect(TahCreationJob).to have_enqueued_sidekiq_job(shop.id, refund.order_id,
                                                                order_response.name,
                                                                sync_info.target_id,
                                                                { kind: 'refund',
                                                                  transaction_id: refund.transactions.first.id })
          end
        end
      end

      context 'without credit note' do
        before { allow_any_instance_of(CreditNoteDataBuilderService).to receive(:call).and_return([nil, nil]) }

        it_behaves_like 'returns nil'
      end
    end

    context 'without lexoffice token' do
      let(:refund) do
        build(:refund, prefix_options: { order_id: 5_075_660_229 }) do |refund|
          refund.transactions.first.status = 'failure'
        end
      end

      it_behaves_like 'returns nil'
    end

    context 'with paypal gateway and failed transaction' do
      let(:refund) do
        build(:refund, prefix_options: { order_id: 5_075_660_229 }) do |refund|
          refund.transactions.first.status = 'failure'
        end
      end

      it_behaves_like 'returns nil'
    end
  end

  describe '#create_order' do
    let(:invoice) { Lexoffice::Invoice.new(shop.lexoffice_token) }
    let(:rule) do
      create(:sync_rule,
             shop:,
             webhooks: 'orders/create',
             target_type: 'Invoice',
             target_action: 'Create')
    end
    let(:sync_info) do
      SyncInfo.create(shopify_id: order.id, shop:, target_type: 'Invoice', last_action: 'Job started')
    end
    let(:response) { sync_target.create_order(order, rule) }
    let(:invoice_response) { FactoryBot.build(:lexoffice_invoice) }

    context 'with lexoffice token' do
      before do
        invoice.data_from_order(order, shop.shop_setting)
        allow_any_instance_of(Shop).to receive(:billing_plan).and_return(double(features: [], name: 'test'))
      end

      context 'with invoice' do
        before do
          allow_any_instance_of(InvoiceDataBuilderService).to receive(:call).and_return([invoice, sync_info])
          stub_request(:post, /invoices/).to_return(status: 200, body: invoice_response.to_json)
        end

        it 'should create invoice' do
          shop.shop_setting.update(send_invoice_mail: true)
          expect(response).to eq invoice_response[:id]
          expect(sync_info.last_action).to eq 'Send Mail Job Queued'
        end

        it 'does not queue a mail job' do
          expect(SendMailJob).to_not have_enqueued_sidekiq_job(sync_info.id)
        end

        it 'does not queue a tender transaction job' do
          expect(TahCreationJob).to_not have_enqueued_sidekiq_job(shop.to_s, order.id, sync_info.target_id)
        end

        it ' creates a sync_info with a populated extra_infos field' do
          response
          expect(sync_info.extra_infos).to eq({ voucher_title: invoice_response[:voucherNumber],
                                                status: invoice_response[:voucherStatus],
                                                total_price: invoice_response.dig(:totalPrice, :totalGrossAmount),
                                                document_id: invoice_response.dig(:files, :documentFileId) })
        end

        context 'with mailing enabled' do
          before { shop.shop_setting.update(send_invoice_mail: true) }

          it 'queues a mail job' do
            expect(response).to eq invoice_response[:id]
            expect(SendMailJob).to have_enqueued_sidekiq_job(sync_info.id)
          end

          context 'with no address and no email' do
            before do
              allow_any_instance_of(CreateOrGetContactService).to receive(:call).and_return(fake_mail_contact)
            end
            let(:order) { order_with_no_address_and_no_email }
            it 'does not queue a mail job' do
              expect(response).to eq invoice_response[:id]
              expect(SendMailJob).to_not have_enqueued_sidekiq_job(sync_info.id)
            end
          end
        end

        context 'with tender transactions enabled' do
          before { shop.shop_setting.update(enable_tah_creation: true) }

          it 'queues a tender transaction job' do
            expect(response).to eq invoice_response[:id]
            expect(TahCreationJob).to have_enqueued_sidekiq_job(shop.id, order.id, order.name, sync_info.target_id)
          end
        end
      end

      context 'without invoice' do
        before { allow_any_instance_of(InvoiceDataBuilderService).to receive(:call).and_return([nil, nil]) }

        it_behaves_like 'returns nil'
      end

      context 'when rest client raises an error' do
        before do
          allow_any_instance_of(Lexoffice::Invoice).to receive(:create).and_raise(RestClient::ExceptionWithResponse)
        end

        it 'should not destroy sync info' do
          expect { response }.to raise_error(RestClient::ExceptionWithResponse)
          expect(SyncInfo.find(sync_info.id)).to be_present
        end
      end
    end

    context 'without lexoffice token' do
      before { allow_any_instance_of(Shop).to receive(:lexoffice_token).and_return('') }

      it_behaves_like 'returns nil'
    end
  end
end
