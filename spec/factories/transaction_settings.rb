# frozen_string_literal: true

FactoryBot.define do
  factory :transaction_setting do
    shop { create(:shop) }
    enable_amazon { [true, false].sample }
    amazon_account_id { SecureRandom.hex(4) }
    enable_apple_pay { [true, false].sample }
    apple_pay_account_id { SecureRandom.hex(4) }
    enable_credit_card { false }
    credit_card_account_id { SecureRandom.hex(4) }
    enable_google_pay { [true, false].sample }
    google_pay_account_id { SecureRandom.hex(4) }
    enable_klarna { [true, false].sample }
    klarna_account_id { SecureRandom.hex(4) }
    enable_samsung_pay { true }
    samsung_pay_account_id { SecureRandom.hex(4) }
    enable_shopify_pay { [true, false].sample }
    shopify_pay_account_id { SecureRandom.hex(4) }
    enable_klarna_pay_later { [true, false].sample }
    klarna_pay_later_account_id { SecureRandom.hex(4) }
    enable_sofort { [true, false].sample }
    sofort_account_id { SecureRandom.hex(4) }
    enable_other { [true, false].sample }
    other_account_id { SecureRandom.hex(4) }
  end

  trait :without_accounts_enabled do
    enable_amazon { false }
    amazon_account_id { nil }
    enable_apple_pay {  false }
    apple_pay_account_id { nil }
    enable_credit_card { false }
    credit_card_account_id { nil }
    enable_google_pay { false }
    google_pay_account_id { nil }
    enable_klarna { false }
    klarna_account_id { nil }
    enable_samsung_pay { false }
    samsung_pay_account_id { nil }
    enable_shopify_pay { false }
    shopify_pay_account_id { nil }
    enable_other { false }
    other_account_id { nil }
  end

  trait :with_accounts_enabled do
    enable_amazon { true }
    amazon_account_id { SecureRandom.hex(4) }
    enable_apple_pay {  true }
    apple_pay_account_id { SecureRandom.hex(4) }
    enable_credit_card { true }
    credit_card_account_id { SecureRandom.hex(4) }
    enable_google_pay { true }
    google_pay_account_id { SecureRandom.hex(4) }
    enable_klarna { true }
    klarna_account_id { SecureRandom.hex(4) }
    enable_samsung_pay { true }
    samsung_pay_account_id { SecureRandom.hex(4) }
    enable_shopify_pay { true }
    shopify_pay_account_id { SecureRandom.hex(4) }
    enable_other { true }
    other_account_id { SecureRandom.hex(4) }
  end
end
