FactoryBot.define do
  factory :sync_rule do
    shop { create(:shop) }
    target_type { nil }
    target_action { nil }
    shopify_entity_type { nil }
    live_sync { [true, false].sample }
    sync_back { [true, false].sample }
    use_images { [true, false].sample }
    webhooks { %w[refunds/create orders/create orders/fulfilled].sample }

    after(:build) do |sync_rule|
      unless sync_rule.class._save_callbacks.select { |cb| cb.kind == :after && cb.filter == :handle_webhooks }.empty?
        sync_rule.class.skip_callback(:save, :after, :handle_webhooks)
      end
    end
  end
end
