# frozen_string_literal: true

FactoryBot.define do
  factory :snippet do
    name { 'Salutation' }
    code do
      '<PERSON><PERSON> {{customer.first_name}}, sie erhalten heute Ihre Rechnung zu Auftrag {{name}}.'
    end
    description { 'Invoice pretext' }

    trait :for_transaction do
      code do
        '<PERSON>o {{customer.first_name}}, sie erhalten heute Ihre Rechnung zu Auftrag {{name}}. ' \
          'Ihre Transaktionen: {{order_transactions.first.amount}}'
      end
    end
  end
end
