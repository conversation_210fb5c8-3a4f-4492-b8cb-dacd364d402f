# frozen_string_literal: true

FactoryBot.define do
  factory :lexoffice_credit_note, class: Hash do
    skip_create

    id { SecureRandom.uuid }
    voucherStatus { %w[draft open].sample }
    voucherNumber { Faker::Alphanumeric.alpha(number: 10) }
    totalPrice { { totalGrossAmount: Faker::Number.decimal(l_digits: 2) } }
    files { { documentFileId: Faker::Number.unique.number(digits: 5) } }

    initialize_with { attributes }
  end
end
