# frozen_string_literal: true

FactoryBot.define do
  factory :material_line_item, class: Hash do
    skip_create

    id { SecureRandom.uuid }
    type { 'material' }
    name { Faker::Commerce.product_name }
    description { Faker::Lorem.sentence }
    quantity { Faker::Number.between(from: 1, to: 10) }
    unitName { 'Stück' }
    unitPrice do
      {
        currency: 'EUR',
        netAmount: Faker::Commerce.price(range: 1..100.0, as_string: false),
        grossAmount: Faker::Commerce.price(range: 1..100.0, as_string: false),
        taxRatePercentage: 19
      }
    end
    discountPercentage { 50 }
    lineItemAmount { (unitPrice[:netAmount] * quantity).round(2) }

    initialize_with { attributes }
  end

  factory :service_line_item, class: Hash do
    skip_create

    id { SecureRandom.uuid }
    type { 'service' }
    name { Faker::Commerce.department }
    description { Faker::Lorem.sentence }
    quantity { Faker::Number.between(from: 1, to: 10) }
    unitName { 'Stunde' }
    unitPrice do
      {
        currency: 'EUR',
        netAmount: Faker::Commerce.price(range: 1..100.0, as_string: false),
        grossAmount: Faker::Commerce.price(range: 1..100.0, as_string: false),
        taxRatePercentage: 7
      }
    end
    discountPercentage { 0 }
    lineItemAmount { (unitPrice[:netAmount] * quantity).round(2) }

    initialize_with { attributes }
  end

  factory :custom_line_item, class: Hash do
    skip_create

    id { nil }
    type { 'custom' }
    name { Faker::Commerce.product_name }
    description { nil }
    quantity { 1 }
    unitName { 'Stück' }
    unitPrice do
      {
        currency: 'EUR',
        netAmount: Faker::Commerce.price(range: 1..100.0, as_string: false),
        grossAmount: Faker::Commerce.price(range: 1..100.0, as_string: false),
        taxRatePercentage: 0
      }
    end
    discountPercentage { 0 }
    lineItemAmount { unitPrice[:netAmount] * quantity }

    initialize_with { attributes }
  end
end

FactoryBot.define do
  factory :lexoffice_invoice, class: Hash do
    skip_create

    id { SecureRandom.uuid }
    organizationId { SecureRandom.uuid }
    createdDate { Faker::Time.between(from: 5.years.ago, to: Time.current).iso8601 }
    updatedDate { createdDate }
    version { 0 }
    language { 'de' }
    archived { false }
    voucherStatus { 'draft' }
    voucherNumber { "RE#{Faker::Number.number(digits: 4)}" }
    voucherDate { Faker::Time.between(from: 5.years.ago, to: Time.current).iso8601 }
    dueDate { nil }
    address do
      {
        contactId: nil,
        name: Faker::Company.name,
        supplement: "Gebäude #{Faker::Number.number(digits: 2)}",
        street: Faker::Address.street_address,
        city: Faker::Address.city,
        zip: Faker::Address.zip_code,
        countryCode: 'DE'
      }
    end
    xRechnung { nil }
    lineItems { [association(:material_line_item), association(:service_line_item), association(:custom_line_item)] }
    totalPrice do
      total_net_amount = lineItems.sum { |item| item[:lineItemAmount] || 0 }
      total_gross_amount = lineItems.sum { |item| (item[:unitPrice][:grossAmount] * item[:quantity]) || 0 }
      total_tax_amount = total_gross_amount - total_net_amount

      {
        currency: 'EUR',
        totalNetAmount: total_net_amount,
        totalGrossAmount: total_gross_amount,
        totalTaxAmount: total_tax_amount,
        totalDiscountAbsolute: nil,
        totalDiscountPercentage: nil
      }
    end
    taxAmounts do
      lineItems
        .group_by { |item| item[:unitPrice][:taxRatePercentage] }
        .map do |tax_rate, items|
        {
          taxRatePercentage: tax_rate,
          taxAmount: items.sum do |item|
            (item[:unitPrice][:grossAmount] * item[:quantity]) - (item[:unitPrice][:netAmount] * item[:quantity])
          end,
          netAmount: items.sum { |item| item[:unitPrice][:netAmount] * item[:quantity] }
        }
      end
    end
    taxConditions { { taxType: 'net', taxTypeNote: nil } }
    paymentConditions do
      {
        paymentTermLabel: '10 Tage - 3 %, 30 Tage netto',
        paymentTermLabelTemplate: '{discountRange} Tage -{discount}, {paymentRange} Tage netto',
        paymentTermDuration: 30,
        paymentDiscountConditions: {
          discountPercentage: 3,
          discountRange: 10
        }
      }
    end
    shippingConditions do
      {
        shippingDate: Faker::Time.between(from: 5.years.ago, to: Time.current).iso8601,
        shippingEndDate: nil,
        shippingType: 'delivery'
      }
    end
    closingInvoice { false }
    claimedGrossAmount { nil }
    downPaymentDeductions { nil }
    recurringTemplateId { nil }
    relatedVouchers { [] }
    title { 'Rechnung' }
    introduction { 'Ihre bestellten Positionen stellen wir Ihnen hiermit in Rechnung' }
    remark { 'Vielen Dank für Ihren Einkauf' }
    files { { documentFileId: SecureRandom.uuid } }

    initialize_with { attributes }
  end
end
