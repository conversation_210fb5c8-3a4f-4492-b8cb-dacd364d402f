FactoryBot.define do
  factory :shop_setting do
    shop { create(:shop) }
    service_api_token { SecureRandom.hex(12) }
    create_orders { [true, false].sample }
    create_invoices { [true, false].sample }
    send_invoice_mail { [true, false].sample }
    excludePOS { [true, false].sample }
    create_refunds { [true, false].sample }
    mark_due_immediately { [true, false].sample }
    create_customer { [true, false].sample }
    enable_tender_transactions { [true, false].sample }
    confirm_tax_settings { [true, false].sample }
    enable_tah_creation { [true, false].sample }
    invoice_mail_offset_days { rand(0..10) }
    invoice_timing { %w[orders/create orders/fulfilled].sample }
    shipping_min_days { 0 }
    shipping_max_days { 1 }
    mail_exclusion_tags { "other, b2c" }
    invoice_title { "Rechnung zu {{name}}" }
    refund_title { "Gutschrift zu {{name}}" }
    use_shipping_address_for_invoices { [true, false].sample }

    trait :with_tender_transactions do
      enable_tender_transactions { true }
    end
  end
end
