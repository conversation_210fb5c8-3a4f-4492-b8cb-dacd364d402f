# frozen_string_literal: true

FactoryBot.define do
  factory :shopify_transaction, class: ShopifyAPI::Transaction do
    sequence(:id) { |n| n }
    kind { 'sale' }
    amount { '100.00' }
    authorization { 'authorization_code' }
    authorization_expires_at { '2023-12-31T23:59:59Z' }
    created_at { '2023-01-01T00:00:00Z' }
    currency { 'EUR' }
    currency_exchange_adjustment { { adjustment: '5.00', original_amount: '105.00', final_amount: '100.00' } }
    sequence(:device_id) { |n| n }
    error_code { nil }
    extended_authorization_attributes { { some_attribute: 'value' } }
    gateway { 'stripe' }
    sequence(:location_id) { |n| n }
    message { 'Transaction successful' }
    sequence(:order_id) { |n| n }
    parent_id { nil }
    payment_details { { card_brand: 'Visa', card_bin: '123456' } }
    payments_refund_attributes { nil }
    processed_at { '2023-01-01T01:00:00Z' }
    receipt { { transaction_receipt: 'receipt_code' } }
    source_name { 'web' }
    status { 'success' }
    test { false }
    total_unsettled_set { { unsettled_amount: '0.00' } }
    sequence(:user_id) { |n| n }

    trait :failed do
      status { 'failure' }
      error_code { 'card_declined' }
      message { 'Card was declined' }
    end

    trait :pending do
      status { 'pending' }
    end

    trait :refund do
      kind { 'refund' }
      amount { '-50.00' }
    end
  end
end
