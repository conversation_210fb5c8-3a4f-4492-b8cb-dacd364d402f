# frozen_string_literal: true

FactoryBot.define do
  factory :error_log do
    shop
    error_type
    exception_type { Faker::Lorem.word }
    error_info_internal { Faker::Lorem.sentence }
    error_info_external { Faker::Lorem.sentence }
    shopify_id { Faker::Number.number(digits: 14) }
    shopify_name { "##{Faker::Number.number(digits: 4)}" }
    shopify_type { %w[ShopifyAPI::Order ShopifyAPI::Refund tender_transaction transaction_assignment_hint].sample }
    order_id { Faker::Number.number(digits: 14) }
    retry_params { { random_param: Faker::Lorem.word } }
  end
end
