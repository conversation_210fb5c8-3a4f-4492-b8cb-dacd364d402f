# frozen_string_literal: true

FactoryBot.define do
  factory :coupon_code do
    coupon_code { SecureRandom.uuid.delete('-').upcase.slice(0, 6) }
    redeemed { false }
    shop_id { nil }
    free_days { [1..60].sample }
    validity { Date.today + rand(1..60).days }
    type { 'OneTimeCouponCode' }
    redeem_counter { rand(1..10) }
    trait :redeemed do
      redeemed { true }
    end

    initialize_with { OneTimeCouponCode.new(attributes) }

    trait :campaign do
      type { 'CampaignCouponCode' }
      initialize_with { CampaignCouponCode.new(attributes) }
    end
  end
end
