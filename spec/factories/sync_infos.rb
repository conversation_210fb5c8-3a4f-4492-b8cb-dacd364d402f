# frozen_string_literal: true

def extra_infos_hash
  {
    voucher_title: "RE#{Faker::Number.number(digits: 4)}",
    status: %w[open paid].sample,
    total_price: Faker::Commerce.price,
    document_id: SecureRandom.hex(12)
  }
end

FactoryBot.define do
  factory :sync_info do
    transient do
      shop_object { create(:shop) }
    end
    shop { shop_object }
    shopify_order_id { SecureRandom.random_number(9_223_372_036_854_775) }
    shopify_order_name { "##{Faker::Number.number(digits: 4)}" }
    shopify_created_at { Faker::Time.between(from: 2.days.ago, to: Time.zone.now) }
    target_id { SecureRandom.hex(12) }
    target_type { nil }
    last_action { 'Job started' }
    invoice_mail_sent { [true, false].sample }
    trait :fresh do
      target_id { nil }
    end
    trait :with_sync_rule do
      sync_rule { create(:sync_rule, shop: shop_object) }
    end

    trait :invoice_created do
      target_type { 'Invoice' }
      last_action { 'Create Invoice' }
      extra_infos { extra_infos_hash }
    end

    trait :credit_note_created do
      target_type { 'Refund' }
      last_action { 'Created' }
      extra_infos { extra_infos_hash }
    end
  end
end
