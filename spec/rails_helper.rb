# frozen_string_literal: true

require "simplecov"
SimpleCov.start("rails") do
  # Only track application code files. Add /lib or other folders if needed
  track_files "{app}/**/*.rb"
  enable_coverage :branch
  refuse_coverage_drop :line, :branch
end

ENV["RAILS_ENV"] ||= "test"
require File.expand_path("../config/environment", __dir__)
# Prevent database truncation if the environment is production
abort("The Rails environment is running in production mode!") if Rails.env.production?

# Load all application files to ensure full coverage
Rails.root.glob("app/**/*.rb").sort.each { |file| require file }

require "rspec/rails"
# Add additional requires below this line. Rails is not loaded until this point!
require "capybara/rails"
require "webmock/rspec"
# Load lib files for BillingPlan Testing
require_relative "../lib/billing"

# The following line is provided for convenience purposes. It has the downside
# of increasing the boot-up time by auto-requiring all files in the support
# directory. Alternatively, in the individual `*_spec.rb` files, manually
# require only the support files necessary.
Rails.root.glob("spec/support/**/*.rb").each { |f| require f }

# Checks for pending migrations and applies them before tests are run.
# If you are not using ActiveRecord, you can remove these lines.
begin
  ActiveRecord::Migration.maintain_test_schema!
rescue ActiveRecord::PendingMigrationError => e
  puts e.to_s.strip
  exit 1
end

SimpleCov.start("rails")

RSpec.configure do |config|
  # Remove this line if you're not using ActiveRecord or ActiveRecord fixtures
  # config.fixture_path = Rails.root.join('spec/fixtures')
  # config.global_fixtures = :all

  # If you're not using ActiveRecord, or you'd prefer not to run each of your
  # examples within a transaction, remove the following line or assign false
  # instead of true.
  config.use_transactional_fixtures = true
  # You can uncomment this line to turn off ActiveRecord support entirely.
  # config.use_active_record = false

  # RSpec Rails can automatically mix in different behaviours to your tests
  # based on their file location, for example enabling you to call `get` and
  # `post` in specs under `spec/controllers`.
  #
  # You can disable this behaviour by removing the line below, and instead
  # explicitly tag your specs with their type, e.g.:
  #
  #     RSpec.describe UsersController, type: :controller do
  #       # ...
  #     end
  #
  # The different available types are documented in the features, such as in
  # https://relishapp.com/rspec/rspec-rails/docs

  config.before(:suite) do
    DatabaseCleaner.strategy = :transaction
    DatabaseCleaner.clean_with(:truncation)
  end

  config.after(:each) do
    # Clean up RedLockPool after each test
    if RedLockPool.instance_variable_get(:@instance)
      # Just reset the instance variable without trying to close the mock
      RedLockPool.instance_variable_set(:@instance, nil)
    end
  end

  config.around(:each) do |example|
    DatabaseCleaner.cleaning do
      example.run
    end
  end

  config.infer_spec_type_from_file_location!

  # Filter lines from Rails gems in backtraces.
  config.filter_rails_from_backtrace!

  # prevent rspec to write return values to the console
  config.before(:each) do
    # allow($stdout).to receive(:write)
    allow(Redis).to receive(:new).and_return(MockRedis.new)
    allow_any_instance_of(ActionCable::Server::Base).to receive(:broadcast).and_return(nil)

    # Reset RedLockPool instance
    RedLockPool.instance_variable_set(:@instance, nil)

    # Add global lock service mock
    lock_client = instance_double(Redlock::Client)
    allow(Redlock::Client).to receive(:new).and_return(lock_client)
    allow(lock_client).to receive(:lock!).and_yield

    # Clean Redis before each test
    Redis.new.flushall

    # Stub ExpandedTitleLengthValidator to prevent external calls in all specs
    allow_any_instance_of(ExpandedTitleLengthValidator).to receive(:validate)
  end

  config.include FactoryBot::Syntax::Methods
  config.include Capybara::DSL
  config.include ActiveSupport::Testing::TimeHelpers
  config.include Devise::Test::ControllerHelpers, type: :controller
  config.include Devise::Test::IntegrationHelpers, type: :request

  config.before(:each) do
    stub_request(:any, /#{Regexp.escape(ENV.fetch("CENTRAL_EVENT_LOGGER_API_BASE_URL", nil))}/)
      .to_return(status: 200, body: { "status" => "success" }.to_json, headers: {})
    stub_request(:get, %r{/admin/oauth/access_scopes\.json})
      .to_return(status: 200, body: "", headers: {})
  end
end
