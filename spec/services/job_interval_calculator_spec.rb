# frozen_string_literal: true

require 'rails_helper'

RSpec.describe JobIntervalCalculator do
  describe '#calculate' do
    let(:index) { rand(1..100) }
    let(:interval) { ENV.fetch('IMPORT_JOB_INTERVAL', 4).to_i }

    context 'when items count is 0' do
      let(:items_count) { 0 }
      let(:service) { described_class.new(items_count) }
      let(:response) { service.calculate(index) }

      it 'should return correct percentage and interval' do
        next_run = response
        expect(next_run).to eq service.job_base_time + (index * interval).seconds
      end
    end

    context 'when items count not 0' do
      let(:items_count) { rand(1..100) }
      let(:service) { described_class.new(items_count) }
      let(:response) { service.calculate(index) }

      it 'should return correct percentage and interval' do
        next_run = response
        expect(next_run).to eq service.job_base_time + (index * interval).seconds
      end
    end
  end

  describe '#calculate_interval' do
    let(:index) { rand(1..100) }
    let(:items_count) { 0 }
    let(:interval) { ENV.fetch('IMPORT_JOB_INTERVAL', 4).to_i }

    let(:service) { described_class.new(items_count) }

    let(:response) { service.calculate_interval(index) }
    let(:correct_response) { service.job_base_time + (index * interval).seconds }

    it 'should return correct response' do
      expect(response).to eq correct_response
    end
  end
end
