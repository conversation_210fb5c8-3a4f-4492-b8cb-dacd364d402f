# frozen_string_literal: true

require 'rails_helper'

RSpec.describe WithLockService do
  describe '.call' do
    it 'executes the block within a lock' do
      result = WithLockService.call('test_lock') { 'test_result' }
      expect(result).to eq('test_result')
    end

    it 'raises ArgumentError when no block is given' do
      expect { WithLockService.call('test_lock') }.to raise_error(ArgumentError)
    end

    it 'handles lock errors' do
      lock_client = Redlock::Client.new([])  # Get the mocked instance
      allow(lock_client).to receive(:lock!).and_raise(Redlock::LockError.new('test'))

      expect(Honeybadger).to receive(:notify)
      expect { WithLockService.call('test_lock') { 'test_result' } }.to raise_error(Redlock::LockError)
    end
  end
end
