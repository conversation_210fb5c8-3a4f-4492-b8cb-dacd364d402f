# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Shopify::TenderTransactionsFetchService do
  let(:shop) { create(:shop, :with_transaction_setting) }
  let(:start_date) { Time.zone.today - 7 }
  let(:end_date) { Time.zone.today }
  let(:transaction_setting) { shop.transaction_setting }
  let(:service) { described_class.new(shop, start_date, end_date) }

  describe '#call' do
    let(:transactions) { [double('ShopifyAPI::TenderTransaction', payment_method: 'credit_card', id: 123)] }

    before do
      allow(shop).to receive(:with_shopify_session).and_yield
      allow(ShopifyAPI::TenderTransaction).to receive(:all).and_return(transactions)
      allow(ShopifyAPI::TenderTransaction).to receive(:next_page?).and_return(false)
    end

    it 'collects and filters transactions' do
      expect(service).to receive(:collect_transactions).and_call_original
      expect(service).to receive(:filter_transactions).and_call_original

      result = service.call

      expect(result[0].first.payment_method).to eq(transactions.first.payment_method)
      expect(result[0].first.id).to eq(transactions.first.id)
      expect(result[1]).to eq(0)
    end
  end

  describe 'private methods' do
    let(:transaction) { double('ShopifyAPI::TenderTransaction', payment_method: 'credit_card', id: 123) }

    describe '#collect_transactions' do
      let(:transactions) { [double('ShopifyAPI::TenderTransaction', payment_method: 'credit_card', id: 123)] }

      before do
        allow(ShopifyAPI::TenderTransaction).to receive(:all).and_return(transactions)
        allow(ShopifyAPI::TenderTransaction).to receive(:next_page?).and_return(false)
      end

      it 'fetches and stores transactions' do
        expect(shop).to receive(:with_shopify_session).and_yield
        result = service.send(:collect_transactions)
        expect(result[0].payment_method).to eq(transactions.first.payment_method)
        expect(result[0].id).to eq(transactions.first.id)
      end
    end

    describe '#filter_transactions' do
      let(:transactions) { [double('ShopifyAPI::TenderTransaction', payment_method: 'credit_card', id: 123)] }

      before do
        allow(shop).to receive(:with_shopify_session).and_yield
        allow(ShopifyAPI::TenderTransaction).to receive(:all).and_return(transactions)
        allow(ShopifyAPI::TenderTransaction).to receive(:next_page?).and_return(false)
      end

      before :each do
        allow(SyncInfo).to receive(:doc_exists?).and_return(false)
      end

      it 'filters transactions based on conditions' do
        allow(transaction_setting).to receive(:send).and_return(true)

        service.send(:collect_transactions)
        service.send(:filter_transactions)

        expect(service.instance_variable_get(:@transactions).first.id).to eq(transactions.first.id)
        expect(service.instance_variable_get(:@skipped_transactions)).to eq(0)
      end

      it 'skips transactions if conditions are not met' do
        allow(transaction_setting).to receive(:send).and_return(false)

        service.send(:collect_transactions)
        service.send(:filter_transactions)

        expect(service.instance_variable_get(:@transactions)).not_to be_empty
        expect(service.instance_variable_get(:@skipped_transactions)).to eq(0)
      end
    end
  end
end
