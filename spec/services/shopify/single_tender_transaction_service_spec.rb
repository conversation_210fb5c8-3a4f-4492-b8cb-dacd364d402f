# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Shopify::SingleTenderTransactionService, type: :service do
  let(:shop) { create(:shop) }
  let(:transaction_id) { '12345' }
  let(:service) { described_class.new(shop, transaction_id) }

  describe '#call' do
    context 'when the transaction exists' do
      it 'returns the transaction with the correct details' do
        transaction_data = Struct.new(:id, :paymentMethod, :processedAt, :remoteReference, :amount).new(
          '12345',
          'credit_card',
          '2023-10-01T12:00:00Z',
          'ref_12345',
          Struct.new(:amount, :currencyCode).new('100.00', 'USD')
        )
        allow(GetTenderTransaction).to receive(:call).and_return(transaction_data)

        result = service.call

        expect(result.id).to eq('12345')
        expect(result.paymentMethod).to eq('credit_card')
        expect(result.processedAt).to eq('2023-10-01T12:00:00Z')
        expect(result.remoteReference).to eq('ref_12345')
        expect(result.amount).to eq(BigDecimal('100.00'))
      end
    end
  end
end
