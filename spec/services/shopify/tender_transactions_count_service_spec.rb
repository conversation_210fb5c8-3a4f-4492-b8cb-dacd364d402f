# frozen_string_literal: true

require 'rails_helper'
RSpec.describe Shopify::TenderTransactionsCountService do
  let(:shop) { FactoryBot.create(:shop, :with_transaction_setting) }
  let(:start_date) { Time.zone.today }
  let(:end_date) { Time.zone.tomorrow }
  let(:service) { described_class.new(shop, start_date, end_date) }

  describe '#call' do
    it 'calls #fetch and returns the list of transactions' do
      allow(service).to receive(:count).and_return(2)
      expect(service.call).to eq 2
    end
  end

  describe '#count' do
    let(:transaction1) { double('ShopifyAPI::TenderTransaction', payment_method: 'credit_card') }
    let(:transaction2) { double('ShopifyAPI::TenderTransaction', payment_method: 'samsung_pay') }
    let(:transaction_page1) do
      double('Array', elements: [transaction1, transaction2], next_page?: true, count: 2)
    end
    let(:transaction3) { double('ShopifyAPI::TenderTransaction', payment_method: 'samsung_pay') }
    let(:transaction4) { double('ShopifyAPI::TenderTransaction', payment_method: 'credit_card') }
    let(:transaction_page2) do
      double('Array', elements: [transaction3, transaction4], next_page?: false, count: 2)
    end
    let(:transaction_page3) do
      double('Array', elements: [transaction1, transaction2], next_page?: true, count: 251)
    end

    it 'fetches all transactions within the specified date range as single page' do
      expect(shop).to receive(:with_shopify_session).and_yield

      allow(ShopifyAPI::TenderTransaction).to receive(:all).with(
        processed_at_min: start_date,
        processed_at_max: end_date,
        limit: 250
      ).and_return(transaction_page2)

      expect(service.send(:count)).to eq 2
    end

    it 'fetches all transactions within the specified date range without paginating' do
      expect(shop).to receive(:with_shopify_session).and_yield

      allow(ShopifyAPI::TenderTransaction).to receive(:all).with(
        processed_at_min: start_date,
        processed_at_max: end_date,
        limit: 250
      ).and_return(transaction_page3)

      expect(service.send(:count)).to eq 251
    end
  end
end
