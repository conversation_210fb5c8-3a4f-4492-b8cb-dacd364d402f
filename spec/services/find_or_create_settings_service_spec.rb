# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FindOrCreateSettingsService do
  let(:domain) { "#{SecureRandom.hex(8)}.myshopify.com" }
  let(:shopify_session) { ShopifyAPI::Auth::Session.new(shop: domain, access_token: "shpat_#{SecureRandom.hex(12)}") }
  let!(:remote_shop) { stub_shopify_shop_current }
  let(:shop) { create(:shop, shopify_domain: domain) }
  let(:service) { described_class.new(shopify_session) }

  before do
    allow(service).to receive(:report_event)
    # Let update_shop_info call through to the actual implementation
    allow(shop).to receive(:update_shop_info).and_call_original
  end

  describe '#call' do
    subject(:perform_call) { service.call }

    context 'when shop exists but has no settings' do
      before do
        shop # Create the shop
      end

      it 'creates shop settings' do
        expect { perform_call }.to change(ShopSetting, :count).by(1)
      end

      it 'creates transaction settings' do
        expect { perform_call }.to change(TransactionSetting, :count).by(1)
      end

      it 'reports the install event' do
        perform_call
        expect(service).to have_received(:report_event).with(
          event_name: 'app_installed',
          event_type: CentralEventLogger::EventTypes::USER_ACQUISITION,
          customer_myshopify_domain: domain,
          customer_info: {
            name: 'Test Shop',
            owner: 'John Doe',
            email: remote_shop.email
          },
          event_value: 'enterprise',
          payload: {
            shop_name: 'Test Shop',
            shop_owner: 'John Doe',
            country: 'DE',
            shopify_plan: 'enterprise',
            shopify_plan_display_name: 'Shopify Plus'
          },
          timestamp: anything,
          external_id: "install:#{shop.shopify_domain}"
        )
      end
    end

    context 'when shop exists with all settings' do
      before do
        create(:shop_setting, shop:)
        create(:transaction_setting, shop:)
      end

      it 'does not create additional settings' do
        expect { perform_call }.not_to change(ShopSetting, :count)
        expect { perform_call }.not_to change(TransactionSetting, :count)
      end

      it 'does not report install event' do
        perform_call
        expect(service).not_to have_received(:report_event)
      end
    end
  end
end
