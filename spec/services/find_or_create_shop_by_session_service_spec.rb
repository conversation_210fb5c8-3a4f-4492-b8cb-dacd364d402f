require 'rails_helper'

RSpec.describe FindOrCreateShopBySessionService do
  before do
    DatabaseCleaner.clean_with(:truncation)
    allow(ShopifyAPI::Shop).to receive(:current).and_return(OpenStruct.new({email: "<EMAIL>", name:"<PERSON><PERSON><PERSON>", country:"Germany", shop_owner:"TestOwner"}))
    allow_any_instance_of(ShopSetting).to receive(:handle_rules).and_return(nil)
  end

  shared_context 'initialize_variables' do
    let(:shop) { FactoryBot.create(:shop) }
    let(:shopify_session) { ShopifyAPI::Auth::Session.new(shop: shop.shopify_domain, access_token: shop.shopify_token) }
    let(:service) { described_class.new(shopify_session) }
  end

  describe '#call' do
    include_context 'initialize_variables'

    context 'when shop exists' do
      before do
        allow_any_instance_of(ShopifyAPI::Webhook).to receive(:save).and_return(nil)
      end

      context 'when shop setting is nil' do
        let(:response) { shop.with_shopify_session { service.call } }

        it 'should create shop setting' do
          expect { response }.to change { ShopSetting.count }.by(1)
          expect(response).to eq shop
        end
      end

      context 'when shop setting is present' do
        let(:response) { shop.with_shopify_session { service.call } }
        before do
          FactoryBot.create(:shop_setting, shop: shop)
        end

        it 'should not create shop setting' do
          expect { response }.to_not change(ShopSetting, :count)
          expect(response).to eq shop
        end
      end
    end
  end
end
