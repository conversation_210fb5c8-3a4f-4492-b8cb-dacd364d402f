# frozen_string_literal: true

require 'rails_helper'

RSpec.describe BulkQueryShopHasMultipleTaxLinesService do
  let(:service) { described_class.new }

  describe '#call' do
    context 'when shop has orders with multiple tax lines' do
      let(:orders) do
        [
          { 'id' => '1', 'taxLines' => [{ 'rate' => 0.19 }] },
          { 'id' => '2', 'taxLines' => [{ 'rate' => 0.19 }, { 'rate' => 0.07 }] },
          { 'id' => '1', 'taxLines' => [{ 'rate' => 0.19 }] }
        ]
      end

      before do
        allow(service).to receive(:fetch_orders).and_return(orders.map(&:to_json).join("\n"))
      end

      it 'returns true' do
        expect(service.call).to be true
      end
    end

    context 'when shop does not have orders with multiple tax lines' do
      let(:orders) do
        [
          { 'id' => '1', 'taxLines' => [{ 'rate' => 0.19 }] },
          { 'id' => '1', 'taxLines' => [{ 'rate' => 0.19 }] }
        ]
      end

      before do
        allow(service).to receive(:fetch_orders).and_return(orders.map(&:to_json).join("\n"))
      end

      it 'returns true' do
        expect(service.call).to be false
      end
    end
  end
end
