# frozen_string_literal: true

require "rails_helper"

RSpec.describe CreditNoteCreationService do
  let(:finalize) { false }
  let(:original_invoice_id) { SecureRandom.uuid }
  let(:credit_note) { Lexoffice::CreditNote.new(SecureRandom.hex(12)) }
  let(:shop) { create(:shop) }
  let(:sync_info) do
    double(doc_created?: false, target_id: nil, shop_id: shop.id, shopify_id: nil,
      extra_infos: { total_price: 123 })
  end
  let(:credit_note_id) { SecureRandom.uuid }

  subject { described_class.new(finalize, original_invoice_id, credit_note, sync_info) }

  before do
    allow(credit_note).to receive(:create).and_return(credit_note_id)
    allow(credit_note).to receive(:find)
    allow(sync_info).to receive(:target_doc_create)
    allow(sync_info).to receive(:add_doc_properties)
    stub_get_credit_note(create(:lexoffice_credit_note, id: credit_note_id))
    allow_any_instance_of(Shop).to receive(:shopify_plan).and_return(
      OpenStruct.new(displayName: "Shopify Basic", partnerDevelopment: false, shopifyPlus: false)
    )
    allow_any_instance_of(Shop).to receive(:billing_plan).and_return(double(features: [], name: "test"))
  end

  describe "#call" do
    context "when the doc is not created" do
      it "should create the credit note and sync_info" do
        subject.call
        expect(credit_note).to have_received(:create).with(finalize:, _original_invoice_id: original_invoice_id)
        expect(sync_info).to have_received(:target_doc_create).with(credit_note.create(finalize:,
          _original_invoice_id: original_invoice_id))
        expect(sync_info).to have_received(:add_doc_properties).with(credit_note)
        expect(credit_note).to have_received(:find).with(credit_note_id)
      end
    end

    context "when the doc is already created" do
      let(:sync_info) { double(doc_created?: true, target_id: SecureRandom.uuid, shop_id: shop.id) }

      it "should not recreate the credit note but should resync and add properties to the document" do
        subject.call
        expect(credit_note).not_to have_received(:create)
        expect(sync_info).not_to have_received(:target_doc_create)
        expect(sync_info).to have_received(:add_doc_properties).with(credit_note)
        expect(credit_note).to have_received(:find).with(sync_info.target_id)
      end
    end
  end

  describe "logging functionality" do
    let(:expected_event_data) do
      {
        event_name: "credit_note_created",
        event_type: "customer_usage",
        customer_myshopify_domain: shop.shopify_domain,
        event_value: 123,
        payload: {
          shopify_plan: { display_name: "Shopify Basic" },
          app_plan: shop.billing_plan.name
        },
        timestamp: anything,
        external_id: nil
      }
    end

    it "logs credit note creation with correct parameters" do
      expect(subject).to receive(:report_event).with(expected_event_data)
      subject.call
    end

    it "includes the correct timestamp in the log" do
      freeze_time do
        expect(subject).to receive(:report_event).with(
          hash_including(timestamp: Time.current)
        )
        subject.call
      end
    end

    it "does not log when credit note already exists" do
      allow(sync_info).to receive(:doc_created?).and_return(true)
      allow(sync_info).to receive(:target_id).and_return(credit_note_id)
      expect(subject).not_to receive(:report_event)
      subject.call
    end

    it "fetches the correct shop for logging" do
      expect(Shop).to receive(:find).with(sync_info.shop_id).and_return(shop)
      subject.call
    end
  end
end
