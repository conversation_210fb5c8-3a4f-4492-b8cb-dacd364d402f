# frozen_string_literal: true

require 'rails_helper'

RSpec.describe InvoiceCreationService do
  before do
    allow_any_instance_of(SyncRule).to receive(:handle_webhooks).and_return(nil)
  end

  describe '#call' do
    let(:shop) { FactoryBot.create(:shop) }
    let(:invoice) { Lexoffice::Invoice.new(shop.lexoffice_token) }
    let(:finalize) { [true, false].sample }
    let!(:sync_info) { FactoryBot.create(:sync_info, :fresh, shop:) }
    let(:order) { build(:order) }
    let(:shop_setting) { FactoryBot.create(:shop_setting, shop:) }
    let(:id) { SecureRandom.hex(12) }

    before do
      stub_request(:post, /invoices/).to_return(status: 200, body: { id: }.to_json)
      stub_request(:get, /invoice/).to_return(status: 200, body: { id: }.to_json, headers: {})
      allow_any_instance_of(Shop).to receive(:shopify_plan).and_return(
        OpenStruct.new(displayName: 'Shopify Basic', partnerDevelopment: false, shopifyPlus: false)
      )
      allow_any_instance_of(Shop).to receive(:billing_plan).and_return(double(features: [], name: 'test'))
    end

    context 'when send mail is true' do
      let(:send_mail) { true }
      let(:response) { described_class.new(invoice, finalize, sync_info, send_mail, order, shop_setting).call }

      it 'should create invoice' do
        expect { response }.to change(sync_info.reload, :last_action)
        expect(response.id).to_not be_nil
      end
    end

    context 'when send mail is false' do
      let(:send_mail) { false }
      let(:response) { described_class.new(invoice, finalize, sync_info, send_mail, order, shop_setting).call }

      it 'should create invoice' do
        expect { response }.to change(sync_info.reload, :last_action)
        expect(sync_info.last_action).to eq 'Created'
        expect(response.id).to_not be_nil
      end
    end

    context 'when error raised' do
      let(:send_mail) { [true, false].sample }
      let(:response) { described_class.new(invoice, finalize, sync_info, send_mail, order, shop_setting).call }

      before do
        allow_any_instance_of(Lexoffice::Invoice).to receive(:create).and_raise(RestClient::ExceptionWithResponse)
      end

      it 'should not delete sync info' do
        expect { response }.to raise_error(RestClient::ExceptionWithResponse).and change { SyncInfo.count }.by(0)
      end
    end

    context 'when doc was already created' do
      let(:send_mail) { [true, false].sample }
      let(:response) { described_class.new(invoice, finalize, sync_info, send_mail, order, shop_setting).call }
      let!(:sync_info) { create(:sync_info, :invoice_created, shop:) }

      it 'should not create invoice, but return existing invoice data' do
        expect { response }.to_not change(sync_info.reload, :last_action)
        expect(response.id).to eq id
      end
    end

    context 'logging functionality' do
      let(:send_mail) { false }
      let(:service) { described_class.new(invoice, finalize, sync_info, send_mail, order, shop_setting) }
      let(:expected_event_data) do
        {
          event_name: 'invoice_created',
          event_type: 'customer_usage',
          customer_myshopify_domain: shop.shopify_domain,
          event_value: nil,
          payload: {
            shopify_plan: { display_name: 'Shopify Basic' },
            app_plan: shop.billing_plan.name
          },
          timestamp: anything,
          external_id: order.id
        }
      end

      it 'logs invoice creation with correct parameters' do
        expect(service).to receive(:report_event).with(expected_event_data)
        service.call
      end

      it 'includes the correct timestamp in the log' do
        freeze_time do
          expect(service).to receive(:report_event).with(
            hash_including(timestamp: Time.current)
          )
          service.call
        end
      end

      it 'does not log when invoice already exists' do
        sync_info.update(last_action: 'Created', target_id: id)
        expect(service).not_to receive(:report_event)
        service.call
      end
    end
  end
end
