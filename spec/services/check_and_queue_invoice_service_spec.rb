# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SyncHelper::CheckAndQueueInvoiceService do
  describe 'call' do
    before do
      Sidekiq::Testing.fake!
      stub_request(:get, /webhooks.json/).to_return(status: 200, body: '', headers: {})
      stub_request(:post, /webhooks.json/).to_return(status: 200, body: '', headers: {})
      allow_any_instance_of(SyncRule).to receive(:handle_webhooks).and_return(nil)
      allow_any_instance_of(ShopSetting).to receive(:handle_rules).and_return(nil)
    end

    let(:shop) { create(:shop, :with_shop_setting) }
    let(:order_id) { 123_456_789 }

    context 'when invoice timing is orders/create' do
      it 'does not queue invoice creation' do
        shop.shop_setting.update(invoice_timing: 'orders/create')
        described_class.call(shop, order_id)
        expect(SyncOrderJob).to_not have_enqueued_sidekiq_job
      end
    end

    context 'when invoice timing is orders/fulfilled' do
      context 'when invoice info is not present' do
        it 'queues invoice creation' do
          shop.shop_setting.update(invoice_timing: 'orders/fulfilled')
          described_class.call(shop, order_id)
          expect(SyncOrderJob).to have_enqueued_sidekiq_job('orders/fulfilled', order_id, shop.id)
        end
      end

      context 'when invoice info is present' do
        context 'when invoice info has no target_id' do
          it 'queues invoice creation' do
            shop.shop_setting.update(invoice_timing: 'orders/fulfilled')
            create(:sync_info, shop:, shopify_id: order_id, target_type: 'Invoice', target_id: nil)
            described_class.call(shop, order_id)
            expect(SyncOrderJob).to have_enqueued_sidekiq_job('orders/fulfilled', order_id, shop.id)
          end
        end

        context 'when invoice info has a target_id' do
          it 'does not queue invoice creation' do
            shop.shop_setting.update(invoice_timing: 'orders/fulfilled')
            create(:sync_info, shop:, shopify_id: order_id, target_type: 'Invoice', target_id: 1)
            described_class.call(shop, order_id)
            expect(SyncOrderJob).to_not have_enqueued_sidekiq_job
          end
        end
      end
    end
  end
end
