# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FindAndMapExtraFinancialAccountsService do
  let(:transaction_params) { 10 }
  let(:shop) { FactoryBot.create(:shop, :with_transaction_setting) }
  let(:order) { double(ShopifyAPI::Order, id: 123) }
  let(:transaction) do
    double(ShopifyAPI::Transaction, gateway: 'stripe', order_id: order.id, kind: 'sale', amount: '10.0',
                                    status: 'success')
  end
  let(:failed_transaction) do
    double(ShopifyAPI::Transaction, gateway: 'stripe', order_id: order.id, kind: 'sale', amount: '10.0',
                                    status: 'failure')
  end

  subject(:service) { described_class.new(transaction_params, shop, order) }

  describe '#call' do
    context 'when transaction exists' do
      before do
        allow(shop).to receive(:with_shopify_session).and_yield
        expect(service).to receive(:order_transactions).and_return([transaction])
        allow(service).to receive(:extend_settings)
      end

      it 'finds and adds gateway' do
        expect(service.call).to eq transaction.gateway
      end

      it 'calls extend_settings with the found transaction' do
        service.call
        expect(service).to have_received(:extend_settings).with(transaction)
      end
    end

    context 'when transaction does not exist' do
      before do
        allow(shop).to receive(:with_shopify_session).and_yield
        expect(service).to receive(:order_transactions).and_return([])
        allow(service).to receive(:extend_settings)
      end

      it 'does not add gateway' do
        expect(service.call).to be_nil
      end

      it 'does not call extend_settings' do
        service.call
        expect(service).not_to have_received(:extend_settings)
      end
    end

    context 'when transaction failed' do
      before do
        allow(shop).to receive(:with_shopify_session).and_yield
        expect(service).to receive(:order_transactions).and_return([failed_transaction])
        allow(service).to receive(:extend_settings)
      end

      it 'does not add gateway' do
        expect(service.call).to be_nil
      end

      it 'does not call extend_settings' do
        service.call
        expect(service).not_to have_received(:extend_settings)
      end
    end
  end

  describe '#transaction_kind' do
    context 'when transaction amount is positive' do
      it 'returns "sale"' do
        expect(service.send(:transaction_kind)).to eq 'sale'
      end
    end

    context 'when transaction amount is negative' do
      let(:transaction_params) { -10.0 }

      it 'returns "refund"' do
        expect(service.send(:transaction_kind)).to eq 'refund'
      end
    end
  end

  describe '#extend_settings' do
    context 'when transaction is not nil' do
      before do
        allow(shop.transaction_setting).to receive(:extra_accounts_info).and_return({})
        allow(shop.transaction_setting).to receive(:update!)
      end

      it 'updates extra_accounts_info with the transaction gateway and enable_other value' do
        service.send(:extend_settings, transaction)
        expect(shop.transaction_setting.extra_accounts_info)
          .to eq({
                   "enable_#{transaction.gateway}" => shop.transaction_setting.enable_other,
                   "#{transaction.gateway}_account_id" => nil
                 })
      end

      it 'updates the shop transaction setting' do
        service.send(:extend_settings, transaction)
        expect(shop.transaction_setting).to have_received(:update!)
          .with(extra_accounts_info: shop.transaction_setting.extra_accounts_info)
      end
    end

    context 'when transaction is nil' do
      it 'does not update extra_accounts_info' do
        allow(shop.transaction_setting).to receive(:extra_accounts_info).and_return({})
        allow(shop.transaction_setting).to receive(:update!)

        service.send(:extend_settings, nil)

        expect(shop.transaction_setting.extra_accounts_info).to eq({})
        expect(shop.transaction_setting).not_to have_received(:update!)
      end
    end
  end
end
