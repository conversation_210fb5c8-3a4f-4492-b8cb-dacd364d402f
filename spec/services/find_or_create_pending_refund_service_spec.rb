# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FindOrCreatePendingRefundService, type: :service do
  let(:shop) { create(:shop) }
  let(:order) { build(:order, :with_refund) }
  let(:transaction_amount) { 100.0 }
  let(:refund) { order.refunds.first }
  let(:service) { described_class.new(order.id, transaction_amount, shop) }

  before do
    allow(shop).to receive(:with_shopify_session).and_yield
    allow(ShopifyAPI::Order).to receive(:find).with(id: order.id).and_return(order)
    allow(order).to receive(:refunds).and_return([refund])
  end

  describe '#call' do
    context 'when refund exists with matching transaction amount' do
      before do
        allow(order).to receive(:currency).and_return('EUR')
        allow(refund.transactions.first).to receive(:currency).and_return('EUR')
        allow(refund.transactions.first).to receive(:amount).and_return(transaction_amount)
        create(
          :sync_info,
          shopify_order_id: order.id,
          target_type: 'Refund',
          target_id: nil,
          shopify_id: refund.id,
          last_action: 'Pending Transaction'
        )
      end

      it 'queues a RefundJob' do
        expect(RefundJob).to receive(:perform_async).with('refunds/create', refund.id, order.id, shop.id)
        service.call
      end

      it 'deletes the error log' do
        error_log = create(:error_log, shopify_id: refund.id, shop_id: shop.id,
                                       error_info_internal: 'Refund has pending transactions')
        service.call
        expect(ErrorLog.find_by(id: error_log.id)).to be_nil
      end

      context 'when sync info exists with target_id' do
        let!(:sync_info) do
          create(:sync_info, shopify_order_id: order.id, target_type: 'Refund', target_id: 1, shopify_id: refund.id)
        end

        it 'returns nil' do
          expect(service.call).to be_nil
        end
      end

      context 'when sync info exists without target_id and no pending last action' do
        let!(:sync_info) do
          create(:sync_info, shopify_order_id: order.id, target_type: 'Refund', target_id: 1, shopify_id: refund.id,
                             last_action: 'Job started')
        end

        it 'returns nil' do
          expect(service.call).to be_nil
        end
      end
    end

    context 'when there is a currency mismatch and refund exists with matching adjustment amount' do
      before do
        allow(order).to receive(:currency).and_return('USD')
        allow(refund.transactions.first).to receive(:currency).and_return('EUR')
        allow(refund.transactions.first).to receive(:amount).and_return(50)
        adjustment = double('adjustment')
        allow(adjustment).to receive(:deep_symbolize_keys).and_return(amount: transaction_amount)
        allow(refund).to receive(:order_adjustments).and_return([adjustment])
        create(
          :sync_info,
          shopify_order_id: order.id,
          target_type: 'Refund',
          target_id: nil,
          shopify_id: refund.id,
          last_action: 'Pending Transaction'
        )
      end

      it 'queues a RefundJob' do
        expect(RefundJob).to receive(:perform_async).with('refunds/create', refund.id, order.id, shop.id)
        service.call
      end
    end

    context 'when refund exists with matching subtotal of refund_line_items' do
      before do
        allow(order).to receive(:currency).and_return('USD')
        allow(refund.transactions.first).to receive(:currency).and_return('EUR')
        allow(refund.transactions.first).to receive(:amount).and_return(100.0)
        allow(refund.order_adjustments).to receive(:any?).and_return([])
        refund_line_item = double('refund_line_item', subtotal: transaction_amount)
        allow(refund).to receive(:refund_line_items).and_return([refund_line_item])
        allow(refund).to receive(:calculate_subtotal_amount).and_return(transaction_amount)
        create(
          :sync_info,
          shopify_order_id: order.id,
          target_type: 'Refund',
          target_id: nil,
          shopify_id: refund.id,
          last_action: 'Pending Transaction'
        )
      end

      it 'queues a RefundJob based on subtotal matching' do
        expect(RefundJob).to receive(:perform_async).with('refunds/create', refund.id, order.id, shop.id)
        service.call
      end
    end

    context 'when refund does not exist with matching transaction amount or adjustment amount' do
      let(:transaction_amount) { 200.0 }

      before do
        allow(order).to receive(:currency).and_return('EUR')
        allow(refund.transactions.first).to receive(:currency).and_return('CHF')
        allow(refund.transactions.first).to receive(:amount).and_return(100.0)
        adjustment = double('adjustment')
        allow(adjustment).to receive(:deep_symbolize_keys).and_return(amount: 100.0)
        allow(refund).to receive(:order_adjustments).and_return([adjustment])
        create(
          :sync_info,
          shopify_order_id: order.id,
          target_type: 'Refund',
          target_id: nil,
          shopify_id: refund.id,
          last_action: 'Pending Transaction'
        )
      end

      it 'returns nil' do
        expect(service.call).to be_nil
      end
    end
  end
end
