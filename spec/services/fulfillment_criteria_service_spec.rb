# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FulfillmentCriteriaService do
  before do
    stub_shopify_requests('davesdevelopmentstore')
  end
  let(:shop) { build(:shop) }

  let(:pending_order) do
    build(:order,
          :with_line_items_and_refund_id,
          :with_shipping_lines,
          :with_refund,
          total_shipping_price_set: { 'shop_money' => { 'amount' => '10.00', 'currency_code' => 'EUR' } },
          total_price: '20.00',
          current_total_price: '10.00',
          total_refund: 0,
          financial_status: 'pending')
  end

  let(:order_paid_with_no_total_outstanding) do
    build(:order, :with_line_items_and_refund_id, :with_refund, financial_status: 'paid', total_outstanding: '0.00')
  end

  let(:order_paid_with_total_outstanding) do
    build(:order, :with_discounted_line_items, :with_refund,
          financial_status: 'paid', total_outstanding: '10.00', current_total_discounts: '10.00')
  end

  let(:order_partially_paid) do
    build(:order,
          :with_line_items_and_refund_id,
          :with_refund,
          total_shipping_price_set: { 'shop_money' => { 'amount' => '10.00', 'currency_code' => 'EUR' } },
          financial_status: 'partially_paid')
  end

  describe '#call' do
    context 'when order has refunds, financial status is pending, and no transactions' do
      let(:service) { described_class.new(pending_order, shop) }

      it 'sets use_fulfill_criteria to true' do
        service.call
        expect(pending_order.instance_variable_get(:@use_fulfill_criteria)).to eq(true)
      end

      it 'removes shipping lines from order if shipping has been refunded' do
        service.call
        expect(pending_order.shipping_lines).to eq([])
      end
    end

    context 'when order has refunds, financial status is pending, and with transactions' do
      let(:order) { build(:order, :with_transactions, :with_refund, financial_status: 'pending') }
      let(:service) { described_class.new(order, shop) }

      it 'sets use_fulfill_criteria to false' do
        service.call
        expect(order.instance_variable_get(:@use_fulfill_criteria)).to eq(false)
      end
    end

    context 'when order has refunds, paid and total outstanding is 0.00' do
      let(:service) { described_class.new(order_paid_with_no_total_outstanding, shop) }

      it 'sets use_fulfill_criteria to true' do
        service.call
        expect(order_paid_with_no_total_outstanding.instance_variable_get(:@use_fulfill_criteria)).to eq(true)
      end
    end

    context 'when order has refunds, paid and total outstanding is 10.00' do
      let(:service) { described_class.new(order_paid_with_total_outstanding, shop) }

      it 'sets use_fulfill_criteria to false' do
        service.call
        expect(order_paid_with_total_outstanding.instance_variable_get(:@use_fulfill_criteria)).to eq(false)
      end
    end

    context 'when order has refunds and financial status is partially paid' do
      let(:service) { described_class.new(order_partially_paid, shop) }

      it 'sets use_fulfill_criteria to true' do
        service.call
        expect(order_partially_paid.instance_variable_get(:@use_fulfill_criteria)).to eq(true)
      end
    end
  end

  describe '#no_transactions?' do
    context 'when order has no successful transactions' do
      let(:order) { build(:order, :with_transactions, transactions: [build(:shopify_transaction, :pending)]) }
      let(:service) { described_class.new(order, shop) }

      it 'returns true' do
        expect(service.send(:no_transactions?)).to eq(true)
      end
    end

    context 'when order has no transactions' do
      let(:order) { build(:order, :without_transactions) }
      let(:service) { described_class.new(order, shop) }

      it 'returns true' do
        expect(service.send(:no_transactions?)).to eq(true)
      end
    end

    context 'when order has successful transactions' do
      let(:order) { build(:order, :with_transactions) }
      let(:service) { described_class.new(order, shop) }

      it 'returns false' do
        expect(service.send(:no_transactions?)).to eq(false)
      end
    end
  end
end
