# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SetAbsoluteDiscountService do
  describe '#call' do
    let!(:shop) { create(:shop, :with_shop_setting) }
    let(:invoice) { build_invoice(shop.lexoffice_token) }
    let(:settings) { shop.shop_setting }
    let(:order) do
      build(:order, :with_line_items, :with_shipping_lines, total_discounts: '10.00', current_total_discounts: '8.00')
    end

    subject { described_class.new(shop, order, invoice, settings) }
    before { allow(order).to receive(:use_fulfillment?).and_return(false) }

    context 'when using live orders' do
      context 'when order is not partially refunded' do
        before do
          allow(order).to receive(:fulfillment_status).and_return('fulfilled')
          allow(order).to receive(:financial_status).and_return('paid')
        end

        it 'sets the discount if discount_difference is positive' do
          allow(order).to receive(:line_items_total_discount).and_return(6.00)
          expect(invoice).to receive(:set_discount).with(2.00)
          subject.call
        end

        it 'does not set the discount if discount_difference is zero or negative' do
          allow(order).to receive(:line_items_total_discount).and_return(8.00)
          expect(invoice).not_to receive(:set_discount)
          subject.call
        end
      end

      context 'when order is partially refunded' do
        before do
          allow(order).to receive(:fulfillment_status).and_return('fulfilled')
          allow(order).to receive(:financial_status).and_return('partially_refunded')
        end

        it 'uses total_discounts instead of current_total_discounts' do
          allow(order).to receive(:line_items_total_discount).and_return(6.00)
          expect(invoice).to receive(:set_discount).with(4.00)
          subject.call
        end
      end
    end

    context 'when using fulfilled orders' do
      before do
        allow(order).to receive(:use_fulfillment?).and_return(true)
        allow(settings).to receive(:invoice_timing_fulfill?).and_return(true)
      end

      it 'sets the discount if discount_difference is positive' do
        fulfillment = build_fulfillment_with_line_items([{ discount_amounts: [2.00] }])
        allow(order).to receive(:fulfillments).and_return([fulfillment])
        allow(order).to receive(:discount_applications).and_return([{ allocation_method: 'each' }])
        expect(invoice).to receive(:set_discount).with(6.00)
        subject.call
      end

      it 'does not set the discount if discount_difference is zero or negative' do
        fulfillment = build_fulfillment_with_line_items([{ discount_amounts: [8.00] }])
        allow(order).to receive(:fulfillments).and_return([fulfillment])
        allow(order).to receive(:discount_applications).and_return([{ allocation_method: 'each' }])
        expect(invoice).not_to receive(:set_discount)
        subject.call
      end

      it 'handles multiple fulfillments correctly' do
        fulfillments = [
          build_fulfillment_with_line_items([{ discount_amounts: [2.00] }]),
          build_fulfillment_with_line_items([{ discount_amounts: [1.00] }])
        ]
        allow(order).to receive(:fulfillments).and_return(fulfillments)
        allow(order).to receive(:discount_applications)
          .and_return([{ allocation_method: 'each' }, { allocation_method: 'each' }])
        expect(invoice).to receive(:set_discount).with(5.00)
        subject.call
      end
    end

    context 'when invoice_timing_fulfill is false' do
      before do
        allow(order).to receive(:use_fulfillment?).and_return(true)
        allow(settings).to receive(:invoice_timing_fulfill?).and_return(false)
      end

      it 'uses live order logic even with fulfillment enabled' do
        allow(order).to receive(:line_items_total_discount).and_return(6.00)
        expect(invoice).to receive(:set_discount).with(2.00)
        subject.call
      end
    end
  end

  describe '#calculate_fulfillment_discounts' do
    let(:order) { build(:order) }
    let(:shop) { build(:shop, :with_shop_setting) }
    let(:invoice) { build_invoice(shop.lexoffice_token) }

    it 'calculates the total discounts from fulfillments' do
      fulfillment = build_fulfillment_with_line_items([
                                                        { discount_amounts: [2.00, 3.00] },
                                                        { discount_amounts: [1.00] }
                                                      ])
      allow(order).to receive(:fulfillments).and_return([fulfillment])
      allow(order).to receive(:discount_applications)
        .and_return([{ allocation_method: 'each' }, { allocation_method: 'each' }])

      service = described_class.new(shop, order, invoice, shop.shop_setting)
      total_discounts = service.send(:calculate_fulfillment_discounts, order)

      expect(total_discounts).to eq(6.00)
    end

    it 'handles empty fulfillments' do
      allow(order).to receive(:fulfillments).and_return([])
      allow(order).to receive(:discount_applications).and_return([])

      service = described_class.new(shop, order, invoice, shop.shop_setting)
      total_discounts = service.send(:calculate_fulfillment_discounts, order)

      expect(total_discounts).to eq(0.00)
    end
  end
end
