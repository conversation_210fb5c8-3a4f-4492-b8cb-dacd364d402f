# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CreateShopWithSettingsService do
  let!(:service_instance) { create(:service_instance, :with_default_id) } # Service requires service instance with id 1
  let(:domain) { "test-shop-#{SecureRandom.hex(8)}.myshopify.com" }
  let(:shopify_session) { ShopifyAPI::Auth::Session.new(shop: domain, access_token: "shpat_#{SecureRandom.hex(12)}") }
  let(:email) { "#{SecureRandom.hex(4)}@example.com" }
  let(:remote_shop) do
    instance_double(
      ShopifyAPI::Shop,
      email:,
      name: 'Test Shop',
      country: 'DE',
      shop_owner: '<PERSON>',
      plan_name: 'enterprise',
      plan_display_name: 'Shopify Plus'
    )
  end
  let!(:billing_plan) { create(:billing_plan, :with_default_id, id: 0, name: 'FreePlan') }
  let(:service) { described_class.new(shopify_session) }
  let(:temp_shop) { build(:shop, shopify_domain: domain, service_instance:) }

  before do
    # Clean up any existing shops with this domain
    Shop.where(shopify_domain: domain).destroy_all

    stub_shopify_authenticated(temp_shop)
    allow_any_instance_of(ShopSetting).to receive(:handle_rules).and_return(nil)
    allow(ShopifyAPI::Shop).to receive(:current).and_return(remote_shop)
    allow(service).to receive(:report_event)
  end

  describe '#call' do
    it 'creates a new shop with correct domain' do
      created_shop = nil
      expect { created_shop = service.call }.to change(Shop, :count).by(1)
      expect(created_shop.shopify_domain).to eq(domain)
    end

    it 'creates shop settings' do
      expect { service.call }.to change(ShopSetting, :count).by(1)
    end

    it 'creates transaction settings' do
      expect { service.call }.to change(TransactionSetting, :count).by(1)
    end

    it 'reports the install event' do
      created_shop = service.call
      expect(service).to have_received(:report_event).with(
        event_name: 'app_installed',
        event_type: CentralEventLogger::EventTypes::USER_ACQUISITION,
        customer_myshopify_domain: created_shop.shopify_domain,
        customer_info: {
          name: 'Test Shop',
          owner: 'John Doe',
          email:
        },
        event_value: 'enterprise',
        payload: {
          shop_name: 'Test Shop',
          shop_owner: 'John Doe',
          country: 'DE',
          shopify_plan: 'enterprise',
          shopify_plan_display_name: 'Shopify Plus'
        },
        timestamp: anything,
        external_id: "install:#{created_shop.shopify_domain}"
      )
    end

    context 'with remote shop attributes' do
      it 'updates shop info attributes' do
        created_shop = service.call
        expect(created_shop.email).to eq(email)
      end
    end
  end
end
