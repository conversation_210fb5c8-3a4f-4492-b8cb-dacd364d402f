# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CreateOrGetContactService do
  let(:shop) { create(:shop, :with_shop_setting, lexoffice_token: "valid_token") }
  let(:order) { build(:order, :with_shipping_address, :with_billing_address) }
  let(:tax_type) { 'net' }
  let(:contact_data) { { name: 'Test Contact' } }
  let(:contact_response) { { id: 'contact123' } }
  let(:service) { described_class.new(order, shop, tax_type) }

  before do
    allow(ContactRequiredService).to receive(:call).with(order, shop).and_return(true)
    allow(Lexoffice::ContactDataService)
      .to receive(:new).with(order, shop.shop_setting.use_shipping_address_for_invoices)
      .and_return(double(call: contact_data))
    allow(Lexoffice::Contact).to receive(:new).and_return(double(find_or_create: contact_response))
    allow(shop).to receive(:refresh_token_if_expired)
  end

  describe '#call' do
    it 'creates or gets the contact' do
      expect(service.call).to eq(contact_response)
    end

    it 'refreshes the shop token' do
      service.call
      expect(shop).to have_received(:refresh_token_if_expired)
    end

    context 'when contact is not required' do
      before do
        allow(ContactRequiredService).to receive(:call).with(order, shop).and_return(false)
      end

      it 'does not create a contact' do
        service.call
        expect(Lexoffice::Contact).not_to have_received(:new)
      end
    end
  end
end
