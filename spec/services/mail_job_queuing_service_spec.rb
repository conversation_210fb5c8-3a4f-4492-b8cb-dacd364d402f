# frozen_string_literal: true

require 'rails_helper'

RSpec.describe MailJobQueuingService do
  describe '#call' do
    let(:sync_info) { FactoryBot.create(:sync_info) }
    before do
      allow_any_instance_of(SyncRule).to receive(:handle_webhooks).and_return(nil)
      allow(Redis).to receive(:new).and_return(MockRedis.new)
    end

    context 'when offset is zero' do
      let(:settings) { FactoryBot.create(:shop_setting, invoice_mail_offset_days: 0) }
      let(:service) { described_class.new(settings, sync_info) }

      it 'should call SendMailJob immediately' do
        described_class.call(settings, sync_info)
        expect(SendMailJob).to have_enqueued_sidekiq_job(sync_info.id)
        expect(sync_info.reload.last_action).to eq('Send Mail Job Queued')
      end
    end

    context 'when offset is greater than zero' do
      let(:settings) { FactoryBot.create(:shop_setting, invoice_mail_offset_days: 2) }
      let(:service) { described_class.new(settings, sync_info) }

      it 'should call SendMailJob in offset days' do
        freeze_time do
          described_class.call(settings, sync_info)

          # Calculate the expected time exactly 2 days from the frozen time
          expected_time = 2.days.from_now.to_i

          # Verify the job is enqueued within a 1-hour range to account for DST shifts
          enqueued_job = SendMailJob.jobs.find { |job| job['args'] == [sync_info.id] }
          expect(enqueued_job).not_to be_nil
          expect(enqueued_job['at'].to_i).to be_within(1.hour).of(expected_time)
          expect(sync_info.reload.last_action).to eq('Send Mail Job Queued')
        end
      end
    end
  end
end
