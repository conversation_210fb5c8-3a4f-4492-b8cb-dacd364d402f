# frozen_string_literal: true

require "rails_helper"

RSpec.describe Support::ApplyDiscountService do
  let(:shop) { create(:shop) }
  let(:discount) { 10 }
  let(:service) { described_class.new(shop, plan_type, discount) }

  describe ".call" do
    context "with import plan type" do
      let(:plan_type) { "import" }

      it "applies import discount" do
        expect(shop).to receive(:update!).with(import_discount_percent: discount)

        result = service.call
        expect(result).to eq({ success: true })
      end
    end

    context "with app plan type" do
      let(:plan_type) { "app" }

      it "applies regular discount" do
        expect(shop).to receive(:update!).with(discount_percent: discount)

        result = service.call
        expect(result).to eq({ success: true })
      end
    end

    context "when update fails" do
      let(:plan_type) { "import" }

      it "raises an ActiveRecord::RecordInvalid error" do
        allow(shop).to receive(:update!).and_raise(ActiveRecord::RecordInvalid.new(shop))

        expect { service.call }.to raise_error(ActiveRecord::RecordInvalid)
      end
    end

    context "with different discount values" do
      let(:plan_type) { "app" }

      [0, 50, 100].each do |discount_value|
        it "accepts #{discount_value}% discount" do
          service = described_class.new(shop, plan_type, discount_value)
          expect(shop).to receive(:update!).with(discount_percent: discount_value)

          service.call
        end
      end
    end
  end
end
