# frozen_string_literal: true

require "rails_helper"

RSpec.describe Support::RetryTransfersService do
  let(:shop) { create(:shop) }
  let(:error_log) { create(:error_log, shop:) }
  let(:service) { described_class.new(shop, retry_all, single_error_id, order_ids) }
  let(:retry_all) { false }
  let(:single_error_id) { nil }
  let(:order_ids) { nil }

  describe ".call" do
    before do
      allow_any_instance_of(ErrorLog).to receive(:retry_job).and_return(true)
    end

    context "when retrying all errors" do
      let(:retry_all) { true }

      it "retries all errors for the shop" do
        create_list(:error_log, 3, shop:)

        result = service.call
        expect(result[:job_count]).to eq(3)
      end
    end

    context "when retrying a single error" do
      let(:single_error_id) { error_log.id }

      it "retries the specific error" do
        error_log # Create the error log

        result = service.call
        expect(result[:job_count]).to eq(1)
      end
    end

    context "when retrying errors for specific orders" do
      let(:order_ids) { %w[123 456] }

      it "retries errors for the specified orders" do
        create_list(:error_log, 2, shop:, order_id: order_ids.first)

        result = service.call
        expect(result[:job_count]).to eq(2)
      end
    end
  end
end
