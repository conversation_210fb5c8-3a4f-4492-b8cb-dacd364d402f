# frozen_string_literal: true

require "rails_helper"

RSpec.describe Support::DeleteEntitiesService do
  let(:shop) { create(:shop) }
  let(:entity_type) { "SyncInfo" }
  let(:entity_ids) { %w[123 456] }
  let(:delete_all) { false }
  let(:service) { described_class.new(shop, entity_type, entity_ids, delete_all) }

  describe ".call" do
    context "with invalid entity type" do
      let(:entity_type) { "InvalidType" }

      it "raises an ArgumentError" do
        expect { service.call }.to raise_error(ArgumentError, "Invalid entity type: #{entity_type}")
      end
    end

    context "with SyncInfo entity type" do
      let(:entity_type) { "SyncInfo" }

      context "when deleting specific entities" do
        let(:delete_all) { false }

        it "deletes only specified sync infos" do
          sync_infos = create_list(:sync_info, 3, shop:, shopify_order_id: entity_ids.first)
          other_sync_info = create(:sync_info, shop:, shopify_order_id: 1234)

          result = service.call

          expect(result).to eq({ success: true })
          expect(SyncInfo.where(id: sync_infos.map(&:id))).not_to exist
          expect(SyncInfo.find_by(id: other_sync_info.id)).to be_present
        end
      end

      context "when deleting all entities" do
        let(:delete_all) { true }

        it "deletes all sync infos for the shop" do
          sync_infos = create_list(:sync_info, 3, shop:)
          other_shop_sync_info = create(:sync_info)

          result = service.call

          expect(result).to eq({ success: true })
          expect(SyncInfo.where(id: sync_infos.map(&:id))).not_to exist
          expect(SyncInfo.find_by(id: other_shop_sync_info.id)).to be_present
        end
      end
    end

    context "with Error entity type" do
      let(:entity_type) { "Error" }

      context "when deleting specific entities" do
        let(:delete_all) { false }

        it "deletes only specified error logs" do
          error_logs = create_list(:error_log, 3, shop:, order_id: entity_ids.first)
          other_error_log = create(:error_log, shop:, order_id: 1234)

          result = service.call

          expect(result).to eq({ success: true })
          expect(ErrorLog.where(id: error_logs.map(&:id))).not_to exist
          expect(ErrorLog.find_by(id: other_error_log.id)).to be_present
        end
      end

      context "when deleting all entities" do
        let(:delete_all) { true }

        it "deletes all error logs for the shop" do
          error_logs = create_list(:error_log, 3, shop:)
          other_shop_error_log = create(:error_log)

          result = service.call

          expect(result).to eq({ success: true })
          expect(ErrorLog.where(id: error_logs.map(&:id))).not_to exist
          expect(ErrorLog.find_by(id: other_shop_error_log.id)).to be_present
        end
      end
    end
  end
end
