# frozen_string_literal: true

require "rails_helper"

RSpec.describe Support::OrderDetailsService do
  let(:shop) { create(:shop) }
  let(:order_id) { "123456" }
  let(:service) { described_class.new(shop, order_id) }
  let(:shopify_transactions) { [double("transaction")] }
  let(:processed_at) { Time.current }
  let(:shopify_order) do
    double("order",
      id: order_id,
      transactions: shopify_transactions,
      processed_at:)
  end
  let(:tender_transaction) { double("tender_transaction", order_id:) }

  before do
    allow(shop).to receive(:with_shopify_session).and_yield
    allow(ShopifyAPI::Order).to receive(:find).with(id: order_id).and_return(shopify_order)
    allow(ShopifyAPI::TenderTransaction).to receive(:all).and_return([tender_transaction])
  end

  describe ".call" do
    let!(:sync_info) { create(:sync_info, shop:, shopify_order_id: order_id) }
    let!(:error_log) { create(:error_log, shop:, order_id:) }

    it "returns order details with all required data" do
      result = service.call

      expect(result).to include(
        :order,
        :transactions,
        :tender_transaction,
        :sync_infos,
        :errors
      )
    end

    it "fetches sync infos correctly" do
      result = service.call

      expect(result[:sync_infos]).to include(sync_info)
    end

    it "fetches error logs correctly" do
      result = service.call

      expect(result[:errors]).to include(error_log)
    end

    it "fetches Shopify order data correctly" do
      result = service.call

      expect(result[:order]).to eq(shopify_order)
      expect(result[:transactions]).to eq(shopify_transactions)
    end

    context "when fetching tender transactions" do
      it "searches within the correct date range" do
        service.call

        min_date = (processed_at.to_date - 1.day).strftime("%Y-%m-%d")
        max_date = (processed_at.to_date + 1.day).strftime("%Y-%m-%d")

        expect(ShopifyAPI::TenderTransaction).to have_received(:all).with(
          processed_at_min: min_date,
          processed_at_max: max_date,
          order: "processed_at ASC",
          limit: 250
        )
      end

      context "when no matching tender transaction is found" do
        let(:tender_transaction) { double("tender_transaction", order_id: "different_id") }

        it "returns an empty hash for tender_transaction" do
          result = service.call

          expect(result[:tender_transaction]).to eq({})
        end
      end
    end

    context "when Shopify API calls fail" do
      before do
        allow(ShopifyAPI::Order).to receive(:find).and_raise(StandardError)
      end

      it "raises the error" do
        expect { service.call }.to raise_error(StandardError)
      end
    end
  end
end
