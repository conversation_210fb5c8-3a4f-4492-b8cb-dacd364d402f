# frozen_string_literal: true

require "rails_helper"

RSpec.describe Support::CountService do
  let(:shop) { create(:shop) }
  let(:start_date) { Time.zone.today }
  let(:end_date) { Time.zone.today + 1.day }
  let(:service) { described_class.new(shop, start_date, end_date) }
  let(:formatted_start_date) { (start_date + 1.day).strftime("%FT%T%:z") }
  let(:formatted_end_date) { (end_date + 1.day).strftime("%FT%T%:z") }

  before do
    allow(shop).to receive(:with_shopify_session).and_yield
  end

  describe ".call" do
    let(:order_count_response) { double("response", body: { "count" => 5 }) }
    let(:refunded_count_response) { double("response", body: { "count" => 2 }) }
    let(:partially_refunded_count_response) { double("response", body: { "count" => 3 }) }

    before do
      allow(ShopifyAPI::Order).to receive(:count).with(
        created_at_min: formatted_start_date,
        created_at_max: formatted_end_date,
        status: "any"
      ).and_return(order_count_response)

      allow(ShopifyAPI::Order).to receive(:count).with(
        created_at_min: formatted_start_date,
        created_at_max: formatted_end_date,
        financial_status: "refunded",
        status: "any"
      ).and_return(refunded_count_response)

      allow(ShopifyAPI::Order).to receive(:count).with(
        created_at_min: formatted_start_date,
        created_at_max: formatted_end_date,
        financial_status: "partially_refunded",
        status: "any"
      ).and_return(partially_refunded_count_response)

      allow(Shopify::TenderTransactionsCountService).to receive(:call)
        .with(shop, start_date.next_day, end_date.next_day)
        .and_return(10)
    end

    it "returns counts for orders, refunds, and transactions" do
      result = service.call

      expect(result).to eq({
        orders: 5,
        refunds: 5, # 2 refunded + 3 partially refunded
        transactions: 10
      })
    end

    it "formats dates correctly" do
      service.call

      expect(ShopifyAPI::Order).to have_received(:count).exactly(3).times.with(
        hash_including(
          created_at_min: formatted_start_date,
          created_at_max: formatted_end_date
        )
      )
    end

    it "calls TenderTransactionsCountService with correct parameters" do
      service.call

      expect(Shopify::TenderTransactionsCountService).to have_received(:call)
        .with(shop, start_date.next_day, end_date.next_day)
    end

    context "when API returns zero counts" do
      let(:order_count_response) { double("response", body: { "count" => 0 }) }
      let(:refunded_count_response) { double("response", body: { "count" => 0 }) }
      let(:partially_refunded_count_response) { double("response", body: { "count" => 0 }) }

      before do
        allow(Shopify::TenderTransactionsCountService).to receive(:call)
          .with(shop, start_date.next_day, end_date.next_day)
          .and_return(0)
      end

      it "returns zero for all counts" do
        result = service.call

        expect(result).to eq({
          orders: 0,
          refunds: 0,
          transactions: 0
        })
      end
    end
  end
end
