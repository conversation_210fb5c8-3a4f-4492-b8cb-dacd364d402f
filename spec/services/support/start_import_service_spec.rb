# frozen_string_literal: true

require "rails_helper"

RSpec.describe Support::StartImportService do
  let(:shop) { create(:shop) }
  let(:start_date) { Time.zone.today }
  let(:end_date) { Time.zone.today + 1.day }
  let(:import_options) { { invoices: true, refunds: false, transactions: false } }
  let(:locale) { "de" }
  let(:service) { described_class.new(shop, start_date, end_date, import_options, locale) }

  before do
    allow(shop).to receive(:plan_active?).and_return(true)
    allow(shop).to receive(:has_running_import?).and_return(false)
    allow(I18n).to receive(:t).with("import.message.plan_not_active").and_return("Plan is not active")
    allow(I18n).to receive(:t).with("import.message.already_running").and_return("Import is already running")
    allow(I18n).to receive(:t).with("import.message.nothing_to_import").and_return("Nothing to import")
  end

  describe ".call" do
    context "when all conditions are met" do
      before do
        allow(SyncAllJob).to receive(:perform_async).and_return("job_123")
      end

      it "starts the import job" do
        expect(SyncAllJob).to receive(:perform_async).with(
          shop.id,
          start_date.next_day,
          end_date.next_day,
          import_options,
          locale
        ).and_return("job_123")

        result = service.call
        expect(result).to eq({ job_id: "job_123" })
      end
    end

    context "when plan is not active" do
      before do
        allow(shop).to receive(:plan_active?).and_return(false)
      end

      it "raises an ImportError" do
        expect { service.call }.to raise_error(
          described_class::ImportError,
          "Plan is not active"
        )
      end
    end

    context "when import is already running" do
      before do
        allow(shop).to receive(:has_running_import?).and_return(true)
      end

      it "raises an ImportError" do
        expect { service.call }.to raise_error(
          described_class::ImportError,
          "Import is already running"
        )
      end
    end

    context "when no import options are selected" do
      let(:import_options) { { invoices: false, refunds: false, transactions: false } }

      it "raises an ImportError" do
        expect { service.call }.to raise_error(
          described_class::ImportError,
          "Nothing to import"
        )
      end
    end

    context "with different import options" do
      before do
        allow(SyncAllJob).to receive(:perform_async).and_return("job_123")
      end

      it "accepts refunds only" do
        service = described_class.new(shop, start_date, end_date, { refunds: true }, locale)
        result = service.call
        expect(result).to eq({ job_id: "job_123" })
      end

      it "accepts transactions only" do
        service = described_class.new(shop, start_date, end_date, { transactions: true }, locale)
        result = service.call
        expect(result).to eq({ job_id: "job_123" })
      end

      it "accepts multiple options" do
        service = described_class.new(
          shop,
          start_date,
          end_date,
          { invoices: true, refunds: true, transactions: true },
          locale
        )
        result = service.call
        expect(result).to eq({ job_id: "job_123" })
      end
    end
  end
end
