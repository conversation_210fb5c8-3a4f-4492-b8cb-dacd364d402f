# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CreateFinancialAccountService do
  describe '#call' do
    let(:transaction_setting) { create(:transaction_setting, credit_card_account_id: nil) }
    let(:financial_account_endpoint) { Lexoffice::FinancialAccount.new('some_token', 'some_type') }
    let(:account_id) { SecureRandom.hex }

    before do
      allow_any_instance_of(Lexoffice::FinancialAccount).to receive(:create).and_return({ 'id' => account_id })
      described_class.call(transaction_setting, financial_account_endpoint, 'credit_card_account_id')
    end

    it 'updates transaction setting' do
      expect(transaction_setting.credit_card_account_id).to eq(account_id)
    end
  end
end
