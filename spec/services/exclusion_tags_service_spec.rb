# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ExclusionTagsService do
  let(:shop) { create(:shop, :with_shop_setting) }
  let(:billing_plan) { nil }
  let!(:order) { build(:order, shop:) }

  before do
    allow(shop).to receive(:billing_plan).and_return(billing_plan)
  end

  subject { described_class.new(shop, order) }

  describe '#call' do
    context 'when the shop does not have the exclude_orders_by_tag feature' do
      let(:billing_plan) { create(:billing_plan, features: []) }

      it 'returns false' do
        expect(subject.call).to be false
      end
    end

    context 'when the shop has the exclude_orders_by_tag feature' do
      let(:billing_plan) { create(:billing_plan, features: ['exclude_orders_by_tag']) }

      context 'when order_exclusion_tags is blank' do
        before do
          shop.shop_setting.update(order_exclusion_tags: '')
        end

        it 'returns false' do
          expect(subject.call).to be false
        end
      end

      context 'when order_tags_array is blank' do
        before do
          allow(order).to receive(:order_tags_array).and_return([])
        end

        it 'returns false' do
          expect(subject.call).to be false
        end
      end

      context 'when order_exclusion_tags and order_tags_array are present' do
        before do
          shop.shop_setting.update(order_exclusion_tags: 'exclude,test')
        end

        context 'when order tags include an exclusion tag' do
          before do
            allow(order).to receive(:order_tags_array).and_return(%w[test other])
          end

          it 'returns true' do
            expect(subject.call).to be true
          end
        end

        context 'when order tags do not include an exclusion tag' do
          before do
            allow(order).to receive(:order_tags_array).and_return(%w[other tag])
          end

          it 'returns false' do
            expect(subject.call).to be false
          end
        end

        context 'when comparing tags case-insensitively' do
          before do
            shop.shop_setting.update(order_exclusion_tags: 'Exclude,Test')
            allow(order).to receive(:order_tags_array).and_return(%w[test other])
          end

          it 'returns true' do
            expect(subject.call).to be true
          end
        end
      end
    end
  end
end
