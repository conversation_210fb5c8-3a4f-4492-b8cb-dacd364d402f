# frozen_string_literal: true

require 'rails_helper'

RSpec.describe OrderJobFilter do
  before do
    allow_any_instance_of(ShopSetting).to receive(:handle_rules).and_return(nil)
    allow_any_instance_of(ShopifyAPI::Order).to receive(:save).and_return(nil)
    stub_shopify_authenticated(shop)
  end

  describe '#filter?' do
    let(:shop) { FactoryBot.create(:shop) }
    let(:shop_setting) { FactoryBot.create(:shop_setting, excludePOS: true) }

    context 'when order source name is pos and shop setting exclude pos is true' do
      let(:order) do
        FactoryBot.create(:order, source_name: 'pos')
      end

      let(:response) { described_class.new(shop, shop_setting).filter?(order) }

      it 'should return correct response' do
        expect(response).to be_truthy
      end
    end

    context 'when order source name is not pos' do
      let(:order) do
        FactoryBot.create(:order, :with_line_items, source_name: SecureRandom.hex(3), financial_status: 'refunded')
      end

      let(:response) { described_class.new(shop, shop_setting).filter?(order) }

      it 'should return correct response' do
        expect(response).to be_falsey
      end
    end

    context 'when financial status is voided' do
      let(:order) do
        FactoryBot.create(:order, :with_line_items, source_name: SecureRandom.hex(3), financial_status: 'voided')
      end

      let(:response) { described_class.new(shop, shop_setting).filter?(order) }

      it 'should return correct response' do
        expect(response).to be_truthy
      end
    end

    context 'when financial status is refunded' do
      let(:order) do
        FactoryBot.create(:order, :with_line_items, source_name: SecureRandom.hex(3), financial_status: 'refunded')
      end

      let(:response) { described_class.new(shop, shop_setting).filter?(order) }

      it 'should return false' do
        expect(response).to be_falsey
      end
    end
  end
end
