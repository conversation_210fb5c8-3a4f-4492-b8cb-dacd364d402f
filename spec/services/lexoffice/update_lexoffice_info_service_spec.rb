# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Lexoffice::UpdateLexofficeInfoService do
  describe '#call' do
    let(:shop) { create(:shop) }
    let(:lexoffice_token) { SecureRandom.hex(15) }
    let(:profile_info) do
      {
        'organizationId' => SecureRandom.hex(25),
        'taxType' => 'gross',
        'smallBusiness' => true,
        'connectionId' => SecureRandom.hex(19),
        'distanceSalesPrinciple' => 'ORIGIN'
      }
    end

    before do
      allow(shop).to receive(:lexoffice_token).and_return(lexoffice_token)
      allow_any_instance_of(Lexoffice::Profile).to receive(:get_info).and_return(profile_info)
    end

    it 'refreshes the token if expired' do
      expect(shop).to receive(:refresh_token_if_expired)
      described_class.new(shop).call
    end

    it 'updates the shop with profile info' do
      expect(shop).to receive(:update).with(
        lexoffice_organization_id: profile_info['organizationId'],
        lexoffice_tax_type: profile_info['taxType'],
        lexoffice_small_business: true,
        lexoffice_connection_id: profile_info['connectionId'],
        distance_sales_principle: profile_info['distanceSalesPrinciple']
      )
      described_class.new(shop).call
    end

    it 'returns the profile info' do
      result = described_class.new(shop).call
      expect(result).to eq(profile_info)
    end
  end
end
