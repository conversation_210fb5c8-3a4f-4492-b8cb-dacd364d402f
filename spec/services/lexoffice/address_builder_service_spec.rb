# frozen_string_literal: true

require "rails_helper"

RSpec.describe Lexoffice::AddressBuilderService do
  describe "#call" do
    subject(:service) { described_class.new(order, use_shipping_address) }

    let(:use_shipping_address) { false }

    context "when order is nil" do
      let(:order) { nil }

      it "returns default address" do
        expect(service.call).to eq(
          {
            name: "Sammelkunde Shopify",
            countryCode: "DE"
          }
        )
      end
    end

    context "when customer exists but no valid billing address" do
      let(:order) { build(:order, :with_customer) }

      it "builds customer address" do
        expect(service.call).to eq(
          {
            name: "#{order.customer.first_name} #{order.customer.last_name}",
            countryCode: "DE"
          }
        )
      end
    end

    context "when billing address is valid" do
      let(:order) { build(:order, :with_billing_address) }

      it "builds detailed address with company" do
        result = service.call
        billing_address = order.billing_address

        expect(result).to include(
          street: billing_address["address1"],
          zip: billing_address["zip"],
          city: billing_address["city"],
          countryCode: "DE",
          name: "#{billing_address["first_name"]} #{billing_address["last_name"]}"
        )
      end

      context "when company is null" do
        before do
          order.billing_address["company"] = "null"
        end

        it "builds address without supplement" do
          result = service.call
          billing_address = order.billing_address

          expect(result).to include(
            street: billing_address["address1"],
            zip: billing_address["zip"],
            city: billing_address["city"],
            countryCode: "DE",
            name: "#{billing_address["first_name"]} #{billing_address["last_name"]}"
          )
          expect(result[:supplement]).to be_nil
        end
      end

      context "when country code is *" do
        before do
          order.billing_address["country_code"] = "*"
        end

        it "uses DE as country code" do
          expect(service.call[:countryCode]).to eq("DE")
        end
      end
    end

    context "when using shipping address" do
      let(:use_shipping_address) { true }
      let(:order) do
        build(:order, :with_billing_address, :with_shipping_address) do |order|
          order.shipping_address = build(:address, country_code: "US")
          order.billing_address = build(:address, country_code: "DE")
        end
      end

      it "builds detailed address from shipping address" do
        result = service.call
        shipping_address = order.shipping_address

        expect(result).to include(
          street: shipping_address["address1"],
          zip: shipping_address["zip"],
          city: shipping_address["city"],
          countryCode: "US",
          name: "#{shipping_address["first_name"]} #{shipping_address["last_name"]}"
        )
      end
    end
  end
end
