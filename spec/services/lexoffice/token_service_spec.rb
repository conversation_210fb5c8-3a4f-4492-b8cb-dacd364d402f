require 'rails_helper'

RSpec.describe Lexoffice::TokenService do
  describe '#perform_request' do
    let(:shop) { FactoryBot.create(:shop) }
    let(:service) { Lexoffice::TokenService.new(shop.lexoffice_refresh_token) }

    before do
      stub_token_refresh
    end

    subject { JSON.parse(service.call) }

    it 'should return access token' do
      should have_key('access_token')
    end

    it 'should return expires in' do
      should have_key('expires_in')
    end
  end
end
