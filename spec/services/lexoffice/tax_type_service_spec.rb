# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Lexoffice::TaxTypesService do
  let(:shop) { create(:shop, country: 'DE') }
  let(:order) { build(:order, :with_customer, :with_billing_address, :with_shipping_address, :with_line_items) }

  describe '#call' do
    subject { described_class.new(shop, order).call }

    context 'when shop is vat free' do
      before do
        allow(shop).to receive(:lexoffice_small_business).and_return(true)
      end

      it 'returns vatfree' do
        expect(subject).to eq('vatfree')
      end
    end

    context 'when country is in EEA, settings are VAT and a positive tax rate' do
      let(:order) do
        build(:order, :with_customer, :with_billing_address, :with_shipping_address, :with_line_items,
              :with_EEA_country_address, :with_positive_tax_rate)
      end

      before do
        allow(shop).to receive(:distance_sales_principle).and_return('ORIGIN')
        allow(order).to receive(:at_least_one_delivery?).and_return(true)
        allow(order).to receive(:taxes_included).and_return(true)
      end

      it 'returns gross' do
        expect(subject).to eq('gross')
      end
    end

    context 'when country is in EEA, settings are VAT and tax rate is zero' do
      let(:order) do
        build(:order, :with_customer, :with_billing_address, :with_shipping_address, :with_line_items,
              :with_EEA_country_address, :with_zero_tax_rate)
      end

      before do
        allow(shop).to receive(:distance_sales_principle).and_return('ORIGIN')
        allow(order).to receive(:at_least_one_delivery?).and_return(true)
        allow(order).to receive(:taxes_included).and_return(false)
      end

      it 'returns thirdPartyCountryDelivery' do
        expect(subject).to eq('thirdPartyCountryDelivery')
      end
    end

    context 'when country is in EEA and settings are OSS and tax rate is zero' do
      let(:order) do
        build(:order, :with_customer, :with_billing_address, :with_shipping_address, :with_line_items,
              :with_EEA_country_address, :with_zero_tax_rate)
      end

      before do
        allow(shop).to receive(:distance_sales_principle).and_return('DESTINATION')
        allow(order).to receive(:at_least_one_delivery?).and_return(true)
        allow(order).to receive(:taxes_included).and_return(true)
      end

      it 'returns thirdPartyCountryDelivery' do
        expect(subject).to eq('thirdPartyCountryDelivery')
      end
    end

    context 'when country is Switzerland and tax rate is zero' do
      let(:order) do
        build(:order, :with_customer, :with_billing_address, :with_shipping_address, :with_line_items,
              :with_zero_tax_rate, :with_switzerland_address)
      end

      before do
        allow(shop).to receive(:distance_sales_principle).and_return('ORIGIN')
        allow(order).to receive(:at_least_one_delivery?).and_return(true)
        allow(order).to receive(:taxes_included).and_return(true)
      end

      it 'returns thirdPartyCountryDelivery' do
        expect(subject).to eq('thirdPartyCountryDelivery')
      end
    end

    context 'when country is outside the EU, non EEA and tax rate is zero' do
      let(:order) do
        build(:order, :with_customer, :with_billing_address, :with_shipping_address, :with_line_items,
              :with_zero_tax_rate, :with_USA_address)
      end

      before do
        allow(shop).to receive(:distance_sales_principle).and_return('ORIGIN')
        allow(order).to receive(:at_least_one_delivery?).and_return(true)
        allow(order).to receive(:taxes_included).and_return(true)
      end

      it 'returns thirdPartyCountryDelivery' do
        expect(subject).to eq('thirdPartyCountryDelivery')
      end
    end

    context 'when country is valid EU country' do
      before do
        allow(shop).to receive(:distance_sales_principle).and_return('ORIGIN')
        allow(order).to receive(:at_least_one_delivery?).and_return(true)
        allow(order).to receive(:taxes_included).and_return(true)
      end

      it 'returns gross' do
        expect(subject).to eq('gross')
      end
    end

    context 'when determining third party country tax type' do
      let(:order) do
        build(:order, :with_company_customer, :with_shipping_address, :with_billing_address, :with_line_items,
              :with_EEA_country_address, :with_zero_tax_rate)
      end

      before do
        allow(shop).to receive(:distance_sales_principle).and_return('ORIGIN')
        allow(order).to receive(:at_least_one_delivery?).and_return(true)
        allow(order).to receive(:taxes_included).and_return(true)
      end

      context 'when customer is a company and there is at least one delivery' do
        before do
          allow(order).to receive(:at_least_one_delivery?).and_return(true)
          allow(order).to receive(:company_customer?).and_return(true)
        end

        it 'returns thirdPartyCountryDelivery' do
          expect(subject).to eq('thirdPartyCountryDelivery')
        end
      end

      context 'when customer is not a company and there is at least one delivery' do
        before do
          allow(order).to receive(:at_least_one_delivery?).and_return(true)
          allow(order).to receive(:company_customer?).and_return(false)
        end

        it 'returns thirdPartyCountryDelivery' do
          expect(subject).to eq('thirdPartyCountryDelivery')
        end
      end

      context 'when customer is a company and there is no delivery' do
        let(:order) do
          build(:order, :with_company_customer, :with_shipping_address, :with_billing_address, :with_line_items,
                :with_EEA_country_address, :with_zero_tax_rate).tap do |order|
            order.shipping_address[:company] = 'Test Company'
            order.billing_address[:company] = 'Test Company'
          end
        end

        before do
          allow(order).to receive(:at_least_one_delivery?).and_return(false)
          allow(order).to receive(:company_customer?).and_return(true)
        end

        it 'returns thirdPartyCountryService' do
          expect(subject).to eq('thirdPartyCountryService')
        end
      end
    end
  end
end
