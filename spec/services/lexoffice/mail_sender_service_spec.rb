# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Lexoffice::MailSenderService do
  describe '#call' do
    let(:shop) { create(:shop_setting).shop }
    let(:order) { build(:order) }
    let(:service) { Lexoffice::MailSenderService.new(order, sync_info) }

    before do
      allow_any_instance_of(ShopSetting).to receive(:handle_rules).and_return(nil)
      allow_any_instance_of(SyncRule).to receive(:handle_webhooks).and_return(nil)
      stub_request(:post, /.*sendmail/).to_return(status: 200, headers: {})
      stub_shopify_authenticated(shop)
    end

    context 'mail was not send before' do
      let(:sync_info) { create(:sync_info, { shop_id: shop.id, invoice_mail_sent: false }) }

      it 'should call lexoffice send mail API' do
        expect(sync_info.invoice_mail_sent).to be_falsey
        expect(service.call).to be_truthy
        expect(sync_info.invoice_mail_sent).to be_truthy
      end
    end

    context 'mail was already sent' do
      let(:sync_info) { create(:sync_info, { shop_id: shop.id, invoice_mail_sent: true }) }

      it 'should not call lexoffice send mail API' do
        expect(service.call).to be_falsey
      end
    end
  end
end
