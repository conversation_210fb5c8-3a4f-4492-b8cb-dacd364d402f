# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Lexoffice::VoucherDateService do
  let(:order) { build(:order, :with_fulfillments) }
  let(:refund) { build(:refund) }

  context 'when rule_webhook is "orders/fulfilled"' do
    let(:rule_webhook) { 'orders/fulfilled' }
    let(:response) { described_class.new(order, rule_webhook, nil).call }
    it 'returns the ISO8601 date for order fulfillment' do
      expect(response).to eq(Time.parse(order.fulfillments.first.created_at).iso8601(3))
    end
  end

  context 'when rule_webhook is "orders/create"' do
    let(:rule_webhook) { 'orders/create' }
    let(:response) { described_class.new(order, rule_webhook, nil).call }

    it 'returns the ISO8601 date for order creation' do
      expect(response).to eq(Time.parse(order.processed_at).iso8601(3))
    end
  end

  context 'when rule_webhook is "refunds/create"' do
    let(:rule_webhook) { 'refunds/create' }
    let(:response) { described_class.new(order, nil, refund).call }

    it 'returns the ISO8601 date for refund creation' do
      expect(response).to eq(Time.parse(refund.processed_at).iso8601(3))
    end
  end
end
