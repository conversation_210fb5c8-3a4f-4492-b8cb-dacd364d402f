# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Lexoffice::ContactDataService do
  before do
    stub_shopify_authenticated(shop)
  end
  describe '#call' do
    let(:shop) { FactoryBot.create(:shop) }
    let(:order) do
      build(:order, :with_customer, :with_billing_address, :with_shipping_address)
    end
    let(:order_with_no_address) do
      build(:order, :with_customer, :with_line_items, line_items_count: 2) do |order|
        order.customer.default_address[:address1] = nil
      end
    end
    let(:order_with_no_address_and_no_email) do
      build(:order, :with_line_items, :with_customer, line_items_count: 2) do |order|
        order.email = nil
        order.customer.email = nil
      end
    end
    let(:order_with_billing_north_ireland) do
      build(:order, :with_customer, :with_billing_address) do |order|
        order.billing_address[:province_code] = 'NIR'
      end
    end

    let(:order_with_shipping_north_ireland) do
      build(:order, :with_customer, :with_shipping_address) do |order|
        order.shipping_address[:province_code] = 'NIR'
      end
    end

    context 'order with billing address, valid email address and customer attributes' do
      let(:response) { described_class.new(order, false).call }

      it 'should return customer attributes with the same email and the billing address infos' do
        expect(response).not_to be_nil
        expect(response[:email]).to eq(order.customer.email)
        expect(response[:phone]).to eq(order.customer.phone)
        expect(response[:address1]).to eq(order.billing_address['address1'])
      end
    end

    context 'order without customer' do
      let(:order_with_no_address_and_no_email) do
        build(:order, :with_line_items, line_items_count: 2)
      end

      let(:response) { described_class.new(order_with_no_address_and_no_email, false).call }

      it 'should return nil' do
        expect(response).to be_nil
      end
    end

    context 'order with customer attributes' do
      context 'with valid email address and no billing or default address' do
        let(:response) { described_class.new(order_with_no_address, false).call }
        it 'should return customer attributes with the same email but no address' do
          expect(response).not_to be_nil
          expect(response[:email]).to eq(order_with_no_address.customer.email)
          expect(response[:address1]).to be_nil
        end
      end
      context 'with invalid email address and no billing but a default address' do
        let(:response) { described_class.new(order_with_no_address_and_no_email, false).call }
        it 'should return customer attributes with a fake email but no address' do
          expect(response).not_to be_nil
          expect(response[:email]).to eq("#{order_with_no_address_and_no_email.customer.id}@lexoffice-shopify-app.de")
          expect(response[:shipping_address]).to be_nil
          expect(response[:address1]).to eq(order_with_no_address_and_no_email.customer.default_address['address1'])
        end
      end
      context 'order with billing address in north ireland' do
        let(:response) { described_class.new(order_with_billing_north_ireland, false).call }
        it 'should set the country code to XI' do
          expect(response[:shipping_address]).to be_nil
          expect(response[:country_code]).to eq('XI')
        end
      end
      context 'order with shipping address in north ireland' do
        let(:response) { described_class.new(order_with_shipping_north_ireland, false).call }
        it 'should set the country code to XI' do
          expect(response[:country_code]).to eq('XI')
        end
      end
    end
  end
end
