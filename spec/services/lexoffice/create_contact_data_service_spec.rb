# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Lexoffice::CreateContactDataService, type: :service do
  let(:customer) do
    {
      company: 'Test Company',
      salutation: 'Mr.',
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '1234567890',
      vatId: 'DE123456789',
      address1: 'Test Street 1',
      address2: 'Test Street 2',
      zip: '12345',
      city: 'Test City',
      country_code: 'DE',
      note: 'Test Note',
      shipping_address: OpenStruct.new(
        address1: 'Shipping Street 1',
        zip: '54321',
        city: 'Shipping City',
        country_code: 'US'
      )
    }
  end

  let(:tax_type) { 'gross' }
  let(:roles) { { 'customer' => {} } }
  let(:version) { 0 }
  let(:contact_id) { nil }

  subject { described_class.new(customer, tax_type, roles, version, contact_id).call }

  context 'when customer is a company' do
    it 'creates contact data with company information' do
      expect(subject[:company]).to be_present
      expect(subject[:person]).to be_nil
    end

    it 'sets email address as business' do
      expect(subject[:emailAddresses][:business]).to eq([customer[:email]])
    end
  end

  context 'when customer is a person' do
    before do
      customer[:company] = nil
    end

    it 'creates contact data with person information' do
      expect(subject[:person]).to be_present
      expect(subject[:company]).to be_nil
    end

    it 'sets email address as private' do
      expect(subject[:emailAddresses][:private]).to eq([customer[:email]])
    end
  end

  context 'when customer has a phone number' do
    it 'adds phone number to contact data' do
      expect(subject[:phoneNumbers][:private]).to eq([customer[:phone]])
    end
  end

  context 'when customer does not have a phone number' do
    before do
      customer[:phone] = nil
    end

    it 'does not add phone number to contact data' do
      expect(subject[:phoneNumbers]).to be_nil
    end
  end

  context 'when customer has a shipping address' do
    it 'adds shipping address to contact data' do
      expect(subject[:addresses][:shipping]).to be_present
    end
  end

  context 'when customer does not have a shipping address' do
    before do
      customer[:shipping_address] = nil
    end

    it 'does not add shipping address to contact data' do
      expect(subject[:addresses][:shipping]).to be_nil
    end
  end

  context 'when address fields exceed 100 characters' do
    let(:long_address) { 'A' * 150 }
    let(:html_address) { '123 Main St & Co. Building' }
    let(:address_with_entity_at_boundary) { "#{'A' * 96}&amp;ABC" } # This would normally be cut in the middle of &amp;

    before do
      customer[:address1] = long_address
      customer[:address2] = html_address
    end

    it 'limits address1 to maximum 100 characters' do
      expect(subject[:addresses][:billing].first[:street].length).to be <= 100
    end

    it 'properly handles HTML encoding in address2' do
      result = subject[:addresses][:billing].first[:supplement]
      expect(result).to eq(html_address) # Should preserve original text while handling HTML entities
    end

    it 'handles HTML entities at truncation boundary' do
      customer[:address1] = address_with_entity_at_boundary
      result = subject[:addresses][:billing].first[:street]
      expect(result).to eq('A' * 96) # Should cut before the &amp; to avoid partial entities
    end

    context 'with shipping address' do
      before do
        customer[:shipping_address] = OpenStruct.new(
          address1: long_address,
          address2: html_address,
          zip: '54321',
          city: 'Shipping City',
          country_code: 'US'
        )
      end

      it 'limits shipping address fields to maximum 100 characters' do
        expect(subject[:addresses][:shipping].first[:street].length).to be <= 100
        expect(subject[:addresses][:shipping].first[:supplement]).to eq(html_address)
      end

      it 'handles HTML entities at truncation boundary in shipping address' do
        customer[:shipping_address] = OpenStruct.new(
          address1: address_with_entity_at_boundary,
          address2: 'Normal Address',
          zip: '54321',
          city: 'Shipping City',
          country_code: 'US'
        )
        result = subject[:addresses][:shipping].first[:street]
        expect(result).to eq('A' * 96) # Should cut before the &amp; to avoid partial entities
      end
    end
  end
end
