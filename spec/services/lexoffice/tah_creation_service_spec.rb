# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Lexoffice::TahCreationService do
  describe '#call' do
    let(:shop) { create(:shop) }
    let(:order) { build(:order, :with_customer, :with_billing_address, :with_shipping_address, :with_line_items) }

    let(:invoice_info_target_id) do
      JSON.parse(load_json_fixture('transaction_assignment_hints/master_card'))['voucherId']
    end

    let(:authorization_key) do
      JSON.parse(load_json_fixture('transaction_assignment_hints/master_card'))['externalReference']
    end

    before do
      stub_request(:post, "#{ENV.fetch('LEXOFFICE_API')}/v1/transaction-assignment-hint")
        .to_return(status: 200, body: load_json_fixture('transaction_assignment_hints/master_card'))
      allow(ShopifyAPI::Order).to receive(:find).and_return(order)
    end

    let(:response) do
      described_class.new(shop.id, invoice_info_target_id, authorization_key, order.id, order.name).call
    end

    it 'creates a sync info' do
      expect { response }.to change { SyncInfo.count }.by(1)
      expect(SyncInfo.last.target_id).to eq(invoice_info_target_id)
      expect(SyncInfo.last.target_type).to eq('Transaction Assignment Hint')
    end
  end
end
