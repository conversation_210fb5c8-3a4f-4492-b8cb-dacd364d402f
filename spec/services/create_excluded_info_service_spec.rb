# frozen_string_literal: true

# spec/services/create_excluded_info_service_spec.rb
require 'rails_helper'

RSpec.describe CreateExcludedInfoService, type: :service do
  let(:shop) { create(:shop) }
  let(:order) { build(:order, :with_refund) }
  let(:refund) { order.refunds.first }
  let(:transaction_id) { '12345' }

  describe '#call' do
    context 'when target_type is Transaction' do
      it 'creates a SyncInfo with transaction details' do
        service = described_class.new(order, shop, 'Transaction', transaction_id)
        expect { service.call }.to change { SyncInfo.count }.by(1)
        sync_info = SyncInfo.last
        expect(sync_info.shop_id.to_i).to eq(shop.id)
        expect(sync_info.shopify_id).to eq(transaction_id)
        expect(sync_info.shopify_order_id.to_i).to eq(order.id)
        expect(sync_info.shopify_order_name).to eq(order.name)
        expect(sync_info.target_type).to eq('Transaction')
        expect(sync_info.last_action).to eq('Excluded')
        expect(sync_info.extra_infos).to eq({ amount: 'Excluded/0' })
      end
    end

    context 'when target_type is Refund' do
      it 'creates a SyncInfo with refund details' do
        service = described_class.new(order, shop, 'Refund', nil, refund.id)
        expect { service.call }.to change { SyncInfo.count }.by(1)
        sync_info = SyncInfo.last
        expect(sync_info.shop_id.to_i).to eq(shop.id)
        expect(sync_info.shopify_id.to_i).to eq(refund.id)
        expect(sync_info.shopify_order_id.to_i).to eq(order.id)
        expect(sync_info.shopify_order_name).to eq(order.name)
        expect(sync_info.target_type).to eq('Refund')
        expect(sync_info.last_action).to eq('Excluded')
        expect(sync_info.extra_infos).to eq({})
      end
    end

    context 'when target_type is neither Transaction nor Refund' do
      it 'creates a SyncInfo with order details' do
        service = described_class.new(order, shop, 'Order')
        expect { service.call }.to change { SyncInfo.count }.by(1)
        sync_info = SyncInfo.last
        expect(sync_info.shop_id.to_i).to eq(shop.id)
        expect(sync_info.shopify_id.to_i).to eq(order.id)
        expect(sync_info.shopify_order_id.to_i).to eq(order.id)
        expect(sync_info.shopify_order_name).to eq(order.name)
        expect(sync_info.target_type).to eq('Order')
        expect(sync_info.last_action).to eq('Excluded')
        expect(sync_info.extra_infos).to eq({})
      end
    end
  end
end
