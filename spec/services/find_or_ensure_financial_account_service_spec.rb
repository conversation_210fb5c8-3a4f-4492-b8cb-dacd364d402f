# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FindOrEnsureFinancialAccountService do
  describe '#call' do
    let(:shop) { create(:shop) }
    let(:account_types) { %w[credit_card klarna sofort] }
    let(:response) { described_class.call(transaction_setting, shop.lexoffice_token, account_types) }

    context 'with existing account id in transaction setting and financial account' do
      let(:transaction_setting) do
        create(:transaction_setting, shop_id: shop.id, enable_credit_card: true,
                                     credit_card_account_id: SecureRandom.hex(4))
      end

      before { allow_any_instance_of(Lexoffice::FinancialAccount).to receive(:find_by_id).and_return(true) }

      it 'does not call CreateFinanceAccountService' do
        expect(CreateFinancialAccountService).to_not receive(:call)
        response
      end
    end

    context 'without existing account id in transaction setting and financial account' do
      let(:transaction_setting) { create(:transaction_setting, shop_id: shop.id) }

      before do
        allow_any_instance_of(CreateFinancialAccountService).to receive(:call).and_return(nil)
        allow_any_instance_of(Lexoffice::Finance).to receive(:find_by_id).and_return(nil)
        allow(Redis).to receive(:new).and_return(MockRedis.new)
      end

      it 'calls CreateFinanceAccountService for each given payment type' do
        expect(CreateFinancialAccountService).to receive(:call).thrice
        response
      end
    end
  end
end
