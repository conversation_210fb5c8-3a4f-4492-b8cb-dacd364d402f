# frozen_string_literal: true

require 'rails_helper'

RSpec.describe BillingServices::UninstallService do
  let(:billing_plan) { create(:billing_plan, name: 'Pro Plan') }
  let!(:shop) { create(:shop) }
  let(:service) { described_class.new(shop) }

  before do
    allow(shop).to receive(:billing_plan).and_return(billing_plan)
  end

  describe '#call' do
    subject(:perform_call) { service.call }

    before do
      allow(service).to receive(:report_event)
      allow(NotificationsJob).to receive(:perform_async)
      allow(MailchimpUnsubscribeJob).to receive(:perform_async)
    end

    it 'reports the uninstall event' do
      expect(service).to receive(:report_event).with(
        event_name: 'app_uninstalled',
        event_type: 'churn',
        customer_myshopify_domain: shop.shopify_domain,
        event_value: 'Pro Plan',
        payload: {
          reason: 'user_choice',
          had_active_subscription: shop.plan_active?
        },
        timestamp: anything
      )

      perform_call
    end

    it 'destroys the shop' do
      expect { perform_call }.to change(Shop, :count).by(-1)
    end

    context 'when in production environment' do
      before do
        allow(Rails.env).to receive(:production?).and_return(true)
      end

      it 'queues notification and mailchimp jobs' do
        shop_infos = {
          shop_owner: shop.shop_owner,
          shopify_domain: shop.shopify_domain,
          email: shop.email
        }.to_json

        expect(NotificationsJob).to receive(:perform_async).with(shop_infos, 'uninstall', 'email')
        expect(NotificationsJob).to receive(:perform_async).with(shop_infos, 'uninstall', 'notification')
        expect(MailchimpUnsubscribeJob).to receive(:perform_async).with(shop_infos)

        perform_call
      end
    end
  end
end
