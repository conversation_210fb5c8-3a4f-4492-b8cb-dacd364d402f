# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FindOrCreateFinancialTransactionService do
  describe '#call' do
    let(:shop) { create(:shop) }
    let(:transaction_setting) { create(:transaction_setting, shop_id: shop.id) }
    let(:response) { described_class.new(shop, transaction_setting, transaction_params).call }
    let(:order) { build(:order, name: SecureRandom.hex(4), created_at: Time.zone.today.to_s) }
    let(:financial_transaction_endpoint) { double('Lexoffice::FinancialTransaction', create: nil) }
    let(:financial_transaction_id) { SecureRandom.hex(4) }
    let(:financial_transaction) { { 'financialTransactionId' => financial_transaction_id } }

    before do
      stub_shopify_authenticated(shop)
      allow(ShopifyAPI::Order).to receive(:find).and_return(order)
      allow(Lexoffice::FinancialTransaction).to receive(:new).and_return(financial_transaction_endpoint)
      allow(financial_transaction_endpoint).to receive_message_chain(:create,
                                                                     :first).and_return(financial_transaction)
      allow(Redis).to receive(:new).and_return(MockRedis.new)
    end

    context 'with existing finance transaction and disabled payment method' do
      let(:transaction_params) { { 'id' => SecureRandom.hex(8), 'payment_method' => 'credit_card' } }

      before do
        allow(SyncInfo).to receive(:find_by).and_return(true)
      end

      it 'does not create a finance account or sync info' do
        expect_any_instance_of(Lexoffice::FinancialTransaction).to_not receive(:create)
        expect(SyncInfo).to_not receive(:create)
        response
      end
    end

    context 'with existing finance transaction and enabled payment method' do
      let(:id) { SecureRandom.hex(4) }
      let(:order_id) { SecureRandom.random_number(9_223_372_036_854_775) }
      let(:transaction_params) do
        { 'id' => id, 'order_id' => order_id, 'amount' => 555, 'payment_method' => 'samsung_pay' }
      end

      it 'does create a sync info' do
        expect { response }.to change { SyncInfo.count }.by(1)
        response
      end
    end

    context 'without existing finance transaction' do
      let(:id) { SecureRandom.hex(4) }
      let(:order_id) { SecureRandom.random_number(9_223_372_036_854_775) }
      let(:transaction_params) do
        { 'id' => id, 'order_id' => order_id, 'amount' => 555, 'payment_method' => 'samsung_pay' }
      end

      it 'calls finance transaction creation' do
        # expect financial_transaction_endpoint to be called
        expect(financial_transaction_endpoint.create.first).to eq(financial_transaction)
        response
      end

      it 'creates a sync info' do
        expect { response }.to change { SyncInfo.count }.by(1)
        expect(SyncInfo.last.target_id).to eq(financial_transaction_id)
        expect(SyncInfo.last.extra_infos['amount']).to eq(555)
      end
    end

    context 'with duplicate finance transaction' do
      let(:id) { SecureRandom.hex(4) }
      let(:order_id) { SecureRandom.random_number(9_223_372_036_854_775) }
      let(:transaction_setting) { create(:transaction_setting, shop_id: shop.id, enable_credit_card: true) }
      let(:transaction_params) do
        { 'id' => id, 'order_id' => order_id, 'amount' => 555, 'payment_method' => 'credit_card' }
      end
      let(:financial_transaction_endpoint) { double('Lexoffice::FinancialTransaction', create: nil) }
      let(:financial_transaction) { { 'financialTransactionId' => id } }

      before do
        SyncInfo.create(shop_id: shop.id, shopify_order_id: order_id, target_type: 'Transaction', shopify_id: id,
                        extra_infos: { 'amount' => 555 })
      end

      it 'does not create a finance transaction if it is a duplicate' do
        expect { response }.not_to(change { SyncInfo.count })
      end
    end

    context 'with partial finance transaction' do
      let(:id) { SecureRandom.hex(4) }
      let(:order_id) { Faker::Number.number(digits: 14) }
      let(:transaction_setting) { create(:transaction_setting, shop_id: shop.id, enable_credit_card: true) }
      let(:transaction_params) do
        { 'id' => id, 'order_id' => order_id, 'amount' => 555, 'payment_method' => 'credit_card' }
      end
      let(:financial_transaction_endpoint) { double('Lexoffice::FinancialTransaction', create: nil) }

      before do
        SyncInfo.create(shop_id: shop.id, shopify_order_id: order_id, target_type: 'Transaction',
                        extra_infos: { 'amount' => 555 })
      end

      it 'creates a finance transaction if it is a partial payment' do
        expect { response }.to(change { SyncInfo.count })
      end
    end
  end
end
