# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EvaluateAndExcludeEntityService, type: :service do
  let(:shop) { create(:shop) }
  let(:shop_settings) { create(:shop_setting, shop:) }
  let(:entity) { build(:order, :with_refund) }
  let(:context) { 'Invoice' }
  let(:transaction_id) { '12345' }
  let(:refund_id) { '54321' }
  let(:shopify_id) do
    case context
    when 'Transaction' then transaction_id
    when 'Refund' then refund_id
    else entity.id.to_s
    end
  end

  before do
    allow(shop).to receive(:shop_setting).and_return(shop_settings)
  end

  describe '#call' do
    context 'when entity should be excluded by tags' do
      before do
        allow(ExclusionTagsService).to receive(:call).and_return(true)
      end

      it 'creates an excluded sync info and returns nil' do
        expect do
          result = described_class.new(entity, shop, context, transaction_id, refund_id).call
          expect(result).to be_nil
        end.to change { SyncInfo.count }.by(1)

        sync_info = SyncInfo.last
        expect(sync_info.shop_id).to eq(shop.id)
        expect(sync_info.shopify_id).to eq(shopify_id)
        expect(sync_info.shopify_order_name).to eq(entity.name)
        expect(sync_info.target_type).to eq(context)
        expect(sync_info.last_action).to eq('Excluded')
      end

      it 'does not create a new sync info if one already exists' do
        create(:sync_info, shop_id: shop.id, shopify_id:, target_type: context)
        expect { described_class.new(entity, shop, context, transaction_id, refund_id).call }
          .not_to(change { SyncInfo.count })
      end

      context 'when target type is Transaction' do
        let(:context) { 'Transaction' }

        it 'sets extra_infos with excluded amount' do
          described_class.new(entity, shop, context, transaction_id, refund_id).call
          sync_info = SyncInfo.last
          expect(sync_info.extra_infos).to eq({ amount: 'Excluded/0' })
        end
      end
    end

    context 'when entity is a POS order' do
      let(:entity) { build(:order, source_name: 'pos') }

      before do
        allow(ExclusionTagsService).to receive(:call).and_return(false)
      end

      context 'when POS orders should be excluded' do
        before do
          allow(shop_settings).to receive(:excludePOS).and_return(true)
        end

        it 'returns nil' do
          result = described_class.new(entity, shop, context, transaction_id, refund_id).call
          expect(result).to be_nil
        end
      end

      context 'when POS orders should not be excluded' do
        before do
          allow(shop_settings).to receive(:excludePOS).and_return(false)
          entity.fulfillment_status = 'fulfilled'
        end

        it 'returns the entity' do
          result = described_class.new(entity, shop, context, transaction_id, refund_id).call
          expect(result).to eq(entity)
        end
      end
    end

    context 'when handling non-fulfilled orders' do
      let(:entity) { build(:order, fulfillment_status:) }
      let(:context) { 'Invoice' }

      before do
        allow(ExclusionTagsService).to receive(:call).and_return(false)
        allow(shop_settings).to receive(:excludePOS).and_return(false)
        allow(shop_settings).to receive(:invoice_timing_fulfill?).and_return(true)
      end

      context 'when order is not fulfilled' do
        let(:fulfillment_status) { nil }

        it 'returns nil when timing is on fulfillment' do
          result = described_class.new(entity, shop, context, transaction_id, refund_id).call
          expect(result).to be_nil
        end

        it 'returns the entity when timing is not on fulfillment' do
          allow(shop_settings).to receive(:invoice_timing_fulfill?).and_return(false)
          result = described_class.new(entity, shop, context, transaction_id, refund_id).call
          expect(result).to eq(entity)
        end

        context 'when order is cancelled' do
          before { allow(entity).to receive(:cancelled_at).and_return(Time.current) }

          it 'returns the entity' do
            result = described_class.new(entity, shop, context, transaction_id, refund_id).call
            expect(result).to eq(entity)
          end
        end
      end

      context 'when order is partially fulfilled' do
        let(:fulfillment_status) { 'partial' }

        it 'returns nil when timing is on fulfillment' do
          result = described_class.new(entity, shop, context, transaction_id, refund_id).call
          expect(result).to be_nil
        end

        it 'returns the entity when timing is not on fulfillment' do
          allow(shop_settings).to receive(:invoice_timing_fulfill?).and_return(false)
          result = described_class.new(entity, shop, context, transaction_id, refund_id).call
          expect(result).to eq(entity)
        end
      end

      context 'when order is fulfilled' do
        let(:fulfillment_status) { 'fulfilled' }

        it 'returns the entity' do
          result = described_class.new(entity, shop, context, transaction_id, refund_id).call
          expect(result).to eq(entity)
        end
      end
    end

    context 'when entity should not be excluded' do
      let(:entity) { build(:order, fulfillment_status: 'fulfilled') }

      before do
        allow(ExclusionTagsService).to receive(:call).and_return(false)
        allow(shop_settings).to receive(:excludePOS).and_return(false)
        allow(shop_settings).to receive(:invoice_timing_fulfill?).and_return(false)
      end

      it 'returns the entity' do
        result = described_class.new(entity, shop, context, transaction_id).call
        expect(result).to eq(entity)
      end
    end
  end
end
