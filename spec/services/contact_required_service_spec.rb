# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ContactRequiredService do
  describe '#call' do
    let(:shop_settings) { FactoryBot.create(:shop_setting, create_customer: true) }
    let(:shop) { shop_settings.shop }
    let(:order) { build(:order, :with_billing_address, :with_shipping_address, :with_customer) }
    let(:response) { described_class.new(order, shop).call }

    before do
      stub_shopify_authenticated(shop)
    end

    context 'when create customer is turned on' do
      it 'returns true' do
        expect(response).to be_truthy
      end

      context 'and the order has no customer' do
        let(:order_with_no_customer) { build(:order, :with_line_items, line_items_count: 2) }
        let(:response) { described_class.new(order_with_no_customer, shop).call }

        it 'returns false' do
          expect(response).to be_falsey
        end
      end
    end

    context 'when create customer is turned off' do
      before do
        shop_settings.update(create_customer: false)
      end

      context 'and the order is B2B' do
        before do
          order.customer.note = 'DE123456789'
          order.shipping_address[:company] = 'Test Company GmbH'
        end

        it 'returns true' do
          expect(response).to be_truthy
        end

        context 'and shipping within Germany' do
          before do
            order.shipping_address[:country_code] = 'DE'
            shop.update(country: 'DE')
          end

          it 'still returns true' do
            expect(response).to be_truthy
          end
        end

        context 'and the shop is a small business' do
          before do
            shop.update(lexoffice_small_business: true)
          end

          it 'still returns true' do
            expect(response).to be_truthy
          end
        end
      end

      context 'and the order has a company but no VAT number but shipping within germany' do
        before do
          order.shipping_address[:company] = 'Test Company GmbH'
          order.customer.note = nil
          shop.update(country: 'DE')
          order.billing_address[:country_code] = 'DE'
          order.shipping_address[:country_code] = 'DE'
        end

        it 'returns false' do
          expect(response).to be_falsey
        end
      end

      context 'and the order has a VAT number but no company but shipping within germany' do
        before do
          order.billing_address[:company] = nil
          order.customer.note = 'DE123456789'
          shop.update(country: 'DE')
          order.billing_address[:country_code] = 'DE'
          order.shipping_address[:country_code] = 'DE'
        end

        it 'returns false' do
          expect(response).to be_falsey
        end
      end

      context 'and the shop is not a small business and not shipping within Germany' do
        before do
          order.shipping_address[:country_code] = 'IT'
        end

        it 'returns true' do
          expect(response).to be_truthy
        end
      end

      context 'without shipping address' do
        let(:order) { build(:order, :with_billing_address, :with_customer) }

        it 'returns true' do
          expect(response).to be_truthy
        end
      end

      context 'without shipping and billing address' do
        let(:order) { build(:order, :with_customer) }

        it 'returns false' do
          expect(response).to be_falsey
        end
      end

      context 'and the shop is a small business' do
        before do
          shop.update(lexoffice_small_business: true)
        end

        it 'returns false' do
          expect(response).to be_falsey
        end
      end

      context 'and shipping within Germany' do
        before do
          order.shipping_address[:country_code] = 'DE'
          shop.update(country: 'DE')
        end

        it 'returns false' do
          expect(response).to be_falsey
        end

        context 'but the shop is based in France' do
          before do
            shop.update(country: 'FR')
          end

          it 'returns true' do
            expect(response).to be_truthy
          end
        end
      end

      context 'and shipping to a foreign country' do
        before do
          order.shipping_address[:country_code] = 'FR'
        end

        it 'returns true' do
          expect(response).to be_truthy
        end
      end
    end
  end
end
