# frozen_string_literal: true

require 'rails_helper'

RSpec.describe TransferLogFetcherService do
  let(:shop) { create(:shop) }
  let(:order_id) { 1234 }

  describe '#call' do
    context 'when fetching a single order' do
      before do
        # Create test data
        create(:sync_info,
               shop:,
               shopify_order_id: order_id,
               shopify_order_name: "Order #{order_id}",
               target_type: 'Invoice',
               target_id: 1,
               extra_infos: { voucher_title: 'Invoice 1', status: 'completed', document_id: 'doc1' })

        create(:error_log,
               shop:,
               order_id:,
               shopify_name: "Order #{order_id}",
               error_info_external: 'Test error',
               shopify_type: 'Invoice')
      end

      it 'returns the correct transfer data structure' do
        result = described_class.new(shop.id, order_id:).call
        transformed_result = TransfersTransformerService.call(result)

        expect(transformed_result.first).to include(
          url: "/orders/#{order_id}",
          order_id: "Order #{order_id}"
        )
      end
    end

    context 'when fetching multiple orders with pagination' do
      before do
        5.times do |i|
          create(:sync_info,
                 shop:,
                 shopify_order_id: i,
                 shopify_order_name: "Order #{i}",
                 target_type: 'Invoice')
        end
      end

      it 'returns paginated results' do
        result, count = described_class.new(shop.id, page: 1).call
        transformed_result = TransfersTransformerService.call(result)

        expect(transformed_result.length).to eq(5)
        expect(count).to eq(5)
      end

      context 'with search' do
        it 'filters results by search term' do
          result, count = described_class.new(shop.id, search: 'Order 1').call
          transformed_result = TransfersTransformerService.call(result)

          expect(transformed_result.length).to eq(1)
          expect(count).to eq(1)
        end
      end

      context 'with filter' do
        it 'filters results by type' do
          create(:sync_info,
                 shop:,
                 shopify_order_id: 123_456_789,
                 target_type: 'Refund')

          result, count = described_class.new(shop.id, filter: 'Refund').call
          transformed_result = TransfersTransformerService.call(result)

          expect(transformed_result.length).to eq(1)
          expect(count).to eq(1)
        end

        it 'filters error records' do
          create(:error_log,
                 shop:,
                 order_id: 987_654_321,
                 error_info_external: 'Error')

          result, count = described_class.new(shop.id, filter: 'Error').call
          transformed_result = TransfersTransformerService.call(result)

          expect(transformed_result.length).to eq(1)
          expect(count).to eq(1)
        end
      end
    end
  end

  describe '#fetch_order_ids' do
    it 'filters out null and empty order IDs' do
      service = described_class.new(shop.id)

      # Mock the SQL query result to include nil and empty values
      result_with_nulls = double('result')
      allow(result_with_nulls).to receive(:pluck).with('order_id').and_return(['123', '', nil, '456', nil])

      allow(ActiveRecord::Base.connection).to receive(:exec_query).and_return(result_with_nulls)
      allow(service).to receive(:build_order_ids_sql).and_return('mock_sql')

      # Call the private method
      order_ids = service.send(:fetch_order_ids)

      # Verify only non-blank values are returned
      expect(order_ids).to eq(%w[123 456])
      expect(order_ids).not_to include(nil, '')
    end
  end
end
