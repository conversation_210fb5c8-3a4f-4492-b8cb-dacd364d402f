require 'rails_helper'

RSpec.describe SyncHelper::InitializeTargetDocService do
  describe '#call' do
    let(:shop) { FactoryBot.create(:shop) }

    context 'when order' do
      let(:order) do
        shop.with_shopify_session { ShopifyAPI::Order.new }
      end

      let(:response) { described_class.new(order, shop).call }

      it 'should return instance of lexoffice invoice' do
        expect(response).to be_instance_of Lexoffice::Invoice
      end
    end

    context 'when refund' do
      let(:refund) do
        shop.with_shopify_session { ShopifyAPI::Refund.new }
      end

      let(:response) { described_class.new(refund, shop).call }

      it 'should return instance of lexoffice credit note' do
        expect(response).to be_instance_of Lexoffice::CreditNote
      end
    end
  end
end
