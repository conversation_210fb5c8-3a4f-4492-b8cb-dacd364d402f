# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SyncHelper::FindTargetDocService do
  before do
    allow_any_instance_of(ShopSetting).to receive(:handle_rules).and_return(nil)
  end

  describe '#call' do
    let!(:shop) { create(:shop, :with_shop_setting) }
    # let!(:shop_setting) { shop.shop_setting }
    let(:target_doc) { Lexoffice::Invoice.new(shop.lexoffice_token) }
    let!(:sync_info) { create(:sync_info, :with_sync_rule, shop_object: shop) }
    let!(:rule) { sync_info.sync_rule }
    let(:sync_helper) { SyncHelper::SyncHelper.new(shop) }
    let(:entity) { build(:order) }

    let(:response) { described_class.new(target_doc, sync_info, entity, rule, sync_helper).call }

    context 'when no errors raised' do
      let(:id) { SecureRandom.hex(4) }

      before do
        allow(target_doc).to receive(:find).and_return({ 'id' => id })
      end

      it 'should return correct response' do
        expect(response['id']).to eq id
      end
    end

    context 'when error raised' do
      before do
        stub_request(:get, /invoices/).to_return(status: 404)
        allow_any_instance_of(SyncHelper::SyncHelper).to receive(:sync_entity).and_return(:sync_entity)
      end

      it 'should destroy sync info and restart sync entity' do
        expect { response }.to change(SyncInfo, :count)
        expect(response).to eq :sync_entity
      end
    end
  end
end
