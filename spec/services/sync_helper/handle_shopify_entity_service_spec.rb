# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SyncHelper::HandleShopifyEntityService do
  describe '#call' do
    let(:rule) { create(:sync_rule) }
    let!(:shop) { FactoryBot.create(:shop) }
    let!(:shop_setting) { FactoryBot.create(:shop_setting, shop:) }
    let(:sync_target) { SyncTarget.new(shop) }

    context 'when order' do
      let(:order) do
        shop.with_shopify_session { ShopifyAPI::Order.new }
      end

      let(:response) { described_class.new(order, rule, sync_target).call }

      it 'should call create_order' do
        expect(sync_target).to receive(:create_order)
        response
      end
    end

    context 'when refund' do
      let(:refund) do
        shop.with_shopify_session { ShopifyAPI::Refund.new }
      end

      let(:response) { described_class.new(refund, rule, sync_target).call }

      it 'should call handle_refund' do
        expect(sync_target).to receive(:handle_refund)
        response
      end
    end
  end
end
