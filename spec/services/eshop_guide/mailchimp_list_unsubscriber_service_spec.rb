# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EshopGuide::MailchimpListUnsubscriberService do
  let(:gibbon_mock) { double('gibbon', lists: double('lists', members: double('members', update: double('create')))) }
  let(:shop) { { 'email' => '<EMAIL>', 'shop_owner' => '<PERSON>' } }

  before do
    allow(ENV).to receive(:fetch).and_return('api_key', 'list_id')
    allow(Gibbon::Request).to receive(:new).and_return(gibbon_mock)
  end

  describe '#call' do
    it 'unsubscribes the member from the list' do
      expect(gibbon_mock).to receive_message_chain(:lists, :members, :update).with(body: { status: 'unsubscribed' })
      EshopGuide::MailchimpListUnsubscriberService.call(shop)
    end

    it 'does not unsubscribe if email is blank' do
      shop['email'] = nil
      expect(gibbon_mock).not_to receive(:lists)
      EshopGuide::MailchimpListUnsubscriberService.call(shop)
    end

    it 'rescues and logs Gibbon::MailChimpError' do
      allow(gibbon_mock).to receive_message_chain(:lists, :members, :update).and_raise(Gibbon::MailChimpError)
      expect(Rails.logger).to receive(:error).with(an_instance_of(Gibbon::MailChimpError))
      EshopGuide::MailchimpListUnsubscriberService.call(shop)
    end
  end
end
