# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EshopGuide::MailchimpListSubscriberService do
  let(:gibbon_mock) { double('gibbon', lists: double('lists', members: double('members', create: double('create')))) }
  let(:shop) { FactoryBot.build(:shop, email: '<EMAIL>', shop_owner: '<PERSON>') }

  before do
    allow(ENV).to receive(:fetch).and_return('api_key', 'list_id')
    allow(Gibbon::Request).to receive(:new).and_return(gibbon_mock)
  end

  describe '#call' do
    context 'when email is present' do
      let(:shop) { FactoryBot.build(:shop, email: '<EMAIL>', shop_owner: '<PERSON>') }
      it 'adds the email to the list with correct data' do
        # Call the MailchimpListSubscriberService
        expect(gibbon_mock).to receive_message_chain(:lists, :members, :create).with(
          { body: { email_address: '<EMAIL>',
                    status: 'subscribed',
                    merge_fields: { FNAME: '<PERSON>', LNAME: '<PERSON><PERSON>' } } }
        )
        expect { EshopGuide::MailchimpListSubscriberService.call(shop) }.not_to raise_error
      end
    end

    context 'when email is blank' do
      let(:shop) { FactoryBot.build(:shop, email: '', shop_owner: 'John Doe') }

      it 'does not call the API' do
        expect(gibbon_mock).to_not receive(:lists)
        EshopGuide::MailchimpListSubscriberService.call(shop)
      end
    end

    context 'when an error occurs during API call' do
      it 'logs the error and does not raise an exception' do
        allow(gibbon_mock).to receive_message_chain(:lists, :members, :create).and_raise(Gibbon::MailChimpError)
        expect(Rails.logger).to receive(:error)
        expect { EshopGuide::MailchimpListSubscriberService.call(shop) }.not_to raise_exception
      end
    end
  end
end
