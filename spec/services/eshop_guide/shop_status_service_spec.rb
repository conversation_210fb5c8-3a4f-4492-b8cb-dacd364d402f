# frozen_string_literal: true

require "rails_helper"

RSpec.describe EshopGuide::ShopStatusService, type: :service do
  before do
    allow_any_instance_of(SyncRule).to receive(:handle_webhooks).and_return(nil)
    allow_any_instance_of(ShopSetting).to receive(:handle_rules).and_return(nil)
    allow_any_instance_of(Lexoffice::Profile).to receive(:get_info).and_return({ "companyName" => "Test Company" })
  end

  let(:shop) { create(:shop, :with_shop_setting) }
  let(:shop_status_service) { described_class.new(shop) }

  describe "#billing_status" do
    let(:billing_plan) { nil }

    before do
      allow(shop).to receive(:billing_plan).and_return(billing_plan)
      allow(shop).to receive(:import_unlocked?).and_return(true)
    end

    context "when billing plan is not active" do
      let(:shop) { create(:shop, :with_shop_setting) }

      it "returns billing status with inactive billing" do
        expect(shop_status_service.billing_status[:plan_active]).to be_falsey
      end
    end

    context "when billing plan is active" do
      let(:billing_plan) { create(:billing_plan) }

      it "returns billing status with active billing" do
        expect(shop_status_service.billing_status[:plan_active]).to be_truthy
      end
    end

    context "when billing plan is legacy" do
      let(:billing_plan) { create(:billing_plan, is_legacy: true) }

      it "returns billing status with legacy" do
        expect(shop_status_service.billing_status[:legacy]).to be_truthy
      end
    end
  end

  describe "#service_status" do
    context "when shop is not connected to service" do
      let(:shop) do
        create(:shop, :with_shop_setting,
          lexoffice_token: nil,
          lexoffice_refresh_token: nil)
      end
      it "returns connected_to_service false" do
        expect(shop_status_service.service_status).to include(connected_to_service: false)
      end
    end

    context "when shop is connected to service" do
      let(:shop) do
        create(:shop, :with_shop_setting,
          lexoffice_token: SecureRandom.hex(12),
          lexoffice_refresh_token: SecureRandom.hex(12))
      end
      it "returns connected_to_service true" do
        expect(shop_status_service.service_status).to include(connected_to_service: true)
      end
    end
  end
end
