# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EshopGuide::DynamicTextService do
  describe '#call' do
    let(:shop) { FactoryBot.create(:shop) }
    let(:order) { build(:order, :with_billing_address) }

    let(:response) do
      EshopGuide::DynamicTextService.call(
        '{{billing_address.first_name}} {{billing_address.last_name}}! Vielen Dank für Ihre Bestellung Nr {{id}}', order
      )
    end

    it 'response should include first name' do
      expect(response).to include order.billing_address['first_name']
    end
  end
end
