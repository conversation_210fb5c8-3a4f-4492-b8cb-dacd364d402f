# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LiquidCodeService do
  let(:shop) { FactoryBot.create(:shop) }
  let(:order) { build(:order, :with_customer, :with_billing_address, :with_shipping_address) }
  let(:transaction) { build(:transaction) }

  before do
    stub_shopify_authenticated(shop)
    allow(ShopifyAPI::Order).to receive(:find).and_return(order)
    allow_any_instance_of(ShopifyAPI::Order).to receive(:transactions).and_return([transaction])
  end

  context 'snippet code' do
    let(:snippet) { FactoryBot.create(:snippet) }
    let(:response) { described_class.call(shop, nil, snippet.code, order.id) }
    it 'should return snippet code' do
      expect(response)
        .to match(
          ["Hallo #{order.customer.first_name}, sie erhalten heute Ihre Rechnung zu Auftrag #{order.name}.",
           nil, false]
        )
    end
  end

  context 'snippet code for transaction' do
    let(:snippet) { FactoryBot.create(:snippet, :for_transaction) }
    let(:response) { described_class.call(shop, nil, snippet.code, order.id) }
    it 'should return snippet code' do
      expect(response)
        .to match(
          ["Hallo #{order.customer.first_name}, sie erhalten heute Ihre Rechnung zu Auftrag #{order.name}. " \
           "Ihre Transaktionen: #{transaction.amount}", nil, false]
        )
    end
  end
end
