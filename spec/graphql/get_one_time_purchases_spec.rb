# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetOneTimePurchases do
  subject(:service) { described_class.new }

  describe '#call' do
    let(:mock_response) do
      OpenStruct.new(
        data: OpenStruct.new(
          appByKey: OpenStruct.new(
            installation: OpenStruct.new(
              oneTimePurchases: OpenStruct.new(
                nodes: [
                  OpenStruct.new(
                    id: 'gid://shopify/OneTimePurchase/1',
                    name: 'Basic Plan',
                    createdAt: '2024-03-20T10:00:00Z',
                    price: OpenStruct.new(amount: '29.99'),
                    status: 'ACTIVE'
                  ),
                  OpenStruct.new(
                    id: 'gid://shopify/OneTimePurchase/2',
                    name: 'Premium Plan',
                    createdAt: '2024-03-19T10:00:00Z',
                    price: OpenStruct.new(amount: '49.99'),
                    status: 'CANCELLED'
                  ),
                  OpenStruct.new(
                    id: 'gid://shopify/OneTimePurchase/3',
                    name: 'Pro Plan',
                    createdAt: '2024-03-18T10:00:00Z',
                    price: OpenStruct.new(amount: '99.99'),
                    status: 'ACTIVE'
                  )
                ]
              )
            )
          )
        )
      )
    end

    before do
      allow(service).to receive(:execute).and_return(mock_response)
    end

    it 'filters only active purchases' do
      result = service.call
      expect(result.length).to eq(2)
      expect(result.map(&:status)).to all(eq('ACTIVE'))
    end

    it 'correctly parses purchase data' do
      result = service.call
      first_purchase = result.first

      expect(first_purchase).to have_attributes(
        id: 'gid://shopify/OneTimePurchase/1',
        name: 'Basic Plan',
        created_at: '2024-03-20T10:00:00Z',
        amount: '29.99',
        status: 'ACTIVE'
      )
    end
  end

  describe '#parse_data' do
    context 'when oneTimePurchases is nil' do
      it 'handles nil data gracefully' do
        result = service.parse_data(OpenStruct.new(nodes: []))
        expect(result).to eq([])
      end
    end
  end
end
