# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Home index', type: :request do
  return pending 'TODO: Refactor'
  before do
    stub_request(:get, "#{ENV.fetch('LEXOFFICE_API')}/v1/profile/")
    allow_any_instance_of(SyncRule).to receive(:handle_webhooks).and_return(true)
    login(shop)
    visit home_index_path
  end

  context 'with connection to lexoffice' do
    let(:shop) { create(:shop, :without_lexoffice) }

    context 'without finance scope set' do
      it 'displays new auth partial' do
        expect(page).to have_content('Connect your lexoffice account to the app')
      end
    end

    context 'with finance scope set' do
      let(:shop) { create(:shop, :with_lexoffice, :with_finance_scope) }

      it 'displays settings' do
        expect(page).to have_content(I18n.t('settings.general'))
      end
    end
  end

  context 'without connection to lexoffice' do
    let(:shop) { FactoryBot.create(:shop, :without_lexoffice) }

    it 'displays new auth partial' do
      expect(page).to have_content('Connect your lexoffice account to the app')
    end
  end
end
