# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Home index', type: :request do
  return pending 'TODO: Refactor'

  let(:shop) { create(:shop, :with_lexoffice, :with_finance_scope, :with_transaction_setting) }

  before do
    stub_request(:get, "#{ENV.fetch('LEXOFFICE_API')}/v1/profile/")
    allow_any_instance_of(SyncRule).to receive(:handle_webhooks).and_return(true)
    login(shop)
    visit home_index_path
  end

  context 'payment data synchronization' do
    it 'updates a shops transaction setting' do
      within('form.edit_shop_setting') do
        check(I18n.t('settings.enable_credit_card'))
        first('.actions').click_on I18n.t('settings.save')
      end

      expect(page).to have_content('Shop setting was successfully updated.')

      visit home_index_path
      expect(current_path).to eq(home_index_path)
      expect(page).to have_checked_field(I18n.t('settings.enable_credit_card'))

      shop.reload
      expect(shop.transaction_setting.enable_credit_card).to be_truthy
    end
  end
end
