require "rails_helper"

RSpec.describe Support::CouponCodesController, type: :routing do
  return pending 'TODO: Refactor'

  describe "routing" do
    it "routes to #index" do
      expect(get: "/coupon_codes").to route_to("coupon_codes#index")
    end

    it "routes to #create" do
      expect(post: "coupon_codes/create").to route_to("coupon_codes#create")
    end

    it "routes to #destroy" do
      expect(delete: "/coupon_codes/1").to route_to("coupon_codes#destroy", id: "1")
    end
  end
end
