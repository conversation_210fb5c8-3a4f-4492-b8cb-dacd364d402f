# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Support::BeaconMessagesController, type: :routing do
  return pending 'TODO: Refactor'

  describe 'routing' do
    it 'routes to #index' do
      expect(get: '/beacon_messages').to route_to('beacon_messages#index')
    end

    it 'routes to #create' do
      expect(post: '/beacon_messages').to route_to('beacon_messages#create')
    end

    it 'routes to #destroy' do
      expect(delete: '/beacon_messages/1').to route_to('beacon_messages#destroy', id: '1')
    end
  end
end
