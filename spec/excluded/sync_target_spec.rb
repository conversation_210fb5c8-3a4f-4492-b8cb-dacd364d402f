# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SyncTarget, type: :model do
  before :each do
    @shop = create(:shop, :with_shop_setting)
    stub_shopify_authenticated(@shop)

    # Mock and request in order to ShopifyAPI generate needed classes for future verifying
    @order = build(:order)
    build(:refund, prefix_options: { order_id: @order.id })
  end

  context 'suture' do
    context 'credit note service' do
      it 'should not fail' do
        comparator = lambda { |recorded, actual|
          return recorded == actual unless recorded.is_a? Array

          recorded_except = recorded[1].attributes.except('id', 'created_at', 'updated_at')
          actual_except = actual[1].attributes.except('id', 'created_at', 'updated_at')
          recorded[0].data == actual[0].data && recorded_except == actual_except
        }

        Suture.verify(:credit_note_service, {
                        subject: CreditNoteDataBuilderService.new,
                        fail_fast: false,
                        comparator:
                      })
      end
    end

    context 'invoice service' do
      it 'should not fail' do
        comparator = lambda { |recorded, actual|
          return recorded == actual unless recorded.is_a? Array

          recorded_except = recorded[1].attributes.except('id', 'created_at', 'updated_at')
          actual_except = actual[1].attributes.except('id', 'created_at', 'updated_at')
          recorded[0].data == actual[0].data && recorded_except == actual_except
        }

        Suture.verify(:invoice_service, {
                        subject: InvoiceDataBuilderService.new,
                        fail_fast: false,
                        comparator:
                      })
      end
    end
  end
end
