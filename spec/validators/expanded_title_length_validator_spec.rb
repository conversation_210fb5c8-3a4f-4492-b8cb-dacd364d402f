# frozen_string_literal: true

require "rails_helper"

RSpec.describe ExpandedTitleLengthValidator, type: :model do
  let(:shop) { create(:shop) }

  before do
    # Remove the global stub for this spec
    allow_any_instance_of(ExpandedTitleLengthValidator).to receive(:validate).and_call_original
  end

  describe "simple title validation" do
    it "is valid when invoice_title is under the max length" do
      setting = build(:shop_setting, shop: shop, invoice_title: "Short Title", refund_title: "Short Refund")
      expect(setting).to be_valid
    end

    it "is invalid when invoice_title exceeds the max length" do
      setting = build(:shop_setting, shop: shop, invoice_title: "A" * 26, refund_title: "Short Refund")
      expect(setting).not_to be_valid
      expect(setting.errors.where(:invoice_title, :too_long)).to be_present
      expect(setting.errors.where(:invoice_title,
        :too_long).first.options[:max_length]).to eq(ExpandedTitleLengthValidator::MAX_LENGTH)
    end

    it "is valid when refund_title is under the max length" do
      setting = build(:shop_setting, shop: shop, invoice_title: "Short Title", refund_title: "Short Refund")
      expect(setting).to be_valid
    end

    it "is invalid when refund_title exceeds the max length" do
      setting = build(:shop_setting, shop: shop, invoice_title: "Short Title", refund_title: "B" * 26)
      expect(setting).not_to be_valid
      expect(setting.errors.where(:refund_title, :too_long)).to be_present
      expect(setting.errors.where(:refund_title,
        :too_long).first.options[:max_length]).to eq(ExpandedTitleLengthValidator::MAX_LENGTH)
    end
  end

  describe "expanded liquid title validation" do
    before do
      allow(Rails.cache).to receive(:read).and_return(nil)
    end

    it "is valid when expanded invoice_title is under the max length" do
      sample_order = build(:order, name: "ShortName")
      allow(ShopifyAPI::Order).to receive(:all).and_return([sample_order])
      allow(ShopifyAPI::Order).to receive(:new).and_return(sample_order)
      setting = build(:shop_setting, shop: shop, invoice_title: "Order: {{ name }}")
      expect(setting).to be_valid
    end

    it "is invalid when expanded invoice_title exceeds the max length" do
      long_name = "A" * 30
      sample_order = build(:order, name: long_name)
      allow(ShopifyAPI::Order).to receive(:all).and_return([sample_order])
      allow(ShopifyAPI::Order).to receive(:new).and_return(sample_order)
      setting = build(:shop_setting, shop: shop, invoice_title: "Order: {{ name }}")
      expect(setting).not_to be_valid
      expect(setting.errors.where(:invoice_title, :expanded_too_long)).to be_present
      expect(setting.errors.where(:invoice_title,
        :expanded_too_long).first.options[:max_length]).to eq(ExpandedTitleLengthValidator::MAX_LENGTH)
      expect(setting.errors.where(:invoice_title,
        :expanded_too_long).first.options[:current_length]).to be > ExpandedTitleLengthValidator::MAX_LENGTH
    end

    it "is valid when expanded refund_title is under the max length" do
      sample_order = build(:order, name: "ShortName")
      allow(ShopifyAPI::Order).to receive(:all).and_return([sample_order])
      allow(ShopifyAPI::Order).to receive(:new).and_return(sample_order)
      setting = build(:shop_setting, shop: shop, refund_title: "Refund: {{ name }}")
      expect(setting).to be_valid
    end

    it "is invalid when expanded refund_title exceeds the max length" do
      long_name = "B" * 30
      sample_order = build(:order, name: long_name)
      allow(ShopifyAPI::Order).to receive(:all).and_return([sample_order])
      allow(ShopifyAPI::Order).to receive(:new).and_return(sample_order)
      setting = build(:shop_setting, shop: shop, refund_title: "Refund: {{ name }}")
      expect(setting).not_to be_valid
      expect(setting.errors.where(:refund_title, :expanded_too_long)).to be_present
      expect(setting.errors.where(:refund_title,
        :expanded_too_long).first.options[:max_length]).to eq(ExpandedTitleLengthValidator::MAX_LENGTH)
      expect(setting.errors.where(:refund_title,
        :expanded_too_long).first.options[:current_length]).to be > ExpandedTitleLengthValidator::MAX_LENGTH
    end
  end

  describe "handles errors gracefully" do
    it "does not add errors if sample order cannot be fetched" do
      setting = build(:shop_setting, shop: shop, invoice_title: "Order: {{ name }}")
      allow(Rails.cache).to receive(:read).and_return(nil)
      allow(shop).to receive(:with_shopify_session).and_yield
      allow(ShopifyAPI::Order).to receive(:all).and_raise(StandardError)
      expect(setting).to be_valid
    end
  end
end
