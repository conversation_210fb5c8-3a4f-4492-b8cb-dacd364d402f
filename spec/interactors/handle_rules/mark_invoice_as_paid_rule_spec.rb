# frozen_string_literal: true

require 'rails_helper'

RSpec.describe HandleRules::MarkInvoiceAsPaidRule do
  before do
    allow_any_instance_of(SyncRule).to receive(:handle_webhooks).and_return(nil)
    allow_any_instance_of(ShopSetting).to receive(:handle_rules).and_return(nil)
  end

  describe '#mark_invoice_as_paid?' do
    context 'when mark invoice is paid equal to true' do
      let(:shop_setting) { FactoryBot.create(:shop_setting, mark_invoice_as_paid: true) }
      let(:response) { described_class.new(shop_setting:).send(:mark_invoice_as_paid?) }

      it 'should return true' do
        expect(response).to be_truthy
      end
    end

    context 'when mark invoice is paid equal to false' do
      let(:shop_setting) { FactoryBot.create(:shop_setting, mark_invoice_as_paid: false) }
      let(:response) { described_class.new(shop_setting:).send(:mark_invoice_as_paid?) }

      it 'should return true' do
        expect(response).to be_falsey
      end
    end
  end

  describe 'create_rule' do
    let(:shop_setting) { FactoryBot.create(:shop_setting) }
    let(:response) { described_class.new(shop_setting:, shop: shop_setting.shop).send(:create_rule) }

    it 'should create sync rule' do
      expect { response }.to change { SyncRule.count }.by(1)
    end
  end

  describe 'destroy_rule' do
    let(:shop_setting) { FactoryBot.create(:shop_setting) }
    let(:response) { described_class.new(shop_setting:, shop: shop_setting.shop).send(:destroy_rule) }

    before do
      allow(ShopifyAPI::Webhook).to receive(:find).and_return(nil)
      allow(ShopifyAPI::Webhook).to receive(:all).and_return(nil)
      FactoryBot.create(:sync_rule, shop: shop_setting.shop, shopify_entity_type: 'Order', target_type: 'Invoice',
                                    target_action: 'MarkInvoicePaid', live_sync: true)
    end

    it 'should destroy sync rules' do
      expect { response }.to change { SyncRule.count }.by(-1)
    end
  end
end
