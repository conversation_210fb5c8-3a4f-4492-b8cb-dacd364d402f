# frozen_string_literal: true

require 'rails_helper'

RSpec.describe HandleRules::CreateInvoicesRule do
  before do
    allow_any_instance_of(SyncRule).to receive(:handle_webhooks).and_return(nil)
    allow_any_instance_of(ShopSetting).to receive(:handle_rules).and_return(nil)
  end

  describe '#create_invoices?' do
    context 'when create invoices true' do
      let(:shop_setting) { FactoryBot.create(:shop_setting, create_invoices: true) }
      let(:response) { described_class.new(shop_setting:).send(:create_invoices?) }

      it 'should return true' do
        expect(response).to be_truthy
      end
    end

    context 'when create invoices false' do
      let(:shop_setting) { FactoryBot.create(:shop_setting, create_invoices: false) }
      let(:response) { described_class.new(shop_setting:).send(:create_invoices?) }

      it 'should return true' do
        expect(response).to be_falsey
      end
    end
  end

  describe 'create_rules' do
    let(:shop_setting) { FactoryBot.create(:shop_setting, create_invoices: true) }
    let(:response) { described_class.new(shop_setting:, shop: shop_setting.shop).send(:create_rules) }

    context 'when invoice timing create' do
      let(:shop_setting) { FactoryBot.create(:shop_setting, create_invoices: true, invoice_timing: 'orders/create') }

      before do
        allow(ShopifyAPI::Webhook).to receive(:find).and_return(nil)
        allow(ShopifyAPI::Webhook).to receive(:all).and_return(nil)
      end

      it 'should create sync rule' do
        expect { response }.to change { SyncRule.count }.by(1)
      end
    end

    context 'when invoice timing fulfill' do
      let(:shop_setting) { FactoryBot.create(:shop_setting, create_invoices: true, invoice_timing: 'orders/fulfilled') }

      before do
        allow(ShopifyAPI::Webhook).to receive(:find).and_return(nil)
        allow(ShopifyAPI::Webhook).to receive(:all).and_return(nil)
      end

      it 'should create sync rule' do
        expect { response }.to change { SyncRule.count }.by(1)
      end
    end
  end

  describe 'destroy_rules' do
    let(:shop_setting) { FactoryBot.create(:shop_setting) }
    let(:response) { described_class.new(shop_setting:, shop: shop_setting.shop).send(:destroy_rules) }

    before do
      allow(ShopifyAPI::Webhook).to receive(:find).and_return(nil)
      allow(ShopifyAPI::Webhook).to receive(:all).and_return(nil)
      FactoryBot.create(:sync_rule, shop: shop_setting.shop, shopify_entity_type: 'Order', target_type: 'Invoice',
                                    target_action: 'Create', webhooks: 'orders/create', live_sync: true)
      FactoryBot.create(:sync_rule, shop: shop_setting.shop, shopify_entity_type: 'Order', target_type: 'Invoice',
                                    target_action: 'Fulfill', webhooks: 'orders/fulfilled', live_sync: true)
    end

    it 'should destroy sync rules' do
      expect { response }.to change { SyncRule.count }.by(-2)
    end
  end
end
