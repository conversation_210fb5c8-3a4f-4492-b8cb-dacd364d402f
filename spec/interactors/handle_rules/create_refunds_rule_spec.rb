# frozen_string_literal: true

require 'rails_helper'

RSpec.describe HandleRules::CreateRefundsRule do
  before do
    allow_any_instance_of(SyncRule).to receive(:handle_webhooks).and_return(nil)
    allow_any_instance_of(ShopSetting).to receive(:handle_rules).and_return(nil)
  end

  describe '#create_refunds?' do
    context 'when create refunds true' do
      let(:shop_setting) { FactoryBot.create(:shop_setting, create_refunds: true) }
      let(:response) { described_class.new(shop_setting:).send(:create_refunds?) }

      it 'should return true' do
        expect(response).to be_truthy
      end
    end

    context 'when create refunds false' do
      let(:shop_setting) { FactoryBot.create(:shop_setting, create_refunds: false) }
      let(:response) { described_class.new(shop_setting:).send(:create_refunds?) }

      it 'should return true' do
        expect(response).to be_falsey
      end
    end
  end

  describe 'create_rule' do
    let(:shop_setting) { FactoryBot.create(:shop_setting) }
    let(:response) { described_class.new(shop_setting:, shop: shop_setting.shop).send(:create_rule) }

    it 'should create sync rule' do
      expect { response }.to change { SyncRule.count }.by(1)
    end
  end

  describe 'destroy_rule' do
    let(:shop_setting) { FactoryBot.create(:shop_setting) }
    let(:response) { described_class.new(shop_setting:, shop: shop_setting.shop).send(:destroy_rule) }

    before do
      allow(ShopifyAPI::Webhook).to receive(:find).and_return(nil)
      allow(ShopifyAPI::Webhook).to receive(:all).and_return(nil)
      FactoryBot.create(:sync_rule, shop: shop_setting.shop, shopify_entity_type: 'Refund', target_type: 'Refund',
                                    target_action: 'Create', live_sync: true)
    end

    it 'should destroy sync rules' do
      expect { response }.to change { SyncRule.count }.by(-1)
    end
  end
end
