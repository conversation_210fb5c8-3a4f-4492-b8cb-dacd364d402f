# frozen_string_literal: true

require 'rails_helper'

RSpec.describe InvoiceDataBuilder::FindOrCreateSyncInfo do

  describe '#call' do
    let!(:shop) { create(:shop, :with_shop_setting) }
    let(:order) do
      build(:order, :with_tax_lines, :with_line_items, :with_customer, :with_shipping_lines, :with_billing_address)
    end
    let(:invoice) { Lexoffice::Invoice.new(shop.lexoffice_token) }
    let(:rule) { create(:sync_rule) }
    let(:is_import_job) { [true, false].sample }
    let(:context) do
      described_class.call(order:, shop_settings: shop.shop_setting, invoice:, rule:, is_import_job:)
    end

    it 'should return sync info' do
      expect(context).to be_a_success
      expect(context.sync_info.shopify_id).to eq order.id.to_s
      expect(context.sync_info.import).to eq is_import_job
    end
  end
end
