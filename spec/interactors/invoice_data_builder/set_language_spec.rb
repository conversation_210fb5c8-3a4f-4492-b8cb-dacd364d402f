# frozen_string_literal: true

require 'rails_helper'

RSpec.describe InvoiceDataBuilder::SetLanguage do

  describe '#call' do
    let!(:shop) { create(:shop, :with_shop_setting) }
    let(:invoice) { Lexoffice::Invoice.new(shop.lexoffice_token) }

    let(:context) { described_class.call(shop_settings: shop.shop_setting, invoice:) }

    before do
      invoice.data = {
        language: 'de'
      }
      shop.shop_setting.update(invoice_language: 'en')
    end

    it 'should set invoice language field' do
      expect(context.invoice.data[:language]).to eq 'en'
    end
  end
end
