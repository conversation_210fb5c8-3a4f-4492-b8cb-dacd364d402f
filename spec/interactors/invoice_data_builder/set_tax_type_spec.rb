# frozen_string_literal: true

require 'rails_helper'

RSpec.describe InvoiceDataBuilder::SetTaxType do

  describe '#call' do
    let!(:shop) { create(:shop, :with_shop_setting) }
    let(:order) do
      build(:order, :with_tax_lines, :with_line_items, :with_customer, :with_shipping_lines, :with_billing_address)
    end
    let(:invoice) { Lexoffice::Invoice.new(shop.lexoffice_token) }

    let(:context) do
      described_class.call(order:, shop_settings: shop.shop_setting, invoice:, shop:, tax_type: 'net')
    end

    before do
      invoice.data = {
        taxConditions: {}
      }
      shop.shop_setting.update(shipping_type: 'period')
    end

    it 'should set invoice data fields' do
      expect(context.invoice.data[:taxConditions][:taxType]).to eq 'net'
      expect(context.invoice.data[:shippingConditions][:shippingType]).to eq 'period'
    end
  end
end
