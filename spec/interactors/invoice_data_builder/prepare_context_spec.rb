require 'rails_helper'

RSpec.describe InvoiceDataBuilder::PrepareContext do

  describe '#call' do
    let(:shop) { create(:shop, :with_shop_setting) }
    let(:rule) { create(:sync_rule, target_type: 'Invoice', target_action: 'Create') }
    let(:context) { described_class.call(shop: shop, rule:) }

    it 'should set invoice' do
      expect(context).to be_a_success
      expect(context.invoice).not_to be_nil
      expect(context.invoice.auth_token).to eq shop.lexoffice_token
    end
  end
end
