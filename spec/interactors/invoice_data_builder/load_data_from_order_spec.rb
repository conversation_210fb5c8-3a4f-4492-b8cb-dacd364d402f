# frozen_string_literal: true

require "rails_helper"

RSpec.describe InvoiceDataBuilder::LoadDataFromOrder do
  describe "#call" do
    let!(:shop) { create(:shop, :with_shop_setting) }
    let(:order) do
      build(:order, :with_tax_lines, :with_line_items, :with_customer, :with_shipping_lines, :with_billing_address,
        :with_fulfillments, billing_address: {
          first_name: "<PERSON>",
          last_name: "<PERSON><PERSON>",
          address1: "123 Main St",
          zip: "12345",
          city: "Berlin",
          country_code: "DE"
        })
    end
    let(:invoice) { Lexoffice::Invoice.new(shop.lexoffice_token) }
    let(:rule) { build(:sync_rule) }
    let(:context) { described_class.call(order:, shop_settings: shop.shop_setting, invoice:, rule:) }

    before do
      stub_shopify_authenticated(shop)
      shop.shop_setting.update!(invoice_timing: "orders/fulfilled")
    end

    it "should load data from order" do
      expect(context).to be_a_success
      expect(context.invoice.data[:voucherDate]).to eq Time.parse(order.fulfillments.first.created_at).iso8601(3)
    end
  end
end
