require 'rails_helper'

RSpec.describe InvoiceDataBuilder::<PERSON>reate<PERSON>ustomer do

  describe '#call' do
    let(:shop) { create(:shop, :with_shop_setting) }

    context 'with contact id' do
      let(:invoice) { Lexoffice::Invoice.new(shop.lexoffice_token) }
      let(:contact_id) { SecureRandom.hex(12) }
      let(:context) { described_class.call(invoice: invoice, contact_id: contact_id, shop_settings: shop.shop_setting) }

      before do
        invoice.data = {
          address: {}
        }
      end
      it 'should set contact id' do
        expect(context).to be_a_success
        expect(context.invoice.data[:address][:contactId]).to eq contact_id
      end
    end

    context 'without contact id' do
      let(:invoice) { Lexoffice::Invoice.new(shop.lexoffice_token) }
      let(:context) { described_class.call(invoice: invoice, shop_settings: shop.shop_setting, contact_checker: true) }

      before do
        invoice.data = {
          address: {}
        }
      end

      it 'should set customer id' do
        expect(context).to be_a_success
        expect(context.invoice.data[:address][:name]).to eq 'Sammelkunde Shopify'
      end
    end
  end
end
