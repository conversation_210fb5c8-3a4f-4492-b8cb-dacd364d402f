# frozen_string_literal: true

require 'rails_helper'

RSpec.describe InvoiceDataBuilder::SetDiscount do
  describe '#call' do
    let!(:shop) { build(:shop, :with_shop_setting) }
    let(:invoice) { build_invoice(shop.lexoffice_token) }
    let(:order) { build(:order) }
    let(:context) { described_class.call(order:, shop_settings: shop.shop_setting, invoice:) }

    before do
      stub_shopify_authenticated(shop)
      initialize_invoice_data(invoice)
    end

    context 'when total discounts is zero' do
      let(:order) { build(:order, total_discounts: '0.00') }

      it 'does not call SetAbsoluteDiscountService' do
        expect(SetAbsoluteDiscountService).not_to receive(:new)
        described_class.call(order:, invoice:)
      end
    end

    context 'when total discounts is not zero' do
      let(:order) { build_discounted_order }

      it 'calls SetAbsoluteDiscountService' do
        service_instance = instance_double(SetAbsoluteDiscountService, call: true)
        expect(SetAbsoluteDiscountService).to receive(:new).with(context.shop,
                                                                 context.order,
                                                                 context.invoice,
                                                                 context.shop_settings).and_return(service_instance)
        expect(service_instance).to receive(:call)

        described_class.call(order: context.order, invoice: context.invoice)
      end
    end
  end
end
