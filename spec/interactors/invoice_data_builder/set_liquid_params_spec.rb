# frozen_string_literal: true

require 'rails_helper'

RSpec.describe InvoiceDataBuilder::SetLiquidParams do
  describe '#call' do
    let!(:shop) { create(:shop, :with_shop_setting) }
    let(:order) do
      build(:order, :with_tax_lines, :with_line_items, :with_customer, :with_shipping_lines, :with_billing_address)
    end
    let(:invoice) { Lexoffice::Invoice.new(shop.lexoffice_token) }
    let(:snippet) { FactoryBot.create(:snippet, :for_transaction) }
    let(:transaction) { build(:transaction) }
    let(:context) { described_class.call(order:, shop_settings: shop.shop_setting, invoice:) }

    before do
      invoice.data = {}
    end

    it 'should set invoice data fields' do
      expect(context.invoice.data[:title]).not_to be_nil
      expect(context.invoice.data[:introduction]).not_to be_nil
    end

    context 'when order transactions needed' do
      before do
        shop.shop_setting.update(invoice_pretext: snippet.code)
        allow_any_instance_of(ShopifyAPI::Order).to receive(:transactions).and_return([transaction])
      end

      it 'should set the transaction amount' do
        expect(context.invoice.data[:introduction])
          .to match(
            "Hallo #{order.customer.first_name}, sie erhalten heute Ihre Rechnung zu Auftrag #{order.name}. " \
            "Ihre Transaktionen: #{transaction.amount}"
          )
      end
    end
  end
end
