# frozen_string_literal: true

require 'rails_helper'

RSpec.describe InvoiceDataBuilder::AddLineItems do
  describe '#call' do
    let!(:shop) { create(:shop, :with_shop_setting) }
    let(:order) do
      build(:order, :with_tax_lines, :with_line_items,
            :with_customer, :with_shipping_lines, :with_billing_address) do |order|
        order.line_items.each_with_index do |line_item, _index|
          line_item[:title] = Faker::Lorem.characters
          line_item[:name] = Faker::Lorem.characters
        end

        order.shipping_lines.each_with_index do |shipping_line_item, _index|
          shipping_line_item[:title] = Faker::Lorem.characters
        end
      end
    end

    let(:french_order) do
      build(:order, :with_line_items, :with_shipping_lines) do |order|
        order.line_items.each do |line_item|
          line_item['tax_lines'].first['rate'] = 0.055
          line_item['taxable'] = true
        end

        order.shipping_lines.each do |shipping_line|
          shipping_line[:price] = '8.00'
          shipping_line[:title] = 'Standard Shipping'
          shipping_line[:tax_lines] = []
        end
      end
    end
    let(:missing_shipping_tax_order) do
      build(:order, :with_shipping_lines, :with_line_items, line_items_count: 2) do |order|
        order.shipping_lines.each do |shipping_line|
          shipping_line['price'] = '8.00'
          shipping_line['title'] = 'Standard Shipping'
          shipping_line['tax_lines'] = [build(:tax_line, rate: 0.19)]
        end

        order.line_items.each_with_index do |line_item, index|
          line_item['tax_lines'].first['rate'] = if index == 0
                                                   0.07
                                                 else
                                                   0.19
                                                 end
        end
      end
    end
    let(:free_shipping_order) do
      build(:order, :with_line_items, :with_shipping_lines, line_items_count: 5) do |order|
        order.line_items.each_with_index do |line_item, index|
          line_item[:title] = Faker::Lorem.characters
          line_item[:name] = Faker::Lorem.characters
          line_item[:price] = 0 if index == 3
          line_item[:tax_lines] = [build(:tax_line, rate: 0.0)]
        end

        order.shipping_lines.each_with_index do |shipping_line_item, _index|
          shipping_line_item[:title] = Faker::Lorem.characters
        end
      end
    end
    let(:free_product_order) do
      build(:order, :with_line_items, :with_discounted_line_items, line_items_count: 5) do |order|
        order.line_items = [
          {
            'title' => Faker::Lorem.characters,
            'name' => Faker::Lorem.characters,
            'price' => '0.01',
            'taxable' => true,
            'tax_lines' => [{ 'rate' => 0.00, 'price' => '0.00' }],
            'discount_allocations' => [{ 'amount' => '0.01' }]
          },
          {
            'title' => Faker::Lorem.characters,
            'name' => Faker::Lorem.characters,
            'price' => '10.00',
            'tax_lines' => [{ 'rate' => 0.19, 'price' => '1.90' }],
            'discount_allocations' => []
          }
        ]
      end
    end
    let(:invoice) { Lexoffice::Invoice.new(shop.lexoffice_token) }

    let(:context) { described_class.call(order:, shop_settings: shop.shop_setting, invoice:) }

    before do
      stub_shopify_authenticated(shop)
      invoice.data = {
        taxConditions: {}
      }
    end

    it 'should add line items to invoice' do
      expect(context.invoice.data[:lineItems].length).to eq(order.line_items.length + order.shipping_lines.length)
    end

    context 'line items with decimal tax rates' do
      let(:context) { described_class.call(order: french_order, shop_settings: shop.shop_setting, invoice:) }

      it 'should add line items with decimal tax rates to invoice' do
        expect(context.invoice.data[:lineItems].first[:unitPrice][:taxRatePercentage]).to eq(5.5)
      end
    end

    context 'with calculate shipping tax enabled' do
      let(:context) do
        described_class.call(order: missing_shipping_tax_order, shop_settings: shop.shop_setting, invoice:)
      end

      before { shop.shop_setting.update(calculate_shipping_tax: true) }

      subject { context.invoice.data[:lineItems].find { |el| el[:name] == 'Standard Shipping' } }

      it 'calculates shipping tax as highest line items tax rate' do
        expect(subject[:unitPrice][:taxRatePercentage]).to eq(19)
      end
    end

    context 'when using fulfillment with multiple fulfillment objects' do
      let(:order_with_fulfillments) do
        build(:order) do |order|
          order.fulfillments = [
            build_fulfillment_with_line_items([{ discount_amounts: ['5.00'] }]),
            build_fulfillment_with_line_items([{ discount_amounts: ['10.00'] }])
          ]
        end
      end

      before do
        shop.shop_setting.update(invoice_timing: 'fulfill')
        allow(order_with_fulfillments).to receive(:use_fulfillment?).and_return(true)
      end

      it 'joins line items from all fulfillments' do
        expected_line_items_count = order_with_fulfillments.fulfillments.flat_map(&:line_items).count
        expect(context.invoice.data[:lineItems].length).to eq(expected_line_items_count)
      end
    end

    context 'invoice with free shipping' do
      let(:context) do
        described_class.call(order: free_shipping_order, shop_settings: shop.shop_setting, invoice:)
      end

      it 'should add free line items to invoice' do
        expect(context.invoice.data[:lineItems].any? { |el| el[:unitPrice]['netAmount'].to_d == 0 }).to be_truthy
      end

      it 'should not add shipping lines to invoice' do
        expect(context.invoice.data[:lineItems].none? { |el| el[:name] == 'Kostenloser Versand' }).to be_truthy
      end
    end

    context 'when checking taxable_with_zero_tax_rate?' do
      it 'returns true for line item with zero tax rate' do
        line_item = { taxable: true, tax_lines: [{ rate: 7.0, price: '0.0' }] }
        expect(ShopifyAPI::LineItem.taxable_with_no_price?(line_item)).to be_truthy
      end

      it 'returns false for line item with non-zero tax rate' do
        line_item = { taxable: false, tax_lines: [{ rate: 7.0, price: '0.0' }] }
        expect(ShopifyAPI::LineItem.taxable_with_no_price?(line_item)).to be_falsey
      end
    end

    context 'invoice with free product from discount' do
      let(:context) do
        described_class.call(order: free_product_order, shop_settings: shop.shop_setting, invoice:)
      end

      it 'should correctly identify free discount with zero tax rate' do
        first_line_item = free_product_order.line_items.first.deep_symbolize_keys
        expect(ShopifyAPI::LineItem.free_discount_with_zero_tax_rate?(first_line_item)).to be_truthy
      end

      it 'should calculate the highest tax rate correctly' do
        highest_tax_rate = free_product_order.highest_tax_rate
        expect(highest_tax_rate).to eq(BigDecimal('19.00'))
      end

      it 'should apply the highest tax rate to the line item' do
        expect(context.invoice.data[:lineItems].first[:unitPrice][:taxRatePercentage]).to eq(19.00)
      end
    end
  end
end
