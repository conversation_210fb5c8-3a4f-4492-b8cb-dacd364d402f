# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CreditNoteDataBuilder::SetLiquidParams do

  describe '#call' do
    let(:shop) { create(:shop, :with_shop_setting) }
    let(:order) { build(:order, :with_line_items, :with_shipping_lines, :with_customer, :with_refund) }
    let(:refund) { build(:refund, prefix_options: { order_id: order.id }) }
    let(:credit_note) { Lexoffice::CreditNote.new(shop.lexoffice_token) }
    let(:context) do
      described_class.call(order_and_refund: order, shop_settings: shop.shop_setting, credit_note:, order:, refund:)
    end
    let(:snippet) { FactoryBot.create(:snippet, :for_transaction) }
    let(:transaction) { build(:transaction) }

    before do
      stub_shopify_authenticated(shop)
      credit_note.data = {}
    end

    it 'should set liquid params' do
      expect(context.credit_note.data[:title]).not_to be_nil
    end

    context 'when order transactions needed' do
      before do
        shop.shop_setting.update(refund_pretext: snippet.code)
        allow_any_instance_of(ShopifyAPI::Order).to receive(:transactions).and_return([transaction])
      end

      it 'should set the transaction amount' do
        expect(context.credit_note.data[:introduction])
          .to match(
            "Hallo #{order.customer.first_name}, sie erhalten heute Ihre Rechnung zu Auftrag #{order.name}. " \
            "Ihre Transaktionen: #{transaction.amount}"
          )
      end
    end
  end
end
