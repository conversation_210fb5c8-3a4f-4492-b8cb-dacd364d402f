require 'rails_helper'

RSpec.describe CreditNoteDataBuilder::SetTaxType do

  describe '#call' do
    let(:shop) { create(:shop, :with_shop_setting) }
    let(:credit_note) { Lexoffice::CreditNote.new(shop.lexoffice_token) }

    before do
      credit_note.data = {
        taxConditions: {}
      }
    end

    context 'when use brutto is true' do
      let(:context) { described_class.call(credit_note:, shop:, use_brutto: true, tax_type: 'gross') }

      it 'should set tax type' do
        expect(context).to be_a_success
        expect(context.credit_note).not_to be_nil
        expect(context.credit_note.data[:taxConditions][:taxType]).to eq 'gross'
      end
    end

    context 'when use brutto is false' do
      let(:context) { described_class.call(credit_note:, shop:, use_brutto: false, tax_type: 'net') }

      it 'should set tax type' do
        expect(context).to be_a_success
        expect(context.credit_note).not_to be_nil
        expect(context.credit_note.data[:taxConditions][:taxType]).to eq 'net'
      end
    end

    context 'when shop lexoffice small business is true' do
      let(:context) do
        described_class.call(credit_note:, shop:, use_brutto: false, tax_type: 'vatfree')
      end

      before do
        shop.update(lexoffice_small_business: true)
      end

      it 'should set tax type' do
        expect(context).to be_a_success
        expect(context.credit_note).not_to be_nil
        expect(context.credit_note.data[:taxConditions][:taxType]).to eq 'vatfree'
      end
    end
  end
end
