# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CreditNoteDataBuilder::AddLineItems do

  describe '#call' do
    let(:shop) { create(:shop, :with_shop_setting) }
    let(:shop_settings) { shop.shop_setting }
    let(:order) { build(:order, :with_line_items, :with_shipping_lines) }
    let(:refund) { build(:refund, prefix_options: { order_id: order.id }) }
    let(:credit_note) { Lexoffice::CreditNote.new(shop.lexoffice_token) }
    let(:context) do
      described_class.call(order:, refund:, credit_note:, shop_settings:)
    end

    before do
      stub_shopify_authenticated(shop)
      credit_note.data = {
        taxConditions: {
          taxType: 'gross'
        }
      }
    end

    it 'should load add line items' do
      expect(context).to be_a_success
      expect(context.credit_note.data['lineItems']).not_to be_nil
    end
  end
end
