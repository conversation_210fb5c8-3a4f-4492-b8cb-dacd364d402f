# frozen_string_literal: true

require "rails_helper"

RSpec.describe CreditNoteDataBuilder::LoadDataFromRefundAndOrder do
  before do
    stub_shopify_authenticated(shop)
  end

  describe "#call" do
    let(:shop) { create(:shop, :with_shop_setting) }
    let(:order) do
      build(:order, :with_line_items, :with_billing_address, :with_shipping_address, total_discount: "1.00")
    end
    let(:refund) { build(:refund, prefix_options: { order_id: 5_075_660_229 }) }
    let(:credit_note) { Lexoffice::CreditNote.new(shop.lexoffice_token) }
    let(:rule) { build(:sync_rule) }
    let(:context) do
      described_class.call(order:, refund:, credit_note:, shop_settings: shop.shop_setting, rule:)
    end

    it "should load data from refund and order" do
      expect(context).to be_a_success
      expect(context.credit_note.data[:voucherDate]).to eq Time.parse(refund.processed_at).iso8601(3)
      expect(context.credit_note.data[:address][:name]).to eq "#{order.billing_address["first_name"]} #{order
        .billing_address["last_name"]}"
    end
  end
end
