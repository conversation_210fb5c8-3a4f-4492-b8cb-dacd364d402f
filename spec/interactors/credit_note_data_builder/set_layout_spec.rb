# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CreditNoteDataBuilder::SetLayout do
  describe '#call' do
    let!(:shop) { create(:shop, :with_shop_setting) }
    let(:credit_note) { Lexoffice::CreditNote.new(shop.lexoffice_token) }

    before do
      allow(shop).to receive(:billing_plan).and_return(billing_plan)
    end

    context 'when the shop has custom_layouts feature enabled' do
      let(:billing_plan) { create(:billing_plan, features: ['custom_layouts']) }

      it 'should set the custom layout' do
        expect(credit_note).to receive(:set_layout).with(shop.shop_setting, 'refund')

        described_class.call(shop:, shop_settings: shop.shop_setting, credit_note:)
      end
    end

    context 'when the shop has custom_layouts feature disabled' do
      let(:billing_plan) { create(:billing_plan, features: %w[some other features]) }

      it 'should not set the custom layout' do
        expect(credit_note).to receive(:set_layout).never

        described_class.call(shop:, shop_settings: shop.shop_setting, credit_note:)
      end
    end
  end
end
