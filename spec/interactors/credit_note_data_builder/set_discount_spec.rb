# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CreditNoteDataBuilder::SetDiscount do
  describe '#call' do
    let!(:shop) { create(:shop, :with_shop_setting) }

    before do
      stub_shopify_authenticated(shop)
    end

    context 'when total discount not equal to 0 and differs from current total discounts' do
      let(:credit_note) { Lexoffice::CreditNote.new(shop.lexoffice_token) }

      let(:order) do
        build(
          :order,
          :with_line_items,
          :with_shipping_lines,
          total_discounts: '1.00',
          current_total_discounts: '0.00'
        )
      end

      let(:refund_line_items) do
        [
          {
            line_item: { price: '5.00' },
            quantity: 1,
            subtotal: '4.00'
          }
        ]
      end

      let(:refund) do
        build(:refund, refund_line_items:)
      end

      let(:context) { described_class.call(order:, refund:, credit_note:) }

      before do
        credit_note.data = {
          totalPrice: {}
        }
      end

      it 'sets the correct discount' do
        # The discount should be calculated based on calculate_absolute_discount
        expect(context.credit_note.data[:totalPrice][:totalDiscountAbsolute]).to eq(1.0)
      end

      context 'and current_total_discounts is 0.50' do
        let(:order) do
          build(
            :order,
            :with_line_items,
            :with_shipping_lines,
            total_discounts: '1.00',
            current_total_discounts: '0.50'
          )
        end

        let(:refund_line_items) do
          [
            {
              line_item: { price: '5.00' },
              quantity: 1,
              subtotal: '4.50'
            }
          ]
        end

        let(:refund) do
          build(:refund, refund_line_items:)
        end

        let(:context) { described_class.call(order:, refund:, credit_note:) }

        before do
          credit_note.data = {
            totalPrice: {}
          }
        end

        it 'sets the correct discount' do
          expect(context.credit_note.data[:totalPrice][:totalDiscountAbsolute]).to eq(0.5)
        end
      end
    end

    context 'when total discount is zero' do
      let(:credit_note) { Lexoffice::CreditNote.new(shop.lexoffice_token) }

      let(:order) do
        build(
          :order,
          :with_line_items,
          :with_shipping_lines,
          total_discounts: '0.00',
          current_total_discounts: '0.00'
        )
      end

      let(:refund) do
        build(:refund, refund_line_items: [])
      end

      let(:context) { described_class.call(order:, refund:, credit_note:) }

      before do
        credit_note.data = {
          totalPrice: {}
        }
      end

      it 'does not set a discount' do
        expect(context.credit_note.data[:totalPrice][:totalDiscountAbsolute]).to be_nil
      end
    end

    context 'when total discount equals current total discounts' do
      let(:credit_note) { Lexoffice::CreditNote.new(shop.lexoffice_token) }

      let(:order) do
        build(
          :order,
          :with_line_items,
          :with_shipping_lines,
          total_discounts: '1.00',
          current_total_discounts: '1.00'
        )
      end

      let(:refund) do
        build(:refund, refund_line_items: [])
      end

      let(:context) { described_class.call(order:, refund:, credit_note:) }

      before do
        credit_note.data = {
          totalPrice: {}
        }
      end

      it 'does not set a discount' do
        expect(context.credit_note.data[:totalPrice][:totalDiscountAbsolute]).to be_nil
      end
    end
  end
end
