# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CreditNoteDataBuilder::FindOrCreateSyncInfo do

  describe '#call' do
    let(:shop) { create(:shop, :with_shop_setting) }
    let(:order) { build(:order, :with_line_items, total_discount: '1.00') }
    let(:refund) { build(:refund, prefix_options: { order_id: order.id }) }
    let(:is_import_job) { [true, false].sample }

    let(:context) { described_class.call(order:, shop:, is_import_job:, refund:) }

    before do
      stub_shopify_authenticated(shop)
    end

    it 'should return sync info' do
      expect(context).to be_a_success
      expect(context.sync_info.shopify_id).to eq refund.id.to_s
      expect(context.sync_info.import).to eq is_import_job
    end
  end
end
