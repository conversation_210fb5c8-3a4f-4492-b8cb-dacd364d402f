# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CreditNoteDataBuilder::PrepareContext do
  before do
    stub_shopify_authenticated
  end

  describe '#call' do
    let(:shop) { create(:shop, :with_shop_setting) }
    let(:order) { build(:order, :with_line_items, total_discount: '1.00') }
    let(:refund) { build(:refund, prefix_options: { order_id: order.id }) }

    let(:context) { described_class.call(shop:, order:, refund:) }

    it 'should set credit note' do
      expect(context).to be_a_success
      expect(context.credit_note).not_to be_nil
      expect(context.credit_note.auth_token).to eq shop.lexoffice_token
    end
  end
end
