require 'rails_helper'

RSpec.describe CreditNoteDataBuilder::CreateShopCustomer do

  describe '#call' do
    let(:shop) { create(:shop, :with_shop_setting) }

    context 'with contact id' do
      let(:credit_note) { Lexoffice::CreditNote.new(shop.lexoffice_token) }
      let(:contact_id) { SecureRandom.hex(12) }
      let(:context) { described_class.call(credit_note:, contact_id:, shop_settings: shop.shop_setting) }

      before do
        credit_note.data = {
          address: {}
        }
      end

      it 'should set contact id' do
        expect(context).to be_a_success
        expect(context.credit_note.data[:address][:contactId]).to eq contact_id
      end
    end

    context 'without contact id' do
      let(:credit_note) { Lexoffice::CreditNote.new(shop.lexoffice_token) }
      let(:context) do
        described_class.call(credit_note: credit_note, shop_settings: shop.shop_setting, contact_checker: true)
      end

      before do
        credit_note.data = {
          address: {}
        }
      end

      it 'should set customer id' do
        expect(context).to be_a_success
        expect(context.credit_note.data[:address][:name]).to eq 'Sammelkunde Shopify'
      end
    end
  end
end
