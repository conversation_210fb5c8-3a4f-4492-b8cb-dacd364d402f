# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CreditNoteDataBuilder::SetLanguage do

  describe '#call' do
    let!(:shop) { create(:shop, :with_shop_setting) }
    let(:credit_note) { Lexoffice::CreditNote.new(shop.lexoffice_token) }

    let(:context) { described_class.call(shop_settings: shop.shop_setting, credit_note:) }

    before do
      credit_note.data = {
        language: 'de'
      }
      shop.shop_setting.update(invoice_language: 'en')
    end

    it 'should set invoice language field' do
      expect(context.credit_note.data[:language]).to eq 'en'
    end
  end
end
