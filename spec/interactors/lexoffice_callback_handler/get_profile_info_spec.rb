require 'rails_helper'

RSpec.describe LexofficeCallbackHandler::GetProfileInfo do
  describe '#call' do
    let(:shop) { FactoryBot.create(:shop) }
    let(:organization_id) { SecureRandom.hex(5) }
    let(:small_business) { SecureRandom.hex(5) }
    let(:tax_type) { %w[grossAmount netAmount].sample }
    let(:connection_id) { SecureRandom.hex(5) }
    let(:body) do
      {
        organizationId: organization_id,
        taxType: tax_type,
        smallBusiness: small_business,
        connectionId: connection_id
      }
    end

    let(:response) { described_class.call(shop: shop) }

    before do
      stub_request(:get, /profile/).to_return(status: 200, body: body.to_json)
    end

    it 'should get profile info' do
      expect(response.profile_info['organizationId']).to eq organization_id
      expect(response.profile_info['taxType']).to eq tax_type
      expect(response.profile_info['smallBusiness']).to eq small_business
      expect(response.profile_info['connectionId']).to eq connection_id
    end
  end
end
