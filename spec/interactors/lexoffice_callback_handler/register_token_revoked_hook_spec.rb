require 'rails_helper'

RSpec.describe LexofficeCallbackHandler::RegisterTokenRevokedHook do
  describe '#call' do
    let(:shop) { FactoryBot.create(:shop) }
    let(:id) { SecureRandom.hex(12) }
    let(:response) { described_class.new(shop: shop).call }

    before do
      stub_request(:post, /event-subscription/)
        .to_return(status: 200, body: { id: id }.to_json)
    end

    it 'should register hook' do
      expect(response['id']).to eq id
    end
  end
end
