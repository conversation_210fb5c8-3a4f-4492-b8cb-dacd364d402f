require 'rails_helper'

RSpec.describe LexofficeCallbackHandler::SetProfileInfo do
  describe '#call' do
    let(:shop) { FactoryBot.create(:shop) }
    let(:organization_id) { SecureRandom.hex(5) }
    let(:small_business) { SecureRandom.hex(5) }
    let(:tax_type) { %w[grossAmount netAmount].sample }
    let(:connection_id) { SecureRandom.hex(5) }
    let(:profile_info) do
      {
        'organizationId' => organization_id,
        'taxType' => tax_type,
        'smallBusiness' => small_business,
        'connectionId' => connection_id
      }
    end

    let(:response) { described_class.call(shop: shop, profile_info: profile_info) }

    it 'should set profile info to shop' do
      expect { response }.to change(shop.reload, :lexoffice_connection_id)
    end
  end
end
