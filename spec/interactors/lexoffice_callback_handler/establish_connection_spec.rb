require 'rails_helper'

RSpec.describe LexofficeCallbackHandler::EstablishConnection do
  describe '#call' do
    let(:shop) { FactoryBot.create(:shop) }
    let(:auth_hash) do
      {
        credentials: {
          token: SecureRandom.hex(12),
          refresh_token: SecureRandom.hex(12),
          expires_at: (Time.now + 1.hour).to_i
        }
      }
    end

    let(:response) { described_class.call(shop: shop, auth_hash: auth_hash) }

    it 'should update shop' do
      expect { response }.to change(shop.reload, :connection_established_at)
      expect(response.shop.lexoffice_token).to eq auth_hash[:credentials][:token]
    end
  end
end
