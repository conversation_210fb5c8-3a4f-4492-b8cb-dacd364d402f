# frozen_string_literal: true

require "rails_helper"

RSpec.describe Support::SupportV1Controller, type: :controller do
  let(:shop) { create(:shop) }

  before do
    allow(ShopifyAPI::Order).to receive(:find).and_return(nil)
  end

  context "#view_order" do
    context "when not logged in" do
      before do
        get :view_order
      end

      it "should return unauthorized" do
        expect(response).to have_http_status 302
      end
    end

    context "when logged in" do
      before do
        sign_in create(:support_user)
      end

      it "should return success" do
        get :view_order, params: { order_id: 123, shop: shop.shopify_domain }
        expect(response).to have_http_status 200
      end

      it "should have a new support user event entry" do
        expect do
          get :view_order, params: { order_id: 123, shop: shop.shopify_domain }
        end.to(change { SupportUserEvent.count }.by(1))
      end
    end
  end
end
