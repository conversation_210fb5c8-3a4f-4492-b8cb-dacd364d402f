# frozen_string_literal: true

require 'rails_helper'

RSpec.describe WebhooksController, type: :controller do
  before do
    allow_any_instance_of(WebhooksController).to receive(:verify_request).and_return(true)
  end

  context '#customer_redact' do
    before { post :customer_redact }

    it 'should return http status ok' do
      expect(response).to have_http_status :ok
    end
  end

  context '#shop_redact' do
    before { post :shop_redact }

    it 'should return http status ok' do
      expect(response).to have_http_status :ok
    end
  end

  context '#customer_data_request' do
    before { post :customer_data_request }

    it 'should return http status ok' do
      expect(response).to have_http_status :ok
    end
  end

  return pending 'TODO: Integrate with fixtures removal branch'

  # rubocop:disable Lint/UnreachableCode
  let(:shop_owner) { Faker::Name.name }
  let(:shopify_domain) { 'XYZ.myshopify.com' }
  let(:email) { Faker::Internet.email }
  let(:shop) { FactoryBot.create(:shop, :with_shop_setting, shop_owner:, shopify_domain:, email:) }
  let(:shop_infos) { { shop_owner:, shopify_domain:, email: } }

  before do
    allow_any_instance_of(SyncRule).to receive(:handle_webhooks).and_return(nil)
    allow_any_instance_of(WebhooksController).to receive(:verify_request).and_return(true)
  end

  describe 'POST #uninstall' do
    context 'when a shop with the given shopify domain exists' do
      it 'deletes the shop and enqueues jobs for uninstall notification and Mailchimp unsubscribe' do
        expect do
          post :uninstall, params: { myshopify_domain: shopify_domain }
        end.to change(Shop, :count).by(-1)

        expect(response).to have_http_status(:ok)
        expect(NotificationsJob).to have_enqueued_sidekiq_job(shop_infos.to_json, 'uninstall', 'email')
        expect(NotificationsJob).to have_enqueued_sidekiq_job(shop_infos.to_json, 'uninstall', 'notification')
        expect(MailchimpUnsubscribeJob).to have_enqueued_sidekiq_job(shop_infos.to_json)
      end
    end
  end

  describe 'POST #customer_redact' do
    it 'returns a 200 OK response' do
      post :customer_redact
      expect(response).to have_http_status(:ok)
    end
  end

  describe 'POST #shop_redact' do
    it 'returns a 200 OK response' do
      post :shop_redact
      expect(response).to have_http_status(:ok)
    end
  end

  describe 'POST #customer_data_request' do
    it 'returns a 200 OK response' do
      post :customer_data_request
      expect(response).to have_http_status(:ok)
    end
  end

  describe 'POST #orders_create' do
    it 'enqueues a SyncOrderJob if there are order rules with live sync for the current shop' do
      FactoryBot.create(:sync_rule, sync_type: 'order', sync_trigger: 'create', live_sync: true, shop_id: shop.id)
      post :orders_create, params: { id: '1234' }
      expect(SyncOrderJob).to have_enqueued_sidekiq_job('orders/create', '1234', shop.id)
      expect(response).to have_http_status(:ok)
    end

    it 'returns a 200 OK response if there are no order rules with live sync for the current shop' do
      post :orders_create, params: { id: '1234' }
      expect(SyncOrderJob).not_to have_enqueued_sidekiq_job
      expect(response).to have_http_status(:ok)
    end
  end

  describe 'POST #orders_fulfilled' do
    it 'enqueues a SyncOrderJob if there are order rules with live sync for the current shop' do
      FactoryBot.create(:sync_rule, sync_type: 'order', sync_trigger: 'fulfilled', live_sync: true, shop_id: shop.id)
      post :orders_fulfilled, params: { id: '1234' }
      expect(SyncOrderJob).to have_enqueued_sidekiq_job('orders/fulfilled', '1234', shop.id)
      expect(response).to have_http_status(:ok)
    end

    it 'returns a 200 OK response if there are no order rules with live sync for the current shop' do
      post :orders_fulfilled, params: { id: '1234' }
      expect(SyncOrderJob).not_to have_enqueued_sidekiq_job
      expect(response).to have_http_status(:ok)
    end

    describe 'POST #refunds_create' do
      it 'enqueues a RefundJob if there are refund rules with live sync for the current shop' do
        FactoryBot.create(:sync_rule, sync_type: 'refund', sync_trigger: 'create', live_sync: true, shop_id: shop.id)
        post :refunds_create, params: { id: '1234', order_id: '5678' }
        expect(RefundJob).to have_enqueued_sidekiq_job('refunds/create', '1234', '5678', shop.id)
        expect(response).to have_http_status(:ok)
      end

      it 'returns a 200 OK response if there are no refund rules with live sync for the current shop' do
        post :refunds_create, params: { id: '1234', order_id: '5678' }
        expect(RefundJob).not_to have_enqueued_sidekiq_job
        expect(response).to have_http_status(:ok)
      end
    end

    describe 'POST #tender_transactions_create' do
      it 'enqueues a TenderTransactionJob if there are tender transaction rules with live sync for the current shop' do
        FactoryBot.create(:sync_rule, sync_type: 'tender_transaction', sync_trigger: 'create', live_sync: true,
                                      shop_id: shop.id)
        post :tender_transactions_create, params: tender_transaction_params
        expect(TenderTransactionJob).to have_enqueued_sidekiq_job(tender_transaction_params.to_json, shop.id)
        expect(response).to have_http_status(:ok)
      end

      it 'returns a 200 OK response if there are no tender transaction rules with live sync for the current shop' do
        post :tender_transactions_create, params: tender_transaction_params
        expect(TenderTransactionJob).not_to have_enqueued_sidekiq_job
        expect(response).to have_http_status(:ok)
      end
    end

    private

    def tender_transaction_params
      {
        id: '1234',
        order_id: '5678',
        amount: '99.99',
        processed_at: Time.current.iso8601,
        remote_reference: 'abcd1234',
        payment_method: 'credit_card'
      }
    end
  end
  # rubocop:enable Lint/UnreachableCode
end
