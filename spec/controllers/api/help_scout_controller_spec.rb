# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::HelpScoutController, type: :controller do
  let(:shop) { create(:shop) }
  let(:api_url) { 'https://helpscout.api.test' }

  before do
    stub_shopify_authenticated(shop)
    allow(ENV).to receive(:fetch).with('HELPSCOUT_API_URL').and_return(api_url)
    allow(ENV).to receive(:fetch).with('HELPSCOUT_API_KEY').and_return('api_key')
    allow(ENV).to receive(:fetch).with('HELP_ARTICLES_CACHE_TIME', anything).and_return(240)
  end

  describe '#default_articles' do
    let(:category_id) { Faker::Alphanumeric.alphanumeric(number: 10) }

    before do
      allow(ENV).to receive(:fetch).with('HELPSCOUT_TOP10_CATEGORY_ID').and_return(category_id)
      stub_request(:get, "#{api_url}/categories/#{category_id}/articles")
        .to_return(status:, body: request_body.to_json, headers: {})
    end

    subject { get :default_articles, params: { id: category_id } }

    context 'when the request is successful' do
      let(:status) { 200 }
      let(:request_body) do
        { 'articles' => { 'items' => Array.new(3) { |i| { 'id' => i, 'name' => "Test Article #{i}" } } } }
      end
      let(:response_body) do
        { 'articles' => Array.new(3) { |i| { 'id' => i, 'title' => "Test Article #{i}" } } }
      end

      it 'returns a list of articles' do
        subject
        expect(response.parsed_body).to eq(response_body)
      end
    end

    context 'when the request is unsuccessful' do
      let(:status) { 404 }
      let(:request_body) { {} }

      it 'returns a not found status' do
        subject
        expect(response.status).to eq 404
      end
    end
  end

  describe '#search_articles' do
    let(:query) { Faker::Alphanumeric.alphanumeric(number: 10) }

    before do
      allow(ENV).to receive(:fetch).with('HELPSCOUT_COLLECTION_ID').and_return('collection_id')
      params = '?collectionId=collection_id&status=published&visibility=public'
      stub_request(:get, "#{api_url}/search/articles#{params}&query=#{query}")
        .to_return(status:, body: request_body.to_json, headers: {})
    end

    subject { get :search_articles, params: { query: } }

    context 'when the request is successful' do
      let(:status) { 200 }
      let(:request_body) do
        { 'articles' => { 'items' => Array.new(11) { |i| { 'id' => i, 'name' => "Test Article #{i}" } } } }
      end
      let(:response_body) do
        { 'articles' => Array.new(10) { |i| { 'id' => i, 'title' => "Test Article #{i}" } } }
      end

      it 'returns a list of first 10 articles' do
        subject
        expect(response.parsed_body).to eq(response_body)
      end
    end

    context 'when no articles found' do
      let(:status) { 200 }
      let(:request_body) { { 'articles' => { 'items' => [] } } }

      it 'returns a no content status' do
        subject
        expect(response.status).to eq 204
      end
    end

    context 'when the request is unsuccessful' do
      let(:status) { 404 }
      let(:request_body) { {} }

      it 'returns a not found status' do
        subject
        expect(response.status).to eq 404
      end
    end
  end

  describe '#fetch_article' do
    let(:article_id) { Faker::Alphanumeric.alphanumeric(number: 10) }

    before do
      stub_request(:get, "#{api_url}/articles/#{article_id}")
        .to_return(status:, body: request_body.to_json, headers: {})
    end

    subject { get :fetch_article, params: { id: article_id } }

    context 'when the request is successful' do
      let(:status) { 200 }
      let(:request_body) do
        { 'article' => { 'id' => 1, 'name' => 'Test Article', 'text' => 'Hello world!' } }
      end
      let(:response_body) do
        { 'article' => { 'title' => 'Test Article', 'body' => 'Hello world!' } }
      end

      it 'returns the articles' do
        subject
        expect(response.parsed_body['article']).to include('title' => 'Test Article')
        expect(response.parsed_body['article']).to have_key('rawBody')
      end
    end

    context 'when the request is unsuccessful' do
      let(:status) { 404 }
      let(:request_body) { {} }

      it 'returns a not found status' do
        subject
        expect(response.status).to eq 404
      end
    end
  end
end
