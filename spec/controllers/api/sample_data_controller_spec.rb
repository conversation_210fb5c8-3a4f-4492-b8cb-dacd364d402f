# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::SampleDataController, type: :controller do
  let(:shop) { create(:shop) }
  let(:cache_key) { "sample_order_#{shop.id}" }

  before do
    stub_shopify_authenticated(shop)
    allow(controller).to receive(:find_or_create_shop_from_session).and_return(shop)
    allow(controller).to receive(:current_shop).and_return(shop)
    allow(shop).to receive(:with_shopify_session).and_yield
    allow(shop).to receive(:id).and_return(shop.id)
    allow(Rails.cache).to receive(:write)
    controller.instance_variable_set(:@current_shop, shop)
  end

  describe "GET #sample_order" do
    subject { get :sample_order }

    context "when an order is found" do
      let(:order) { build(:order, :with_transactions) }

      before do
        allow(ShopifyAPI::Order).to receive(:all).with(limit: 1).and_return([order])
        allow(order).to receive(:as_json).and_return({ foo: "bar" })
        allow(order).to receive(:respond_to?).and_call_original
        allow(order).to receive(:respond_to?).with(:transactions, any_args).and_return(true)
        allow(order).to receive(:transactions).and_return(order.transactions)
        allow(JSON).to receive(:parse).and_call_original
      end

      it "returns the parsed order, success true, and writes to cache" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.parsed_body["order"]).to eq({ "foo" => "bar" })
        expect(response.parsed_body["success"]).to eq(true)
        expect(Rails.cache).to have_received(:write).with(a_string_starting_with("sample_order_"), order.to_json,
          expires_in: 10.minutes)
      end
    end

    context "when no order is found" do
      before do
        allow(ShopifyAPI::Order).to receive(:all).with(limit: 1).and_return([])
      end

      it "returns the fallback structure, success true, and writes to cache" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.parsed_body["order"]).to include("id", "name", "email", "line_items")
        expect(response.parsed_body["success"]).to eq(true)
        expect(Rails.cache).to have_received(:write).with(a_string_starting_with("sample_order_"), kind_of(String),
          expires_in: 10.minutes)
      end
    end

    context "when ShopifyAPI::Order.all raises an exception" do
      let(:error_message) { "API error" }

      before do
        allow(ShopifyAPI::Order).to receive(:all).with(limit: 1).and_raise(StandardError.new(error_message))
        allow(Rails.logger).to receive(:error)
      end

      it "returns the fallback structure, success false, error message, and writes to cache" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.parsed_body["order"]).to include("id", "name", "email", "line_items")
        expect(response.parsed_body["success"]).to eq(false)
        expect(response.parsed_body["error"]).to eq(error_message)
        expect(Rails.cache).to have_received(:write).with(a_string_starting_with("sample_order_"), kind_of(String),
          expires_in: 10.minutes)
        expect(Rails.logger).to have_received(:error).with(/Error fetching sample order: #{error_message}/)
      end
    end
  end
end
