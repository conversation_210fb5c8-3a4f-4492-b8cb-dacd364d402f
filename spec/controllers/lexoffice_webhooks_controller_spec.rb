# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LexofficeWebhooksController, type: :controller do
  let(:shop) { create(:shop) }
  let(:invoice_endpoint) { instance_double('Lexoffice::Invoice') }
  let(:cn_endpoint) { instance_double('Lexoffice::CreditNote') }
  let(:sync_info) { create(:sync_info, target_id: 'resourceId') }

  before do
    allow(Shop).to receive(:find_by).and_return(shop)
    allow(shop).to receive(:refresh_token_if_expired)
    allow(Lexoffice::Invoice).to receive(:new).and_return(invoice_endpoint)
    allow(Lexoffice::CreditNote).to receive(:new).and_return(cn_endpoint)
    allow(invoice_endpoint).to receive(:find)
    allow(cn_endpoint).to receive(:find)
    allow(SyncInfo).to receive(:find_by).and_return(sync_info)
    allow(sync_info).to receive(:add_doc_properties)
    allow_any_instance_of(Lexoffice::WebhookVerification).to receive(:verify_request).and_return(true)
    allow(DocStatusJob).to receive(:perform_async)
  end

  describe '#lexoffice_token_revoked' do
    context 'the shop exists' do
      it 'resets lexoffice connection if event date is later than shop connection established at + 2 minutes' do
        allow(shop).to receive(:reset_lexoffice_connection)
        params = {
          'organizationId' => 'orgId',
          'eventDate' => (shop.connection_established_at + 3.minutes).iso8601,
          'eventType' => 'credit-note.deleted'
        }
        post(:lexoffice_token_revoked, params:)
        expect(shop).to have_received(:reset_lexoffice_connection)
      end
    end
    context 'the shop does not exist' do
      it 'returns no content' do
        allow(Shop).to receive(:find_by).and_return(nil)
        params = {
          'organizationId' => 'orgId',
          'eventDate' => (shop.connection_established_at + 3.minutes).iso8601,
          'eventType' => 'credit-note.deleted'
        }
        expect do
          post(:lexoffice_token_revoked, params:)
        end.not_to raise_error
        expect(response).to have_http_status(:no_content)
      end
    end
  end

  describe '#invoice_status_changed' do
    it 'finds an invoice and adds its properties to sync_info if sync_info is present' do
      post :invoice_status_changed, params: { 'organizationId' => 'orgId', 'resourceId' => 'resourceId',
                                              'eventType' => 'invoice.updated' }
      # expect a DocStatusJob to be enqueued with params invoice and resourceId
      expect(DocStatusJob).to have_received(:perform_async).with('Invoice', 'resourceId')
    end
  end

  describe '#credit_note_status_changed' do
    it 'finds a credit note and adds its properties to sync_info if sync_info is present' do
      post :credit_note_status_changed, params: { 'organizationId' => 'orgId', 'resourceId' => 'resourceId',
                                                  'eventType' => 'credit-note.deleted' }
      expect(DocStatusJob).to have_received(:perform_async).with('CreditNote', 'resourceId')
    end
  end
end
