# frozen_string_literal: true

require 'rails_helper'

RSpec.describe OmniauthCallbacksController, type: :controller do
  let(:shop) { create(:shop) }
  let(:auth_hash) do
    {
      credentials: {
        token: 'token',
        refresh_token: 'refresh_token',
        expires_at: 1.hour.from_now
      }
    }
  end
  let(:profile_endpoint) { instance_double('Lexoffice::Profile') }
  let(:profile_info) do
    {
      'organizationId' => 'orgId',
      'taxType' => 'taxType',
      'smallBusiness' => true,
      'connectionId' => 'connId'
    }
  end

  before do
    stub_shopify_authenticated(shop)
    session[:host] = Base64.strict_encode64('test-host')
    session[:shop] = shop.shopify_domain
    allow(Shop).to receive(:find_by).and_return(shop)
    allow(shop).to receive(:update)
    allow(shop).to receive(:save)
    allow(LexofficeWebhooksJob).to receive(:perform_async)
    allow(Lexoffice::Profile).to receive(:new).and_return(profile_endpoint)
    allow(profile_endpoint).to receive(:get_info).and_return(profile_info)
    allow(Rails.logger).to receive(:debug)
    request.env['omniauth.params'] = { shop: nil }
  end

  describe '#lexoffice' do
    it 'updates shop, queues a LexofficeWebhooksJob, and redirects to admin if auth_hash is present' do
      request.env['omniauth.auth'] = auth_hash
      get :lexoffice
      expect(LexofficeWebhooksJob).to have_received(:perform_async).with(shop.id)
      expect(shop).to have_received(:update).twice
      expect(response).to redirect_to("https://test-host/apps/#{ShopifyApp.configuration.api_key}")
    end

    it 'redirects to admin if auth_hash is blank' do
      request.env['omniauth.auth'] = nil
      get :lexoffice
      expect(response).to redirect_to("https://test-host/apps/#{ShopifyApp.configuration.api_key}")
    end

    it 'handles RestClient::Conflict' do
      conflict_error = RestClient::Conflict.new
      allow(profile_endpoint).to receive(:get_info).and_raise(conflict_error)
      request.env['omniauth.auth'] = auth_hash
      get :lexoffice
      expect(Rails.logger).to have_received(:debug).with(conflict_error.message)
      expect(response).to redirect_to("https://test-host/apps/#{ShopifyApp.configuration.api_key}")
    end
  end
end
