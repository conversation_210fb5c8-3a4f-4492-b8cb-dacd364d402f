# lexoffice-sevdesk-shared-components

## Initial setup

```bash
$ git submodule update --init --recursive
```

## Troubleshooting
Wenn es nicht möglich ist, das Submodule per https <PERSON> zu klonen, kann das Submodule auch per SSH geklont werden. 
Dazu muss der SSH Key in Github hinterlegt werden und das submodule Setup zurückgesetzt werden.

```bash
$ cd path/to/project

$ git rm --cached frontend/shared
$ git config -f .gitmodules --remove-section submodule.frontend/shared

$ rm -rf frontend/shared
$ rm -rf .git/modules/frontend/shared

$ git <NAME_EMAIL>:eshopguide/lexoffice-sevdesk-shared-components.git frontend/shared
```