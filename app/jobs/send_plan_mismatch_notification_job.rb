# frozen_string_literal: true

class SendPlanMismatchNotificationJob
  include Sidekiq::Worker
  include JobErrorReporting

  def perform(shop_id)
    shop = Shop.find(shop_id)
    return unless shop.plan_mismatch_since?

    shop_infos = {
      shop_owner: shop.shop_owner,
      shopify_domain: shop.shopify_domain,
      email: shop.email
    }
    target_date = shop.plan_mismatch_since + 14.days

    return unless Rails.env.production?

    set_error_context(user_id: shop.shopify_domain)
    NotificationsJob.perform_async(
      shop_infos.to_json,
      'plan_mismatch',
      'email',
      target_date.to_s
    )
  end
end
