# frozen_string_literal: true

require 'ostruct'

class ImportTenderTransactionJob < TenderTransactionJob
  attr_writer :retry_count

  sidekiq_options queue: 'import'

  def retry_count
    @retry_count || 0
  end

  def perform(transaction_attr, shop_id, import_job_id, locale = 'de')
    @transfer_success = false
    super(transaction_attr, shop_id, true, locale)
  rescue StandardError
    @error = true
  ensure
    if retry_count.zero?
      job_status = JobStatus.find_or_create_by_safe!(guid: import_job_id)
      if @error || !@transfer_success
        job_status.increase_progress
      else
        job_status.increase_progress(:transaction)
      end
    end
  end
end
