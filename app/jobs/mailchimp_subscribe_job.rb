# frozen_string_literal: true

require 'open-uri'

# This job is used to subscribe a shop to the Mailchimp list
class MailchimpSubscribeJob
  include Sidekiq::Worker
  include JobErrorReporting
  sidekiq_options retry: 0, queue: 'misc'

  def perform(shop_id)
    shop = Shop.find(shop_id)
    return if shop.blank?

    set_error_context(user_id: shop.shopify_domain)
    EshopGuide::MailchimpListSubscriberService.call(shop)
  rescue StandardError => e
    Rails.error.report(e)
  end
end
