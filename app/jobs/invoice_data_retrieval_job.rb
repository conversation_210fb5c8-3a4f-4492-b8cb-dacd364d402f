# frozen_string_literal: true

class InvoiceDataRetrievalJob
  include Sidekiq::Worker
  include JobErrorReporting

  sidekiq_options queue: 'infosync'

  def perform(shop_id)
    @shop = Shop.find(shop_id)
    return unless valid_shop?

    set_error_context(user_id: @shop.shopify_domain)
    initialize_invoice_objects
    process_sync_info_batches
    log_invoice_updates
  end

  private

  def valid_shop?
    @shop&.connected_to_lexoffice? && @shop&.plan_active?
  end

  def initialize_invoice_objects
    @shop.refresh_token_if_expired
    @invoice = Lexoffice::Invoice.new(@shop.lexoffice_token)
    @credit_note = Lexoffice::CreditNote.new(@shop.lexoffice_token)
    @transaction = Lexoffice::FinancialTransaction.new(@shop.lexoffice_token)

    @invoice_count = 0
    @credit_note_count = 0
    @transaction_count = 0
  end

  def process_sync_info_batches
    SyncInfo.where(shop_id: @shop.id).find_in_batches(batch_size: 500) do |batch|
      process_sync_info_batch(batch)
    end
  end

  def process_sync_info_batch(batch)
    batch.each do |sync_info|
      process_sync_info(sync_info) if sync_info.doc_created?
    rescue StandardError => e
      Rails.error.report(e)
      next
    end
  end

  def process_sync_info(sync_info)
    # TODO: Implement request throttling
    data = fetch_invoice_data(sync_info)
    sync_info.add_doc_properties(data)
  end

  def fetch_invoice_data(sync_info)
    if sync_info.invoice?
      @invoice_count += 1
      @invoice.find(sync_info.target_id)
      @invoice
    elsif sync_info.refund?
      @credit_note_count += 1
      @credit_note.find(sync_info.target_id)
      @credit_note
    elsif sync_info.transaction?
      @transaction_count += 1
      @transaction.find(sync_info.target_id)
      @transaction
    end
  end

  def log_invoice_updates
    Rails.logger.info "#{@shop.name}: UPDATED #{@invoice_count} INVOICES,
    #{@credit_note_count} CREDIT NOTES, #{@transaction_count} TRANSACTIONS"
  end
end
