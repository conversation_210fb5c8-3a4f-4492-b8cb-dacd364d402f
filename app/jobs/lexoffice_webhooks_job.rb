# frozen_string_literal: true

# This job registers the Lexoffice Webhooks for any given shop
class LexofficeWebhooksJob
  include Sidekiq::Job
  include JobErrorReporting

  sidekiq_options queue: 'infosync'

  HOOKS = %i[
    register_token_revoked_hook
    register_invoice_status_changed_hook
    register_credit_note_status_changed_hook
  ].freeze

  def perform(shop_id)
    shop = Shop.find(shop_id)
    return if shop.blank?

    set_error_context(user_id: shop.shopify_domain)
    shop.refresh_token_if_expired
    event_subscription_endpoint = Lexoffice::EventSubscription.new(shop.lexoffice_token)

    HOOKS.each do |hook|
      register_hook(event_subscription_endpoint, hook)
    end
  end

  private

  def register_hook(event_subscription_endpoint, hook)
    event_subscription_endpoint.send(hook)
  rescue RestClient::Conflict => e
    Rails.logger.debug { e.message }
  end
end
