# frozen_string_literal: true

class FinancialAccountJob < FinancialBaseJob
  include Sidekiq::Worker
  sidekiq_options retry: 1, queue: "doctasks"

  def perform(shop_id)
    shop = Shop.includes(:transaction_setting).find(shop_id)
    # return if not connected to lexoffice
    return unless shop.lexoffice_token?

    set_error_context(user_id: shop.shopify_domain, tags: "lexoffice_financial_account_job")
    FindOrEnsureFinancialAccountService.call(shop.transaction_setting, shop.lexoffice_token,
      account_types(shop.transaction_setting))
  rescue StandardError => e
    # re-raise lock errors, so the job can be requeued without incrementing the retry count
    raise e if e.class.to_s.include?("Redlock::LockError")

    Rails.error.report(e)
  end

  private

  def account_types(transaction_setting)
    # basic and extra accounts, paypal is already filtered out
    (ALL_TYPES.select { |type| transaction_setting.send("enable_#{type}") } +
      transaction_setting.extra_accounts_info.keys.each_with_object([]) do |k, arr|
        if transaction_setting.extra_accounts_info[k] && k.include?("enable_") && k.exclude?("account_id")
          arr.push(k.split("enable_").second.tr(" ",
            "_").downcase)
        end
      end).compact
  end
end
