# frozen_string_literal: true

class ShopUpdateJob < ApplicationJob
  extend ShopifyAPI::Webhooks::Handler

  class << self
    def handle(topic:, shop:, body:)
      perform_later(topic:, shop_domain: shop, webhook: body)
    end
  end

  # rubocop:disable Lint/UnusedMethodArgument
  def perform(topic:, shop_domain:, webhook:)
    shop = Shop.find_by!(shopify_domain: shop_domain)
    set_error_context(user_id: shop_domain, params: { webhook: })

    BillingServices::CheckPlanMismatchService.call(shop:)
  end
  # rubocop:enable Lint/UnusedMethodArgument
end
