# frozen_string_literal: true

class AppSubscriptionsUpdateJob < ApplicationJob
  extend ShopifyAPI::Webhooks::WebhookHandler

  self.queue_adapter = :sidekiq

  class << self
    def handle(data:)
      # TODO: This Webhook will be removed when we switch to managed webhooks
      # Ignore for now
    end
  end

  def perform(data:)
    # TODO: This Webhook will be removed when we switch to managed webhooks
    # Ignore for now
  end
end
