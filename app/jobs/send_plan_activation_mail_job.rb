# frozen_string_literal: true

class SendPlanActivationMailJob
  include Sidekiq::Worker
  include JobErrorReporting

  def perform(shop_id)
    shop = Shop.find(shop_id)
    return if shop.plan_active?

    shop_infos = {
      shop_owner: shop.shop_owner,
      shopify_domain: shop.shopify_domain,
      email: shop.email
    }

    set_error_context(user_id: shop.shopify_domain)
    NotificationsJob.perform_async(
      shop_infos.to_json,
      'plan_activation',
      'email',
      "#{shop.admin_url}/PlansAndCoupons"
    )
  end
end
