# frozen_string_literal: true

require "open-uri"

# This job is used to unsubscribe a shop from the Mailchimp list
# it takes the shop as a JSON string, because the shop might already be uninstalled when this runs
class MailchimpUnsubscribeJob
  include Sidekiq::Worker
  include JobErrorReporting
  sidekiq_options retry: 0, queue: "misc"

  def perform(shop_json)
    shop = JSON.parse(shop_json)

    set_error_context(user_id: shop["shopify_domain"])
    EshopGuide::MailchimpListUnsubscriberService.call(shop)
  rescue StandardError => e
    Rails.error.report(e)
  end
end
