# frozen_string_literal: true

# is not used at the moment, since the uninstall is handled in the webhooks controller
# but the class is needed so that ShopifyApp::WebhooksManager.add_registrations does
# not raise an error
class UninstallJob < ApplicationJob
  extend ShopifyAPI::Webhooks::Handler

  class << self
    def handle(topic:, shop:, body:)
      perform_later(topic:, shop_domain: shop, webhook: body)
    end
  end

  def perform(topic:, shop_domain:, webhook:)
    shop = Shop.find_by(shopify_domain: shop_domain)
    set_error_context(user_id: shop_domain, params: { webhook: })

    if shop.nil?
      logger.error("#{self.class} failed: cannot find shop with domain '#{shop_domain}'")

      raise ActiveRecord::RecordNotFound, 'Shop Not Found'
    end

    @webhook = webhook
    @topic = topic
  end
end
