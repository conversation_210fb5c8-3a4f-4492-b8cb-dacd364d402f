# frozen_string_literal: true

require 'open-uri'

class ImportRefundJob < RefundJob
  sidekiq_options queue: 'import'

  def retry_count
    @retry_count || 0
  end

  def perform(action, refund_id, order_id, shop_id, import_job_id, locale = 'de') # rubocop:disable Metrics/ParameterLists, Style/OptionalBooleanParameter
    super(action, refund_id, order_id, shop_id, true, locale)
  rescue StandardError
    @error = true
  ensure
    if retry_count.zero?
      job_status = JobStatus.find_or_create_by_safe!(guid: import_job_id)
      if @error
        job_status.increase_progress
        # Code to handle the fact that an exception was raised
      else
        # Code to handle the case where no exception was raised
        job_status.increase_progress(:refund)
      end
    end
  end
end
