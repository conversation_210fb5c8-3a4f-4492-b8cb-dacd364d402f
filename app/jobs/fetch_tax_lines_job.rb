# frozen_string_literal: true

class FetchTaxLinesJob
  include Sidekiq::Worker
  include JobErrorReporting

  sidekiq_options retry: 3

  def perform(shop_id)
    shop = Shop.find(shop_id)
    set_error_context(user_id: shop.shopify_domain)

    shop.has_multiple_tax_lines = shop.with_shopify_session do
      BulkQueryShopHasMultipleTaxLinesService.call
    end

    shop.save!
  rescue ShopifyGraphql::ResourceNotFound, ShopifyGraphql::UnauthorizedAccess, ShopifyGraphql::PaymentRequired,
         ArgumentError
    # Ignore these errors, probably inactive shops or shops with no orders
  end
end
