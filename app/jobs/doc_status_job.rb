# frozen_string_literal: true

class DocStatusJob
  include Sidekiq::Job
  include JobErrorReporting
  VALID_DOC_TYPES = { CreditNote: "Refund", Invoice: "Invoice" }.freeze
  sidekiq_options retry: 10, queue: "infosync"

  def perform(doc_type, doc_id)
    process_status_change(doc_type, doc_id)
  end

  def process_status_change(doc_type, doc_id)
    sync_info = find_sync_info(doc_type, doc_id)
    return if sync_info.nil?

    # Get document info from lexoffice
    shop = sync_info.shop
    return if shop.nil? || shop.lexoffice_token.nil?

    set_error_context(
      user_id: shop.shopify_domain,
      params: { doc_type:, doc_id: },
      tags: "lexoffice_webhook",
      optional: { sync_info_id: sync_info.id }
    )

    # Refresh token if expired
    shop.refresh_token_if_expired
    doc_endpoint = "Lexoffice::#{doc_type}".constantize.new(shop.reload.lexoffice_token)
    # The find method fills the doc_endpoint with the document info
    doc_endpoint.find(doc_id)

    # Then update sync_info
    sync_info.add_doc_properties(doc_endpoint)
  rescue RestClient::NotFound, RestClient::Unauthorized => e
    Rails.error.report(e)
  end

  private

  def find_sync_info(doc_type, doc_id)
    target_type = VALID_DOC_TYPES[doc_type.to_sym]
    SyncInfo.find_by(target_id: doc_id, target_type:)
  end
end
