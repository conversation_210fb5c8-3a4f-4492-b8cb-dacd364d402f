# frozen_string_literal: true

require "open-uri"

class NotificationsJob
  include StatusUpdate
  include Sidekiq::Worker
  include JobErrorReporting
  sidekiq_options retry: 0, queue: "misc"

  def perform(shop, service_instruction, service_name, instructions = nil)
    shop = JSON.parse(shop)

    set_error_context(
      user_id: shop["shopify_domain"],
      params: { service_instruction:, service_name:, instructions: }
    )

    case service_name
    when "email"
      EshopGuide::MailchimpDeliveryService.call(shop, service_instruction.to_sym, instructions)
    when "notification"
      EshopGuide::SlackNotifierService.call(shop, service_instruction.to_sym)
    end
  rescue StandardError => e
    Rails.error.report(e)
  end
end
