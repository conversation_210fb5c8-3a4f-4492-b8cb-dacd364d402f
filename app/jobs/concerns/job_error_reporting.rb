# frozen_string_literal: true

module JobErrorReporting
  extend ActiveSupport::Concern

  def set_error_context(user_id:, params: nil, tags: nil, optional: {})
    context = {
      user_id:,
      entry_point: self.class.to_s,
      jid: find_job_id(self),
      params:,
      tags:
    }.merge(optional)
    context.compact!

    Rails.error.set_context(**context)
  end

  private

  def find_job_id(job)
    if job.respond_to?(:jid)
      job.jid
    elsif job.respond_to?(:job_id)
      job.job_id
    end
  end
end
