module CreditNoteDataBuilder
  class FindOrCreateSyncInfo
    include <PERSON><PERSON>

    def call
      context.sync_info = SyncInfo.find_or_create_by(shopify_id: context.refund.id, shop: context.shop, target_type: "Refund") do |info|
        info.last_action = 'Job started'
        info.import = context.is_import_job
        info.shopify_order_id = context.order.id
        info.shopify_order_name = context.order.name
        info.shopify_created_at = context.refund.processed_at
      end
    end
  end
end
