# frozen_string_literal: true

module CreditNoteDataBuilder
  class SetDiscount
    include Interactor

    def call
      if context.order.total_discounts.to_d.zero? ||
         context.order.total_discounts.to_d == context.order.current_total_discounts.to_d ||
         context.credit_note.amount_discrepancy
        return
      end

      context.credit_note.set_discount(context.refund.calculate_absolute_discount)
    end
  end
end
