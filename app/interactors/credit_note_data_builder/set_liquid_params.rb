# frozen_string_literal: true

module CreditNoteDataBuilder
  class SetLiquidParams
    include <PERSON>actor

    def call
      add_transactions_to_order
      context.credit_note.set_title title if context.shop_settings.refund_title.present?
      context.credit_note.set_introduction introduction if context.shop_settings.refund_pretext.present?
      context.credit_note.set_remark remark if context.shop_settings.refund_posttext.present?
    end

    private

    def title
      EshopGuide::DynamicTextService.call(context.shop_settings.refund_title, context.order_and_refund)
    end

    def introduction
      EshopGuide::DynamicTextService.call(context.shop_settings.refund_pretext, context.order_and_refund)
    end

    def remark
      EshopGuide::DynamicTextService.call(context.shop_settings.refund_posttext, context.order_and_refund)
    end

    def add_transactions_to_order
      transactions_needed = [context.shop_settings.refund_title, context.shop_settings.refund_pretext,
                             context.shop_settings.refund_posttext].any? do |text|
        text.include?('order_transactions')
      end.present?
      return unless transactions_needed

      context.order_and_refund = context.order.extend_order_with_transactions
                                        .to_hash.merge({ refund: context.refund.to_hash })
    end
  end
end
