# frozen_string_literal: true

module CreditNoteDataBuilder
  class AddLineItems
    include Interactor

    def call
      context.credit_note.add_line_items(context.refund.refund_line_items, context.refund)
      context.credit_note.handle_shipping_refund(context.order, context.refund)
      context.credit_note.handle_amount_discrepancy(context.refund, context.use_brutto, context.order)
    end
  end
end
