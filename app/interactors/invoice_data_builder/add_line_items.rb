# frozen_string_literal: true

module InvoiceDataBuilder
  class AddLineItems
    include Interactor

    def call
      return if context.order.line_items.blank?

      FulfillmentCriteriaService.new(context.order, context.shop).call if context.shop_settings.invoice_timing_fulfill?

      if context.shop_settings.invoice_timing_fulfill? && context.order.use_fulfillment?
        context.invoice.add_line_items(context.order.fulfillments.flat_map(&:line_items),
                                       context.shop_settings.use_SKUs,
                                       context.order)
      else
        context.invoice.add_line_items(context.order.line_items,
                                       context.shop_settings.use_SKUs,
                                       context.order)
      end

      add_shipping_lines if context.order.shipping_lines.present?
    end

    private

    # Set shipping tax rate if calculate_shipping_tax setting is enabled
    def add_shipping_lines
      shipping_tax_rate = if context.shop_settings.calculate_shipping_tax? && !context.order.tax_exempt?
                            context.invoice.calc_shipping_tax_rate(context.order.line_items,
                                                                   context.order.shipping_lines)
                          end
      context.invoice.add_line_items(context.order.shipping_lines,
                                     context.shop_settings.use_SKUs,
                                     context.order,
                                     shipping_tax_rate,
                                     shipping_lines: true)
    end
  end
end
