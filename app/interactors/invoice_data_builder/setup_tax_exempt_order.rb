# frozen_string_literal: true

# Removes the taxRates on line items and shipping lines when a order is tax_exempt. Shopify removes the price of the tax
# but not the taxRate. This causes a problem with creating invoices in Lexoffice because it expects a 0.0 taxRate.
module InvoiceDataBuilder
  class SetupTaxExemptOrder
    include Interactor

    def call
      if !context.order.pos_order? && context.order.third_party_country? && !context.order.tax_exempt?
        context.invoice.set_tax_exempt_rate(context.order.shipping_lines, context.order.line_items)
      end

      if context.shop.vat_free? && context.order.zero_sum_taxes?
        context.invoice.set_tax_exempt_rate(context.order.shipping_lines, context.order.line_items)
      end

      return unless context.order.tax_exempt?

      context.invoice.set_tax_exempt_rate(context.order.shipping_lines, context.order.line_items)
    end
  end
end
