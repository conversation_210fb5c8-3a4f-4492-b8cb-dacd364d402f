module InvoiceDataBuilder
  class FindOrCreateSyncInfo
    include <PERSON><PERSON>

    def call
      context.sync_info = SyncInfo.find_or_create_by(shopify_id: context.order.id, shop: context.shop,
                                                     target_type: context.rule.target_type) do |info|
        info.last_action = 'Job started'
        info.import = context.is_import_job
        info.shopify_order_id = context.order.id
        info.shopify_order_name = context.order.name
        info.shopify_created_at = context.order.processed_at
        info.sync_rule_id = context.rule.id
      end
    end
  end
end
