module InvoiceDataBuilder
  class SetLiquidParams
    include <PERSON><PERSON>

    def call
      add_transactions_to_order
      if context.shop_settings.invoice_title.present?
        context.invoice.set_title EshopGuide::DynamicTextService.call(context.shop_settings.invoice_title,
                                                                      context.order)
      end
      if context.shop_settings.invoice_pretext.present?
        context.invoice.set_introduction EshopGuide::DynamicTextService.call(context.shop_settings.invoice_pretext,
                                                                             context.order)
      end
      return if context.shop_settings.invoice_posttext.blank?

      context.invoice.set_remark EshopGuide::DynamicTextService.call(context.shop_settings.invoice_posttext,
                                                                     context.order)
    end

    private

    def add_transactions_to_order
      transactions_needed = [context.shop_settings.invoice_title, context.shop_settings.invoice_pretext,
                             context.shop_settings.invoice_posttext].any? do |text|
        text.include?('order_transactions')
      end.present?
      context.order = context.order.extend_order_with_transactions if transactions_needed
    end
  end
end
