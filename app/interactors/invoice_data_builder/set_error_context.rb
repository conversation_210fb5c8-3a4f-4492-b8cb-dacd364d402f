# frozen_string_literal: true

module InvoiceDataBuilder
  class SetErrorContext
    include Interactor

    def call
      app_home = ENV.fetch("APP_HOME", nil)
      shop = context.shop.shopify_domain
      order_id = context.order.id

      Rails.error.set_context(
        order_id: context.order.id,
        order_name: context.order.name,
        settings: context.shop_settings.attributes,
        order_data_url: "#{app_home}/support/view_order?shop=#{shop}&order_id=#{order_id}"
      )
    end
  end
end
