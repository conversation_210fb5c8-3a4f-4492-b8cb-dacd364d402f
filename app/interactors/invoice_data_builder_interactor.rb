# frozen_string_literal: true

class InvoiceDataBuilderInteractor
  include Interactor::Organizer

  organize [
    InvoiceDataBuilder::PrepareContext,
    InvoiceDataBuilder::SetErrorContext,
    InvoiceDataBuilder::LoadDataFromOrder,
    InvoiceDataBuilder::FindOrCreateSyncInfo,
    InvoiceDataBuilder::<PERSON>reateCustomer,
    InvoiceDataBuilder::SetSubTaxType,
    InvoiceDataBuilder::SetTaxType,
    InvoiceDataBuilder::SetLanguage,
    InvoiceDataBuilder::SetLiquidParams,
    InvoiceDataBuilder::SetupTaxExemptOrder,
    InvoiceDataBuilder::SetLayout,
    InvoiceDataBuilder::AddLineItems,
    InvoiceDataBuilder::SetDiscount
  ]
end
