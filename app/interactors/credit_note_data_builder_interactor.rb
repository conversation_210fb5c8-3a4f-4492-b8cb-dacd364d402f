# frozen_string_literal: true

class CreditNoteDataBuilderInteractor
  include Interactor::Organizer

  organize [
    CreditNoteDataBuilder::PrepareContext,
    CreditNoteDataBuilder::SetErrorContext,
    CreditNoteDataBuilder::FindOrCreateSyncInfo,
    CreditNoteDataBuilder::LoadDataFromRefundAndOrder,
    CreditNoteDataBuilder::CreateShopCustomer,
    CreditNoteDataBuilder::SetSubTaxType,
    CreditNoteDataBuilder::SetLanguage,
    CreditNoteDataBuilder::SetTaxType,
    CreditNoteDataBuilder::SetLayout,
    CreditNoteDataBuilder::AddLineItems,
    CreditNoteDataBuilder::SetDiscount,
    CreditNoteDataBuilder::SetLiquidParams
  ]
end
