module LexofficeCallbackHandler
  class SetProfileInfo
    include Interactor

    def call
      context.shop.lexoffice_organization_id = context.profile_info["organizationId"]
      context.shop.lexoffice_tax_type = context.profile_info["taxType"]
      context.shop.lexoffice_small_business = context.profile_info["smallBusiness"]
      context.shop.lexoffice_connection_id = context.profile_info["connectionId"]
      context.shop.save
    end
  end
end
