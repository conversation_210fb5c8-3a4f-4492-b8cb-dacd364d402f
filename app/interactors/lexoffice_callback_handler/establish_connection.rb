module LexofficeCallbackHandler
  class EstablishConnection
    include Interactor

    delegate :shop, to: :context

    def call
      shop.lexoffice_token = context.auth_hash[:credentials][:token]
      shop.lexoffice_refresh_token = context.auth_hash[:credentials][:refresh_token]
      shop.lexoffice_token_expires_at = context.auth_hash[:credentials][:expires_at]
      shop.credit_note_scope = true
      shop.send_mail_scope = true
      shop.finance_scope = true
      shop.connection_needs_auth_refresh = false
      shop.connection_established_at = Time.now
      shop.save
    end
  end
end
