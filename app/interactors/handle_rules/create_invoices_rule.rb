module HandleRules
  class CreateInvoicesRule
    include Interactor

    def call
      create_invoices? ? create_rules : destroy_rules
    end

    private

    def create_invoices?
      context.shop_setting.create_invoices
    end

    def invoice_timing_create?
      context.shop_setting.invoice_timing_create?
    end

    def invoice_timing_fulfill?
      context.shop_setting.invoice_timing_fulfill?
    end

    def create_rules
      if invoice_timing_create?
        # create orders/create rule
        SyncRule.find_or_create_by(shop: context.shop, shopify_entity_type: 'Order', target_type: 'Invoice',
                                   target_action: 'Create', live_sync: true, webhooks: 'orders/create')
        # destroy fulfill rule if invoice gets created on order creation
        rule_fulfilled = SyncRule.find_by(shop: context.shop, shopify_entity_type: 'Order', target_type: 'Invoice',
                                          webhooks: 'orders/fulfilled', live_sync: true)
        rule_fulfilled.destroy if rule_fulfilled.present?
      elsif invoice_timing_fulfill?
        # create orders/fulfilled rule
        SyncRule.find_or_create_by(shop: context.shop, shopify_entity_type: 'Order', target_type: 'Invoice',
                                   target_action: 'Create', live_sync: true, webhooks: 'orders/fulfilled')
        # destroy create rule if invoice gets created on order fulfillment
        rule_created = SyncRule.find_by(shop: context.shop, shopify_entity_type: 'Order', target_type: 'Invoice',
                                        webhooks: 'orders/create', live_sync: true)
        rule_created.destroy if rule_created.present?
      end
    end

    def destroy_rules
      rule = SyncRule.find_by(shop: context.shop, shopify_entity_type: 'Order',
                              target_type: 'Invoice', webhooks: 'orders/create', live_sync: true)
      rule_fulfilled = SyncRule.find_by(shop: context.shop, shopify_entity_type: 'Order',
                                        target_type: 'Invoice', webhooks: 'orders/fulfilled', live_sync: true)

      rule.destroy if rule.present?
      rule_fulfilled.destroy if rule_fulfilled.present?
    end
  end

end
