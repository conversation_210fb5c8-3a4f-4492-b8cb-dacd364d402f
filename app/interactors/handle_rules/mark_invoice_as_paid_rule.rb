module HandleRules
  class MarkInvoiceAsPaidRule
    include Interactor

    def call
      mark_invoice_as_paid? ? create_rule : destroy_rule
    end

    private

    def mark_invoice_as_paid?
      context.shop_setting.mark_invoice_as_paid
    end

    def create_rule
      SyncRule.find_or_create_by(shop: context.shop, shopify_entity_type: 'Order', target_type: 'Invoice', target_action: 'MarkInvoicePaid', live_sync: true) do |rule|
        rule.webhooks = 'orders/paid'
      end
    end

    def destroy_rule
      rule = SyncRule.find_by(shop: context.shop, shopify_entity_type: 'Order', target_type: 'Invoice', target_action: 'MarkInvoicePaid', live_sync: true)
      rule.destroy if rule.present?
    end
  end

end
