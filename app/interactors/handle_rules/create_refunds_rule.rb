module HandleRules
  class CreateRefundsRule
    include Interactor

    def call
      create_refunds? ? create_rule : destroy_rule
    end

    private

    def create_refunds?
      context.shop_setting.create_refunds
    end

    def create_rule
      SyncRule.find_or_create_by(shop: context.shop, shopify_entity_type: 'Refund', target_type: 'Refund', target_action: 'Create', live_sync: true) do |rule|
        rule.webhooks = 'refunds/create'
      end
    end

    def destroy_rule
      rule = SyncRule.find_by(shop: context.shop, shopify_entity_type: 'Refund', target_type: 'Refund', target_action: 'Create', live_sync: true)
      rule.destroy if rule.present?
    end
  end

end
