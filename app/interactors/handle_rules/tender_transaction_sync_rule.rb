# frozen_string_literal: true

module HandleRules
  class TenderTransactionSyncRule
    include Interactor

    def call
      create_rule
    end

    private

    def create_rule
      SyncRule.find_or_create_by(shop: context.shop, shopify_entity_type: 'TenderTransaction',
                                 target_type: 'TenderTransaction', target_action: 'Create', live_sync: true) do |rule|
        rule.webhooks = 'tender_transactions/create'
      end
    end
  end
end
