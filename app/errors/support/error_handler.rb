# frozen_string_literal: true

module Support
  module Error<PERSON><PERSON><PERSON>
    def self.included(clazz)
      clazz.class_eval do
        rescue_from ::Support::CustomError do |e|
          respond_with_error(e, e.status, e.message)
        end

        rescue_from ActiveRecord::RecordNotFound do |e|
          respond_with_error(e, 404, e.message)
        end

        rescue_from ActiveRecord::RecordInvalid do |e|
          respond_with_error(e, 422, e.message)
        end

        rescue_from ShopifyAPI::Errors::InvalidGraphqlRequestError do |e|
          respond_with_error(e, 422, e.message)
        end

        rescue_from ShopifyGraphql::ConnectionError do |e|
          respond_with_error(e, 503, "Failed to connect to Shopify API")
        end
      end
    end

    private

    def respond_with_error(error, status, message, tags: "Support")
      Rails.error.report(
        error,
        context: error_context(tags),
        handled: true
      )

      render json: {
        success: false,
        error:,
        message:
      }, status:
    end

    def error_context(tags)
      {
        tags:,
        context: {
          user_id: @current_shop&.shopify_domain,
          controller: self.class.name,
          action: action_name
        }
      }
    end
  end
end
