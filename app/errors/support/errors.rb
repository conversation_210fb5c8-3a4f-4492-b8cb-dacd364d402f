# frozen_string_literal: true

module Support
  module Errors
    class ShopNotFoundError < CustomError
      def initialize
        super(:shop_not_found, 404, "Shop not found")
      end
    end

    class ImportError < CustomError
      def initialize(message)
        super(:import_error, 422, message)
      end
    end

    class TransferError < CustomError
      def initialize(message)
        super(:transfer_error, 422, message)
      end
    end

    class PlanActivationError < CustomError
      def initialize(message)
        super(:plan_activation_error, 422, message)
      end
    end

    class DiscountError < CustomError
      def initialize(message)
        super(:discount_error, 422, message)
      end
    end

    class ShopifyGraphQLError < CustomError
      attr_reader :graphql_errors

      def initialize(message, graphql_errors = [])
        @graphql_errors = graphql_errors
        super(:shopify_graphql_error, 422, message)
      end
    end
  end
end
