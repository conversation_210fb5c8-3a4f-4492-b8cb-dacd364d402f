# frozen_string_literal: true

class GetTenderTransactions
  include ShopifyGraphql::Query

  TENDER_TRANSACTIONS_QUERY = <<~GRAPHQL
    query($query: String, $first: Int) {
      tenderTransactions(first: $first, query: $query) {
        nodes {
          id
          amount {
            amount
            currencyCode
          }
          paymentMethod
          processedAt
          remoteReference
          test
        }
      }
    }
  GRAPHQL

  # will be used when the shopify api is updated to version > 2024-07 (actual)
  def call(processed_at_min: nil, processed_at_max: nil, first: 10)
    variables = { first: }
    query_parts = []

    query_parts << "processed_at:>='#{processed_at_min}'" if processed_at_min
    query_parts << "processed_at:<='#{processed_at_max}'" if processed_at_max

    variables[:query] = query_parts.join(' AND ') unless query_parts.empty?

    response = execute(TENDER_TRANSACTIONS_QUERY, query: variables[:query], first: variables[:first])
    response.data.tender_transactions
  end
end
