# frozen_string_literal: true

class GetTenderTransaction
  include ShopifyGraphql::Query

  QUERY = <<~GRAPHQL
       query($query: String){
      tenderTransactions(first:1, query:$query) {
    	 nodes
        {
    			id
          paymentMethod
          processedAt
          remoteReference
          amount{
            amount
            currencyCode
          }
        }
    	}
    }
  GRAPHQL

  def call(query:)
    response = execute(QUERY, query:)
    response.data.tenderTransactions.nodes.first
  end
end
