# frozen_string_literal: true

class GetOrderDetails
  include ShopifyGraphql::Query

  ORDER_QUERY = <<~GRAPHQL
    query($id: ID!) {
      order(id: $id) {
        id
        name
        displayFinancialStatus
        displayFulfillmentStatus
        processedAt
        totalPriceSet {
          shopMoney {
            amount
            currencyCode
          }
        }
        transactions {
          id
          status
          kind
          gateway
          amountSet {
            shopMoney {
              amount
              currencyCode
            }
          }
          processedAt
        }
      }
    }
  GRAPHQL

  def call(id:)
    execute(ORDER_QUERY, id: format_gid(id))
  end

  private

  def format_gid(id)
    return id if id.start_with?('gid://')

    "gid://shopify/Order/#{id}"
  end
end
