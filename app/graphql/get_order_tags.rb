# frozen_string_literal: true

class GetOrderTags
  include ShopifyGraphql::Query

  QUERY = <<~GRAPHQL
    query {
      shop {
        orderTags(first: 250) {
          edges {
            node
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }
    }
  GRAPHQL

  def call
    response = execute(QUERY)
    data = response.data.shop.orderTags.edges.map(&:node)

    while response.data.shop.orderTags.pageInfo.hasNextPage
      response = execute(QUERY, cursor: response.data.shop.orderTags.pageInfo.endCursor)
      data += response.data.shop.orderTags.edges.map(&:node)
    end
    data
  end
end
