# frozen_string_literal: true

class GetOneTimePurchases
  include ShopifyGraphql::Query

  QUERY = <<~GRAPHQL
    query($key: String!) {
      appByKey(apiKey: $key) {
        installation {
          oneTimePurchases(first: 10) {
            nodes {
              name
              price {
                amount
              }
              id
              status
              createdAt
            }
          }
        }
      }
    }
  GRAPHQL

  def call
    response = execute(QUERY, key: ENV.fetch('SHOPIFY_CLIENT_API_KEY'))
    parse_data(response.data&.appByKey&.installation&.oneTimePurchases)
  end

  def parse_data(one_time_purchases)
    active_purchases = one_time_purchases.nodes.select { |purchase| purchase.status == 'ACTIVE' }
    active_purchases.map do |one_time_purchase|
      OpenStruct.new(
        id: one_time_purchase.id,
        name: one_time_purchase.name,
        created_at: one_time_purchase.createdAt,
        amount: one_time_purchase.price.amount,
        status: one_time_purchase.status
      )
    end
  end
end
