# frozen_string_literal: true

class GetActiveSubscription
  include ShopifyGraphql::Query

  QUERY = <<~GRAPHQL
    query($key: String!) {
      appByKey(apiKey: $key) {
        installation {
          activeSubscriptions {
            id
            createdAt
            name
            status
            lineItems {
              plan {
                pricingDetails {
                  ... on AppRecurringPricing {
                    __typename
                    price {
                      amount
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  GRAPHQL

  def call
    response = execute(QUERY, key: ENV.fetch('SHOPIFY_CLIENT_API_KEY'))
    parse_data(response.data&.appByKey&.installation&.activeSubscriptions&.first)
  end

  def parse_data(subscription)
    OpenStruct.new(
      id: subscription.id,
      name: subscription.name,
      created_at: subscription.createdAt,
      amount: subscription.lineItems.first.plan.pricingDetails.price.amount,
      status: subscription.status
    )
  end
end
