# frozen_string_literal: true

# Used to create helpful messages that are displayed to the user via the helpscout beacon functionality
class BeaconMessage < ApplicationRecord
  validates :name, format: { with: /\A[A-Za-z '-]*\z/ }, presence: true
  validates :help_scout_id, format: { with: /\A[0-9a-z'-]*\z/ }, presence: true
  validates :domain_name, format: { with: /\A[a-z'-]*\z/ }, presence: true
end
