# frozen_string_literal: true

# stores transmission data mainly to prevent duplicate documents
class SyncInfo < ApplicationRecord
  serialize :extra_infos, type: Hash, coder: <PERSON>AM<PERSON>
  belongs_to :shop
  belongs_to :sync_rule, optional: true

  after_create_commit :broadcast_transfer_log_create
  after_update_commit :broadcast_transfer_log_update

  scope :for_shop, ->(shop_id) { where(shop_id:) }

  def target_doc_create(document_id)
    update(target_id: document_id, last_action: 'Created')
  end

  def add_doc_properties(infos)
    if infos.instance_of?(Lexoffice::Invoice) || infos.instance_of?(Lexoffice::CreditNote)
      infos = {
        voucher_title: infos.voucher_title,
        status: infos.status,
        total_price: infos.total_price,
        document_id: infos.document_id
      }
    end
    update(extra_infos: infos)
  end

  def doc_created?
    target_id.present? && last_action != 'Job started'
  end

  def set_mail_sent
    self.invoice_mail_sent = true
    self.last_action = 'Mailed'
    save
  end

  def retry_job
    if invoice?
      SkippedRetry::RetryOrderJob.perform_async(shop.shop_setting.invoice_timing, shopify_id, shop_id)
      return
    end
    if refund?
      if shopify_order_id.present?
        SkippedRetry::RetryRefundJob.perform_async('refunds/create', shopify_id, shopify_order_id, shop_id)
      end
      return
    end
    return unless transaction?

    transaction = Shopify::SingleTenderTransactionService.call(shop, shopify_id)
    transaction_args = { order_id: shopify_order_id,
                         payment_method: transaction.paymentMethod,
                         amount: transaction.amount,
                         id: shopify_id,
                         remote_reference: transaction.remoteReference,
                         processed_at: transaction.processedAt }
    SkippedRetry::RetryTenderTransactionJob.perform_async(transaction_args.to_json, shop_id)
    nil
  end

  def lexoffice_doc_type
    refund? ? Lexoffice::CreditNote : Lexoffice::Invoice
  end

  def invoice?
    target_type == 'Invoice'
  end

  def refund?
    target_type == 'Refund'
  end

  def transaction?
    target_type == 'Transaction'
  end

  def transaction_assignment_hint?
    target_type == 'Transaction Assignment Hint'
  end

  # does a document with the same shopify_id and target type exist and does it have a target_id set?
  def self.doc_exists?(shopify_id, shop_id, target_type)
    where(shopify_id:, shop_id:, target_type:).where.not(target_id: nil).exists?
  end

  # Wofür ist das gut? Den Excluded Status merken wir uns doch im last_action Feld.
  def self.excluded_doc_exists?(shopify_id, shop_id, target_type)
    where(shopify_id:, shop_id:, target_type:)
      .exists?(['extra_infos LIKE ?', '%:status: excluded%'])
  end

  private

  def broadcast_transfer_log_create
    ActionCable.server.broadcast("transfer_log_#{shop_id}", {
                                   shopify_order_id:,
                                   last_action:,
                                   target_type:,
                                   action: 'create'
                                 })
  end

  def broadcast_transfer_log_update
    return unless saved_change_to_last_action?

    ActionCable.server.broadcast("transfer_log_#{shop_id}", {
                                   shopify_order_id:,
                                   last_action:,
                                   target_type:,
                                   action: 'update'
                                 })
  end
end
