# frozen_string_literal: true

class ShopSetting < ApplicationRecord
  audited max_audits: 50
  belongs_to :shop
  before_save :ensure_mark_due_on_send_mail
  before_save :ensure_tender_transaction_enabled
  after_save :handle_rules

  include CentralEventLogger::Trackable

  validates_with LexliquidValidator
  validates_with ExpandedTitleLengthValidator
  validates :invoice_pretext, :invoice_posttext, :refund_pretext, :refund_posttext, length: { maximum: 1800 }
  validates :shipping_max_days, numericality: { greater_than: :shipping_min_days },
    if: -> { (shipping_type || "").include? "period" }

  def invoice_timing_create?
    invoice_timing == "orders/create"
  end

  def invoice_timing_fulfill?
    invoice_timing == "orders/fulfilled"
  end

  def handle_rules
    HandleRulesService.call(self, shop)
  end

  private

  def ensure_mark_due_on_send_mail
    self.mark_due_immediately = true if send_invoice_mail
  end

  def ensure_tender_transaction_enabled
    self.enable_tender_transactions = true if enable_tah_creation
  end
end
