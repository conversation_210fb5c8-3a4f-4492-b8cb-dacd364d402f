# frozen_string_literal: true

# The JobStatus class is used to track the progress of an import job
# It is used in conjunction with the JobStatusWorker
class JobStatus < ApplicationRecord
  belongs_to :shop
  self.primary_key = 'guid'
  scope :for_shop, ->(shop_id) { where(shop_id:) }
  scope :running_for_shop, ->(shop_id) { where(shop_id:, running: true) }

  def self.shop_has_running_job?(shop_id)
    running_for_shop(shop_id).count.positive?
  end

  def increase_progress(successful_doc_type = nil)
    JobStatus.transaction do
      case successful_doc_type
      when :invoice
        self.invoices_processed += 1
      when :refund
        self.refunds_processed += 1
      when :transaction
        self.transactions_processed += 1
      end

      self.current_job_count = current_job_count + 1
      percent_value = (current_job_count.to_f / total_job_count) * 100 if total_job_count.positive?
      self.percent = percent_value || 0
      # job done?
      if current_job_count >= total_job_count
        self.running = false
        self.message = I18n.t('import.finish', job_count: current_job_count)
      end
      save!
      broadcast_progress
    rescue ActiveRecord::StaleObjectError
      reload
      Rails.logger.debug 'STALE OBJECT!!!!!!!!!!!!!!!!!!!!!!!!!'
      retry
    end
  end

  def start_progress(message, percent, stage)
    update(message:, percent:, stage:, running: true)
    broadcast_progress
  end

  def update_message(message)
    update(message:)
    broadcast_progress
  end

  def finish_job(message)
    update(message:, running: false)
    broadcast_progress
  end

  def broadcast_progress
    ActionCable.server.broadcast("job_status_#{id}", { progress: percent,
                                                       status: message,
                                                       invoices_queued:,
                                                       invoices_processed:,
                                                       invoices_skipped:,
                                                       refunds_queued:,
                                                       refunds_processed:,
                                                       refunds_skipped:,
                                                       transactions_queued:,
                                                       transactions_processed:,
                                                       transactions_skipped:,
                                                       current_job_count:,
                                                       total_job_count:,
                                                       finished:,
                                                       failed_doc_count:,
                                                       success: finished_without_errors })
  end

  def finished
    !running && percent >= 100
  end

  def finished_without_errors
    finished &&
      invoices_queued == invoices_processed &&
      refunds_queued == refunds_processed &&
      transactions_queued == transactions_processed
  end

  def failed_doc_count
    (invoices_queued || 0) - (invoices_processed || 0) +
      (refunds_queued || 0) - (refunds_processed || 0) +
      (transactions_queued || 0) - (transactions_processed || 0)
  end

  def handle_exception_status(message)
    update(message:, running: false)
    logger.debug message
  end

  def update_with_optimistic_locking(attributes)
    tries = 3
    begin
      update(attributes)
    rescue ActiveRecord::StaleObjectError => e
      raise e unless tries.positive?

      tries -= 1
      reload
      retry
    end
  end
end
