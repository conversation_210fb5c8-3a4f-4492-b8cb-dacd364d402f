# frozen_string_literal: true

# model for SupportUsers with access to all app data. Used for audit log accessing customer data
class SupportUser < ApplicationRecord
  has_many :support_user_events, dependent: nil

  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable
  devise :database_authenticatable, :trackable, :rememberable, :validatable

  def self.from_google(auth)
    find_or_create_by(provider: auth[:provider], uid: auth[:uid]) do |user|
      user.email = auth[:email]
      user.password = Devise.friendly_token[0, 20]
      # If you are using confirmable and the provider(s) you use validate emails,
      # uncomment the line below to skip the confirmation emails.
      # user.skip_confirmation!
    end
  end

  def admin?
    email == "<EMAIL>"
  end

  def name
    email
  end

  # Track support user events
  def track(name, properties = {})
    SupportUserEvent.create(support_user: self, name:, properties:, time: Time.current)
  end
end
