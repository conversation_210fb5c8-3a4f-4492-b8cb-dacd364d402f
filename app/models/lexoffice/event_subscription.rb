module Lexoffice
  class EventSubscription < Base
    include Rails.application.routes.url_helpers

    attr_accessor :callback_url

    def register_token_revoked_hook
      Rails.application.routes.url_helpers.lexoffice_token_revoked_url
      @data = {
        eventType: 'token.revoked',
        callbackUrl: lexoffice_token_revoked_url # "#{ENV["APP_HOME"]}lexoffice_token_revoked"
      }
      create
    end

    def register_invoice_status_changed_hook
      @data = {
        eventType: 'invoice.status.changed',
        callbackUrl: invoice_status_changed_url
      }
      create
    end

    def register_credit_note_status_changed_hook
      @data = {
        eventType: 'credit-note.status.changed',
        callbackUrl: credit_note_status_changed_url
      }
      create
    end

  end
end
