# frozen_string_literal: true

module Lexoffice
  class Invoice < Lexoffice::Base
    attr_accessor :mail_data, :tax_rates_counts
    attr_reader :status, :voucher_title, :total_price, :document_id

    def to_honeybadger_context
      { lexoffice_request: data.clone.tap do |invoice|
                             break if invoice.nil?

                             invoice[:address] = "*****" if invoice[:address].present?
                           end }
    end

    def create(finalize: false, _original_invoice_id: "")
      # TODO: handle "precedingSalesVoucherId" => original_invoice_id
      query_params = URI.encode_www_form("finalize" => finalize)
      rescue_with_error_context do
        response = nil
        Retriable.with_context(:lexoffice_api) do
          response = JSON.parse(RestClient.post("#{ENV.fetch("LEXOFFICE_API")}/v1/#{class_to_resturl}?#{query_params}",
            data.to_json, headers))
        end
        @id = response["id"]
      end
    end

    # Finds the invoice in lexoffice and writes the key infos to the object fields
    def find(id)
      response = super.with_indifferent_access
      @id = response[:id]
      @status = response[:voucherStatus]
      @voucher_title = response[:voucherNumber]
      @total_price = response.dig(:totalPrice, :totalGrossAmount)
      @document_id = response.dig(:files, :documentFileId)
      response
    end

    def documentID(id)
      rescue_with_error_context do
        response = JSON.parse(RestClient.get("#{ENV.fetch("LEXOFFICE_API")}/v1/#{class_to_resturl}/#{id}/document",
          headers))
        response["documentFileId"]
      end
    end

    def pdf(id)
      rescue_with_error_context do
        RestClient.get("#{ENV.fetch("LEXOFFICE_API")}/v1/files/#{documentID(id)}", headers)
      end
    end

    def voucher_number(id)
      rescue_with_error_context do
        response = JSON.parse(RestClient.get("#{ENV.fetch("LEXOFFICE_API")}/v1/#{class_to_resturl}/#{id}", headers))
        response["voucherNumber"]
      end
    end

    def send_mail_call
      rescue_with_error_context do
        RestClient.post "#{ENV.fetch("LEXOFFICE_API")}/v1/#{class_to_resturl}/#{id}/sendmail", mail_data.to_json,
          headers
      end
    end

    def data_from_order(order, shop_settings)
      address = if should_skip_address?(shop_settings, order)
                  {}
                else
                  Lexoffice::AddressBuilderService.call(order, shop_settings.use_shipping_address_for_invoices)
                end

      @data = {
        language: "de",
        voucherDate: Lexoffice::VoucherDateService.call(order, shop_settings.invoice_timing),
        address:,
        totalPrice: {
          currency: "EUR"
        },
        taxConditions: {
          taxType: "gross"
        },
        shippingConditions: {
          shippingType: "none"
        },
        lineItems: []
      }
    end

    def set_invoice_language(language)
      @data[:language] = language
    end

    def set_tax_type(tax_type)
      @data[:taxConditions][:taxType] = tax_type
    end

    def set_discount(discount_value)
      @data[:totalPrice][:totalDiscountAbsolute] = discount_value
    end

    def set_contact(contact_id)
      @data[:useCollectiveContact] = false
      @data[:address][:contactId] = contact_id
    end

    def gross_or_net
      @data[:taxConditions][:taxType] == "gross" ? "grossAmount" : "netAmount"
    end

    def set_title(text)
      # lexoffice title is max 25 chars
      sanitized_text = remove_expanding_chars(text)
      raise TitleTooLongError.new(sanitized_text, 25) if sanitized_text.length > 25

      @data[:title] = sanitized_text
    end

    def set_cpd_customer
      @data[:address][:name] = "Sammelkunde Shopify" if @data[:address][:name].nil?
      @data[:address][:countryCode] = "DE" if @data[:address][:countryCode].nil?
    end

    def set_introduction(text)
      @data[:introduction] = text
    end

    def set_date(settings, order)
      return if settings.shipping_type.nil?

      min_days = settings.shipping_min_days&.day || 0.days
      max_days = settings.shipping_max_days&.day || 1.day

      # calculate the starting date (order create or order fulfillment date)
      start_date = if settings.invoice_timing_fulfill? &&
          order.fulfillments.present?
                     order.fulfillments.first.created_at.to_date
                   else
                     order.processed_at.to_date
                   end

      if settings.shipping_type.include? "period"
        @data[:shippingConditions] = {
          shippingDate: (start_date + min_days).to_datetime,
          shippingEndDate: (start_date + max_days).to_datetime,
          shippingType: settings.shipping_type
        }
      elsif settings.shipping_type != "none"
        @data[:shippingConditions] = {
          shippingDate: (start_date + max_days).to_datetime,
          shippingType: settings.shipping_type
        }
      end
    end

    def set_layout(settings, type)
      @data[:printLayoutId] = settings.send("#{type}_layout_id") if settings.send("#{type}_layout_id").present?
    end

    def set_remark(text)
      @data[:remark] = text
    end

    def set_sub_tax_type(sub_tax_type)
      @data[:taxConditions][:taxSubType] = sub_tax_type
    end

    def set_tax_exempt_rate(shipping_lines, line_items, process_shipping_lines: true)
      set_tax_rate_to_zero(shipping_lines) if process_shipping_lines && shipping_lines.present?
      set_tax_rate_to_zero(line_items) if line_items.present?
    end

    def calc_shipping_tax_rate(line_items, shipping_lines)
      # return highest tax rate of shipping lines greater than zero
      shipping_rates = shipping_lines.map do |shipping_line|
        shipping_line = shipping_line.deep_symbolize_keys
        shipping_line[:tax_lines].present? ? (shipping_line[:tax_lines].first[:rate] * 100).to_d.round(2) : 0
      end

      return shipping_rates.max if shipping_rates.max.positive?

      # if not found return highest tax rate of lines_items no matter if it's greater than zero
      # (zero will be handled in the add_line_items method)
      line_items_rates = line_items.map do |line_item|
        line_item = line_item.deep_symbolize_keys
        line_item[:tax_lines].present? ? (line_item[:tax_lines].first[:rate] * 100).to_d.round(2) : 0
      end

      line_items_rates.max
    end

    def add_line_items(line_items, use_skus, order, shipping_tax_rate = nil, shipping_lines: false)
      @data[:lineItems] = [] if data[:lineItems].blank?

      line_items.each do |line_item|
        line_item = line_item.deep_symbolize_keys
        unit_price = {
          currency: "EUR",
          # in case of line_item is a shipping line set shipping_tax_rate, else set line_items tax rate
          taxRatePercentage: determine_tax_rate_percentage(line_item, shipping_tax_rate, order, shipping_lines:)
        }

        next if shipping_lines && free_shipping?(line_item)

        unit_price[gross_or_net] = line_item[:price]

        # Add SKU to name if shop_settings.use_SKUs is true
        sku = use_skus && line_item[:sku].present? ? " [#{line_item[:sku]}]" : ""
        line_name = limit_product_title("#{line_item[:name] || line_item[:title]}#{sku}")

        invoice_line_item = {
          type: "custom",
          name: line_name,
          quantity: line_item[:quantity] || 1,
          unitName: "Stück",
          unitPrice: unit_price
        }
        # Discounts
        apply_discount_to_line_item(line_item, invoice_line_item, order)
        @data[:lineItems] << invoice_line_item
      end
    end

    def send_mail(shopify_order, settings)
      return if shopify_order.email.blank?

      @mail_data = { recipients: [shopify_order.email] }
      if settings.invoice_mail_subject.present?
        @mail_data["subject"] =
          EshopGuide::DynamicTextService.call(settings.invoice_mail_subject, shopify_order)
      end
      if settings.invoice_mail_body.present?
        @mail_data["message"] =
          EshopGuide::DynamicTextService.call(settings.invoice_mail_body, shopify_order)
      end
      send_mail_call
    end

    private

    def determine_tax_rate_percentage(line_item, shipping_tax_rate, order, shipping_lines: false)
      if shipping_tax_rate.present?
        shipping_tax_rate
      elsif ShopifyAPI::LineItem.free_discount_with_zero_tax_rate?(line_item) ||
          ShopifyAPI::LineItem.taxable_with_no_price?(line_item)
        order.highest_applicable_tax_rate
      elsif line_item[:tax_lines].present? && ((line_item[:taxable] == true) || shipping_lines)
        (line_item[:tax_lines].first[:rate] * 100).to_d.round(2)
      else
        0
      end
    end

    def apply_discount_to_line_item(line_item, invoice_line_item, order)
      return unless order.discounts_present?
      return unless ShopifyAPI::LineItem.apply_line_item_discount?(order, line_item)
      return if line_item[:price].to_f.zero?

      result = ShopifyAPI::LineItem.get_all_line_item_discounts(line_item, order).to_f /
        (line_item[:price].to_f * invoice_line_item[:quantity]) * 100
      discounts = result.round(2)
      invoice_line_item[:discountPercentage] = discounts if discounts.positive?
    end

    def free_shipping?(line_item)
      price = line_item[:price].to_d
      return true if price <= 0

      discount_allocations = line_item[:discount_allocations]
      return false if discount_allocations.blank?

      discounts = discount_allocations.sum { |allocation| allocation[:amount].to_d }
      price == discounts
    end

    def set_tax_rate_to_zero(lines)
      lines.each do |line|
        line["tax_lines"].each do |tax_line|
          tax_line["rate"] = 0.0
        end
      end
    end

    # If you provide a contactId, Lexoffice already knows the address from the contact record,
    # so you don’t need to supply the address fields again unless you want to override them for this invoice.
    def should_skip_address?(shop_settings, order)
      shop_settings.create_customer &&
        order&.customer.is_a?(::ShopifyAPI::Customer) &&
        order.customer.email.present? &&
        order.customer.email != "null"
    end
  end
end
