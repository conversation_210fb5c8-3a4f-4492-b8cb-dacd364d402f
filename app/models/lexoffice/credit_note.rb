# frozen_string_literal: true

module Lexoffice
  class CreditNote < Lexoffice::Invoice
    attr_accessor :amount_discrepancy

    def data_from_refund_and_order(refund, order, shop_settings)
      @data = build_base_data_structure(refund, order)
      if should_skip_address?(shop_settings, order)
        @data[:address] = {} # Ensure address key exists
        return @data
      end

      @data[:address] = Lexoffice::AddressBuilderService.call(order, shop_settings.use_shipping_address_for_invoices)
      @data
    end

    def determine_tax_rate(order_adjustment)
      rate = ((order_adjustment["tax_amount"].to_f / order_adjustment["amount"]).round(2) * 100).to_i
      [7, 19].include?(rate) ? rate : 0
    end

    def get_gross(order_adjustment)
      (order_adjustment[:tax_amount].to_f + order_adjustment[:amount].to_f) * -1
    end

    def add_line_items(line_items, refund)
      @data["lineItems"] = @data["lineItems"] || []
      @tax_rates_counts ||= {}
      line_items.each do |line_item|
        # get tax_rate from order line item
        line_item = line_item.deep_symbolize_keys
        unit_price = {
          currency: "EUR",
          taxRatePercentage: determine_tax_rate_percentage(line_item, refund)
        }

        unit_price[gross_or_net] = line_item.dig(:line_item, :price)

        lineitem = {
          type: "custom",
          name: (line_item.dig(:line_item, :name) || line_item.dig(:line_item, :title)).first(255),
          quantity: line_item[:quantity] || 1,
          unitName: "Stück",
          unitPrice: unit_price,
          discountPercentage: 0
        }
        @data["lineItems"] << lineitem
      end
    end

    def handle_shipping_refund(order, refund)
      shipping_adjustments = refund.order_adjustments.select { |x| x.symbolize_keys[:kind] == "shipping_refund" }
      @tax_rates_counts ||= {}
      shipping_adjustments.each do |shipping_adjustment|
        shipping_adjustment = shipping_adjustment.deep_symbolize_keys
        unit_price = {
          currency: "EUR",
          taxRatePercentage: if use_shipping_tax_line?(order)
                               tax_rate = (order.shipping_lines.first.symbolize_keys[:tax_lines]
                                                .first.symbolize_keys[:rate] * 100).to_d.round(2)
                               @tax_rates_counts[tax_rate] = @tax_rates_counts[tax_rate].to_i + 1
                               tax_rate
                             else
                               0
                             end
        }
        tax_condition = gross_or_net
        unit_price[tax_condition] = (
          if tax_condition == "grossAmount"
            shipping_adjustment[:amount].to_d + shipping_adjustment[:tax_amount].to_d
          else
            shipping_adjustment[:amount].to_d
          end) * -1
        lineitem = {
          type: "custom",
          name: order.shipping_lines.first.symbolize_keys[:title].to_s.first(255),
          quantity: 1,
          unitName: "Stück",
          unitPrice: unit_price,
          discountPercentage: 0
        }

        @data["lineItems"] << lineitem
      end
    end

    def handle_amount_discrepancy(refund, use_brutto, order)
      @data["lineItems"] = @data["lineItems"] || []
      collect_tax_rates(order)
      return if @tax_rates_counts.nil?

      lineitem = HandleAmountDiscrepancyService.new(refund, use_brutto, @data, @tax_rates_counts).call
      return if lineitem.nil?

      self.amount_discrepancy = true
      @data["lineItems"] = [lineitem]
    end

    def send_mail(order_and_refund, settings)
      return unless order_and_refund["email"].present?

      @mail_data = { recipients: [order_and_refund["email"]] }
      if settings.credit_note_mail_subject.present?
        @mail_data["subject"] =
          EshopGuide::DynamicTextService.call(settings.credit_note_mail_subject, order_and_refund)
      end
      if settings.credit_note_mail_body.present?
        @mail_data["message"] =
          EshopGuide::DynamicTextService.call(settings.credit_note_mail_body, order_and_refund)
      end
      send_mail_call
    end

    def get_original_invoice(order)
      original_invoice = SyncInfo.where(shopify_id: order.id, target_type: "Invoice")
      original_invoice.first.target_id if original_invoice.present?
    end

    private

    def build_base_data_structure(refund, order)
      {
        voucherDate: Lexoffice::VoucherDateService.call(order, nil, refund),
        totalPrice: { currency: "EUR" },
        taxConditions: { taxType: "gross" }
      }
    end

    def determine_tax_rate_percentage(line_item, refund)
      if ShopifyAPI::LineItem.free_discount_with_zero_tax_rate?(line_item[:line_item]) ||
          ShopifyAPI::LineItem.taxable_with_no_price?(line_item[:line_item])
        refund.highest_tax_rate
      elsif use_tax_line?(line_item)
        tax_rate = (line_item.dig(:line_item, :tax_lines).first[:rate] * 100).to_d.round(2)
        # tax rate is a rate, @tax_rates_counts[tax_rate] is the number of time this tax rate
        # was used e.g. 19% was occured 3 time in an order, then at the end
        # should @tax_rates_counts[tax_rate] == 3
        @tax_rates_counts[tax_rate] = @tax_rates_counts[tax_rate].to_i + 1
        tax_rate
      else
        0
      end
    end

    def use_tax_line?(line_item)
      line_item.dig(:line_item, :tax_lines).present? && line_item.dig(:line_item, :tax_lines).first[:price] != "0.00"
    end

    def use_shipping_tax_line?(order)
      order.shipping_lines.first.symbolize_keys[:tax_lines].present? &&
        order.shipping_lines.first.symbolize_keys[:tax_lines].first.symbolize_keys[:price] != "0.00"
    end

    def collect_tax_rates(order)
      return if @tax_rates_counts.present? || order.nil?

      @tax_rates_counts = {}
      order.shipping_lines.each do |line|
        line = line.with_indifferent_access
        if line[:tax_lines].present? && line[:tax_lines].first[:price] != "0.00"
          tax_rate = line[:tax_lines].first[:rate] * 100
          @tax_rates_counts[tax_rate] = @tax_rates_counts[tax_rate].to_i + 1
        end
      end

      order.line_items.each do |line|
        line = line.with_indifferent_access
        if line[:tax_lines].present? && line[:tax_lines].first[:price] != "0.00"
          tax_rate = line[:tax_lines].first[:rate] * 100
          @tax_rates_counts[tax_rate] = @tax_rates_counts[tax_rate].to_i + 1
        end
      end
    end
  end
end
