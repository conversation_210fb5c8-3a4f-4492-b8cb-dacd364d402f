# frozen_string_literal: true

module Lexoffice
  # This is the base class for all lexoffice models
  class Base
    include ModelErrorReporting
    include StringSanitizer

    attr_accessor :auth_token, :data, :id, :lexoffice_endpoint

    def to_honeybadger_context
      { lexoffice_request: data.to_json }
    end

    def initialize(auth_token)
      @auth_token = auth_token
    end

    def create
      response = make_api_request(:post, class_to_resturl)
      @id = response["id"]
      response
    end

    def update(id)
      response = make_api_request(:put, "#{class_to_resturl}/#{id}")
      @id = response["id"]
      response
    end

    def find(id)
      response = make_api_request(:get, "#{class_to_resturl}/#{id}")
      @id = response["id"]
      response
    end

    def all
      return [] if @auth_token.blank?

      make_api_request(:get, class_to_resturl)
    end

    def headers
      { Authorization: "Bearer #{@auth_token}", Accept: "application/json", "Content-Type": "application/json" }
    end

    def class_to_resturl
      self.class.name.demodulize.pluralize.underscore.dasherize
    end

    private

    def make_api_request(method, endpoint, payload = nil)
      rescue_with_error_context do
        url = "#{ENV.fetch("LEXOFFICE_API")}/v1/#{endpoint}"
        payload_json = payload || (data&.to_json if %i[post put].include?(method))

        response = nil
        Retriable.with_context(:lexoffice_api) do
          response = case method
                     when :get
                       handle_get_request(url)
                     when :post
                       JSON.parse(RestClient.post(url, payload_json, headers))
                     when :put
                       JSON.parse(RestClient.put(url, payload_json, headers))
                     else
                       raise ArgumentError, "Unsupported HTTP method: #{method}"
                     end
        end
        response
      end
    end

    def handle_get_request(url)
      JSON.parse(RestClient.get(url, headers))
    rescue RestClient::Unauthorized
      Rails.logger.fatal "UNAUTHORIZED: #{auth_token}"
      raise
    end
  end
end
