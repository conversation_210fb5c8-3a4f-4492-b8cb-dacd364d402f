# frozen_string_literal: true

module Lexoffice
  # a specialisation of the finance object to make the transaction model
  class FinancialTransaction < Finance
    def initialize(auth_token, transaction_setting = nil, financial_transaction = nil, order = nil)
      super(auth_token)
      @transaction_setting = transaction_setting
      if financial_transaction.present? && !order.nil?
        self.data = [{
          financialAccountId: find_account_id(financial_transaction['payment_method']),
          bookingDate: financial_transaction['processed_at'],
          valueDate: financial_transaction['processed_at'],
          transactionDate: financial_transaction['processed_at'],
          purpose: "Shopify Auftrag: #{order.name}",
          amount: financial_transaction['amount'],
          externalReference: financial_transaction['remote_reference'],
          recipientOrSenderName: order.billing_address&.present? ? recipient_info(order.billing_address) : nil,
          additionalInfo: order.payment_gateway_names.join(',').humanize,
          recipientOrSenderEmail: order.email
        }]
      end
      self.lexoffice_endpoint = 'transactions'
    end

    def to_honeybadger_context
      # takes the original data hash and hides sensitive information in the externalReference,recipientOrSenderEmail
      # and recipientOrSenderName fields
      {
        lexoffice_request: data.first.clone.tap do |transaction|
          transaction[:externalReference] = '*****'
          transaction[:recipientOrSenderEmail] = '*****'
          transaction[:recipientOrSenderName] = '*****'
        end
      }
    end

    def find(id)
      response = find_by_id(id)
      {
        amount: response['amount']
      }
    end

    private

    def find_account_id(payment_method)
      payment_method = payment_method.tr(' ', '_').downcase
      if @transaction_setting.respond_to?("#{payment_method}_account_id")
        @transaction_setting["#{payment_method}_account_id"]
      else
        @transaction_setting.extra_accounts_info["#{payment_method}_account_id"]
      end
    end

    def recipient_info(billing_address)
      return if billing_address.blank?

      billing_address.symbolize_keys[:name]
    end
  end
end
