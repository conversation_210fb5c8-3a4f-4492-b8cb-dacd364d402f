# frozen_string_literal: true

module Lexoffice
  # models the lexoffice finance endpoint
  class Finance < Lexoffice::Base
    def to_honeybadger_context
      { lexoffice_request: data.to_json }
    end

    def find_by_id(id)
      Retriable.retriable do
        rescue_with_error_context do
          base_url = ENV.fetch('LEXOFFICE_API')
          endpoint = "/v1/finance/#{lexoffice_endpoint}/#{id}"
          url = "#{base_url}#{endpoint}"
          response = RestClient.get(url, headers)
          successful_response(response)
        end
      rescue RestClient::NotFound
        nil
      end
    end

    def create
      Retriable.retriable do
        rescue_with_error_context do
          JSON.parse(RestClient.post("#{ENV.fetch('LEXOFFICE_API')}/v1/finance/#{lexoffice_endpoint}", data.to_json,
                                     headers))
        end
      end
    end

    def find_or_create_finance_object(id = nil)
      return find_by_id(id) if id.present?

      create
    end

    private

    def successful_response(response)
      parsed_response = JSON.parse(response.body)
      return nil if response.body.empty?

      parsed_response
    end
  end
end
