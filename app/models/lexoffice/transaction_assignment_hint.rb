# frozen_string_literal: true

module Lexoffice
  class TransactionAssignmentHint < Lexoffice::Base
    def initialize(auth_token, voucher_id, external_reference)
      @auth_token = auth_token
      self.data = {
        'voucherId': voucher_id,
        'externalReference': external_reference
      }
    end

    def create
      rescue_with_error_context do
        Rails.error.set_context(lexoffice_request: data.to_json)
        response = JSON.parse(RestClient.post("#{ENV.fetch('LEXOFFICE_API')}/v1/transaction-assignment-hint",
                                              data.to_json, headers))
        @id = response['id']
        response
      end
    end
  end
end
