# frozen_string_literal: true

module Lexoffice
  # a specialisation of finance model to make a financial account model
  class FinancialAccount < Finance
    def initialize(auth_token, name)
      @auth_token = auth_token
      self.data = {
        type: 'PAYMENT_PROVIDER',
        externalReference: 'Shopify',
        accountSystem: 'PaymentProvider',
        bankName: name.humanize,
        balance: 0,
        name: "[Shopify] #{name.humanize}"
      }
      self.lexoffice_endpoint = 'accounts'
    end
  end

  def find_by_id(id)
    super(id)['financialAccountId']
  end
end
