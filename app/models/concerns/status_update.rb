

module StatusUpdate
  extend ActiveSupport::Concern

  def set_status(job_id, message, shop)
    job_status = JobStatus.find_or_create_by(guid: job_id)
    job_status.message = message
    job_status.running = true
    job_status.shop = shop
    job_status.save
    logger.debug message
  end

  def set_exception_status(job_id, message, shop)
    job_status = JobStatus.find_or_create_by(guid: job_id)
    job_status.message = message
    job_status.running = false
    job_status.shop = shop
    job_status.save
    logger.debug message
  end

  def set_progress(job_id, current, all)

    percent = (current.to_f/all.to_f)*100

    job_status = JobStatus.find_or_create_by(guid: job_id)
    job_status.percent = percent
    job_status.running = true
    job_status.save
    logger.debug percent
  end

  def set_status_with_progress(job_id, message, percent, stage, shop)
    job_status = JobStatus.find_or_create_by(guid: job_id)
    job_status.message = message
    job_status.percent = percent
    job_status.running = true
    job_status.stage = stage
    job_status.shop = shop
    job_status.save
    logger.debug message
    job_status
  end

  def set_job_complete(job_id, result_message)
    job_status = JobStatus.find_or_create_by(guid: job_id)
    job_status.running = false
    job_status.message = result_message
    job_status.save
  end

  def destroy_job_status(job_id)
    JobStatus.find(job_id).destroy!
  end


end