# frozen_string_literal: true

# Concern for string sanitization and manipulation methods
module StringSanitizer
  extend ActiveSupport::Concern

  # Limits an address field to 100 characters while preserving HTML entities
  # @param address [String] The address string to limit
  # @return [String, nil] The limited address string or nil if input was blank
  def limit_address_field(address)
    return nil if address.blank?

    truncate_html_safe(address, 100)
  end

  # Limits a product title to 255 characters while preserving HTML entities
  # @param string [String] The product title to limit
  # @return [String] The limited product title or empty string if input was blank
  def limit_product_title(string)
    return "" if string.blank?

    truncate_html_safe(string, 255)
  end

  # Removes characters that would expand when HTML encoded, preserving spaces
  # Also compresses consecutive spaces into a single space
  # @param string [String] The string to process
  # @return [String, nil] The string with expanding characters removed and consecutive spaces compressed,
  # or nil if input was blank
  def remove_expanding_chars(string)
    return nil if string.blank?

    # First remove expanding characters
    result = string.gsub(/[&<>"'©€®£¢§™]/, "")
    # Then compress consecutive spaces into a single space
    result.gsub(/\s+/, " ")
  end

  private

  # Core method to truncate a string while preserving HTML entities
  # @param text [String] The text to truncate
  # @param max_length [Integer] Maximum length to truncate to
  # @return [String] The truncated text with HTML entities preserved
  def truncate_html_safe(text, max_length)
    # HTML encode the string
    encoded = CGI.escapeHTML(text)
    # Limit to 100 characters
    truncated = encoded[0...max_length]
    # Find the last complete HTML entity
    last_entity_start = truncated.rindex("&")
    if last_entity_start && !truncated.index(";", last_entity_start)
      # If we have a partial entity at the end, remove it
      truncated = truncated[0...last_entity_start]
    end
    # HTML decode back
    CGI.unescapeHTML(truncated)
  end
end
