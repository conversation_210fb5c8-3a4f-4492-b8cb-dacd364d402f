# frozen_string_literal: true

module DiscountCalculator
  # This method is used to calculate the total line item discounts
  def self.calculate_line_item_discounts(items, discount_applications)
    total_discounts = 0.0

    items.each do |item|
      item = item.deep_symbolize_keys
      next unless item[:discount_allocations]&.any?

      item[:discount_allocations].each do |discount_allocation|
        discount_allocation = discount_allocation.deep_symbolize_keys

        discount_application_index = discount_allocation[:discount_application_index]
        discount_application = discount_applications[discount_application_index]
        next unless discount_application

        discount_application = discount_application.deep_symbolize_keys
        if valid_line_item_discount?(discount_application) || valid_free_shipping_line_item?(discount_application, item)
          total_discounts += discount_allocation[:amount].to_f
        end
      end
    end

    total_discounts.round(2)
  end

  # A valid line_item discount is based on the discount_application's allocation_methods
  def self.valid_line_item_discount?(discount_application)
    discount_application[:allocation_method] == 'each' ||
      (discount_application[:allocation_method] == 'across' && discount_application[:target_selection] == 'explicit')
  end

  # Handle all free shipping line_items as valid line_item discounts.
  def self.valid_free_shipping_line_item?(discount_application, item)
    return false unless discount_application[:target_type] == 'shipping_line'

    discount_match = item[:discount_allocations]&.any? { |allocation| allocation[:amount] == item[:price] }
    discount_match || (discount_application[:value] == '100.0' && discount_application[:value_type] == 'percentage')
  end
end
