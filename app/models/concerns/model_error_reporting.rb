# frozen_string_literal: true

module ModelErrorReporting
  extend ActiveSupport::Concern

  # This method is used to add additional context to the error report while adding extra context using proc
  def rescue_with_error_context(&)
    Rails.error.set_context(instance: self)

    extra_messages = []
    on_each_retry = proc do |try, elapsed_time, next_interval|
      extra_messages << "#{try} tries in #{elapsed_time} seconds and #{next_interval} seconds until the next try"
    end

    Retriable.with_context(:lexoffice_api, on_retry: on_each_retry, &)
  rescue RestClient::ExceptionWithResponse => e
    Rails.error.set_context(lexoffice_response: e.response, extra_info: extra_messages.join(" | "))

    error_message = extract_error_message(e)
    Rails.error.report(e, context: { error_message: "#{e}: #{error_message}" })

    raise e
  end

  private

  def extract_error_message(exception)
    return exception.message if exception.response.blank?

    JSON.parse(exception.response)["message"]
  rescue JSON::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NoMethodError
    exception.message
  end
end
