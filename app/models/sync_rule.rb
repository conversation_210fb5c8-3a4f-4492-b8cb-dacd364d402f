# frozen_string_literal: true

# The SyncRule model is used to store the rules for the synchronization of orders, refunds and tender transactions.
# It determines which shopify entity is synced to a lexoffice entity and takes care of dynamically creating and deleting
# webhooks for the synchronization.
# In short: if a rule exists for an entity, it will be synced.
class SyncRule < ApplicationRecord
  belongs_to :shop
  has_many :sync_infos

  scope :order_rules, -> { where(shopify_entity_type: 'Order') }
  scope :refund_rules, -> { where(shopify_entity_type: 'Refund') }
  scope :tender_transaction_rules, -> { where(shopify_entity_type: 'TenderTransaction') }
  scope :with_live_sync, -> { where(live_sync: true) }
  scope :for_shop, ->(shop_id) { where(shop_id:) }
  scope :for_hook, ->(hook) { where(webhooks: hook) }

  after_destroy :delete_webhooks
  after_save :handle_webhooks

  ALL_FIELDS_TOPICS = %w[tender_transactions/create].freeze

  def find_webhook_topics
    webhooks&.split(',') || []
  end

  private

  def handle_webhooks
    shop.with_shopify_session do
      topics = find_webhook_topics
      webhooks = ShopifyAPI::Webhook.all
      # Only select webhooks for the relevant Topics
      webhooks = webhooks.to_a.select { |hook| topics&.include? hook.topic }
      if live_sync?
        # register webhook, if there are none registered for the relevant topics
        register_webhooks(topics) if webhooks.empty?
      else
        delete_webhooks_if_not_needed(webhooks)
        # delete webhooks, if there are none registered for the relevant topics
      end
    end
  end

  def delete_webhooks
    shop.with_shopify_session do
      topics = find_webhook_topics
      webhooks = ShopifyAPI::Webhook.all
      # Only select webhooks for the relevant Topics
      webhooks = webhooks.to_a.select { |hook| topics.include? hook.topic }
      # delete webhooks, if there are none registered for the relevant topics
      delete_webhooks_if_not_needed(webhooks)
    end
  end

  def delete_webhooks_if_not_needed(webhooks)
    # check if webhook is still being used
    rules = SyncRule.where(
      'sync_rules.shopify_entity_type = ? AND sync_rules.shop_id = ? AND sync_rules.live_sync=? AND webhooks=?',
      shopify_entity_type, shop, true, self.webhooks
    )
    unregister_webhooks(webhooks) if rules.empty?
  end

  def register_webhooks(topics)
    address = "#{ENV.fetch('APP_HOME', 'https://lexoffice-integration.herokuapp.com')}/sync/"
    topics.each do |topic|
      # if topic is in ALL_FIELDS_TOPICS, we need to register a webhook with all fields
      # otherwise we register a webhook with only the id field
      webhook = if ALL_FIELDS_TOPICS.include?(topic)
                  ShopifyAPI::Webhook.new(from_hash: { topic:,
                                                       format: 'json',
                                                       address: (address + topic.tr('/', '_')) })
                else
                  ShopifyAPI::Webhook.new(from_hash: { topic:,
                                                       format: 'json',
                                                       address: (address + topic.tr('/', '_')),
                                                       fields: %i[id order_id] })
                end
      webhook.save!
    end
  end

  def unregister_webhooks(webhooks)
    webhooks.each(&:delete)
  end
end
