# frozen_string_literal: true

class Shop < ApplicationRecord
  include ShopifyApp::ShopSessionStorageWithScopes
  include ShopifyBilling::Concerns::ShopBilling

  has_one :shop_setting, dependent: :destroy
  has_one :transaction_setting, dependent: :destroy
  belongs_to :service_instance
  has_many :sync_rules, dependent: :delete_all
  has_many :job_statuses, dependent: :destroy
  has_many :error_logs, dependent: :destroy
  has_many :sync_infos, dependent: :destroy
  belongs_to :campaign_coupon_code, optional: true

  after_create :schedule_plan_activation_mail

  def self.find_or_create_new(shop_session)
    FindOrCreateShopBySessionService.new(shop_session).call
  end

  def shop_setting
    ShopSetting.find_by(shop_id: id)
  end

  def vat_free?
    lexoffice_small_business || lexoffice_tax_type == 'vatfree'
  end

  def api_version
    ShopifyAPI::Context.api_version
  end

  def execute_graphql_query(&block)
    with_shopify_session do |session|
      client = ShopifyAPI::Clients::Graphql::Admin.new(session:)
      yield(client) if block
    end
  end

  def reset_lexoffice_connection
    OAuthRevokeService.call(self)
  end

  def mark_for_connection_reset
    update(connection_needs_auth_refresh: true)
  end

  def send_mail_for_connection_reset
    # check if mail has been sent in interval, if so, don't send another one and return
    return unless send_error_mail?

    with_shopify_session do
      NotificationsJob.perform_async(to_json, 'warn', 'email', I18n.t('error_mail.instructions'))
      update(last_error_mail_sent: Time.zone.now)
    end
  end

  def has_running_import?
    JobStatus.exists?(shop_id: id, running: true)
  end

  def import_unlocked?
    import_manually_unlocked_at.present? || import_unlocked_at.present?
  end

  def after_activate_one_time_purchase
    update!(import_unlocked_at: Time.zone.now)

    NotificationsJob.perform_async(@shop.to_json, 'import', 'notification') if Rails.env.production?
  end

  # rubocop:disable Style/OptionalBooleanParameter
  def refresh_token_if_expired(force = false)
    OAuthRefreshTokenService.new(self, force).call if lexoffice_token.present? && lexoffice_refresh_token.present?
  end
  # rubocop:enable Style/OptionalBooleanParameter

  def update_shop_info
    return unless must_be_updated?

    with_shopify_session do
      shopify_shop_infos = ShopifyAPI::Shop.current
      update(email: shopify_shop_infos.email, shop_owner: shopify_shop_infos.shop_owner,
             name: shopify_shop_infos.name, country: shopify_shop_infos.country)
      shopify_shop_infos
    end
  end

  def add_to_mailchimp_subscribers
    return unless Rails.env.production?

    # add to mailchimp subscribers
    MailchimpSubscribeJob.perform_async(id)
  end

  def connected_to_lexoffice?
    lexoffice_token.present? && lexoffice_refresh_token.present? && !connection_needs_auth_refresh
  end

  def should_run_invoice_info_job?
    connected_to_lexoffice? && plan_active? && !invoice_info_job_ran
  end

  def add_or_update_access_scopes
    scopes = ShopifyAPI::AccessScope.all.map(&:handle).join(',')
    update(access_scopes: scopes) if access_scopes != scopes
  end

  def admin_url
    "https://#{shopify_domain}/admin/apps/#{ENV.fetch('SHOPIFY_CLIENT_API_KEY')}"
  end

  def errors?
    error_logs.exists?
  end

  def send_install_notifications
    return unless Rails.env.production?

    # send welcome email to shop owner
    NotificationsJob.perform_async(to_json, 'new_install', 'email')
    # notify slack channel
    NotificationsJob.perform_async(to_json, 'install', 'notification')
  end

  private

  def send_error_mail?
    last_error_mail_sent.nil? ||
      ((Time.zone.now - last_error_mail_sent) / 1.hour) > ENV['ERROR_MAIL_INTERVAL_HOURS'].to_i
  end

  def must_be_updated?
    email.nil? || shop_owner.nil? || country.nil? || name.nil?
  end

  def schedule_plan_activation_mail
    SendPlanActivationMailJob.perform_in(3.days, id) if Rails.env.production?
  end
end
