# frozen_string_literal: true

# This class stores the settings for the creation of transactions in Lexoffice. Mainly the assosiated account numbers
# and if transactions should be created for a given payment method
class TransactionSetting < ApplicationRecord
  serialize :extra_accounts_info, type: Hash, coder: YAML
  audited max_audits: 50
  belongs_to :shop
  include CentralEventLogger::Trackable
end
