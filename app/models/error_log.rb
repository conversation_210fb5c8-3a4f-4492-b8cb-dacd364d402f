# frozen_string_literal: true

# The ErrorLog class stores information about errors that occur during the transmission of data to lexoffice.
# It enables the user to see the errors and to retry the failed transmission.
class ErrorLog < ApplicationRecord
  belongs_to :shop
  belongs_to :error_type, optional: true
  before_save :ensure_error_type

  attr_accessor :helpscout_article_id

  scope :for_shop, ->(shop_id) { where(shop_id:) }

  self.per_page = 10

  def self.handleException(e, shop_id, entity, ref_order_id = nil)
    entity = find_entity(shop_id, ref_order_id) if entity.nil? && ref_order_id.present?
    entity_id = entity&.id || ref_order_id

    find_or_create_by(shop_id:, shopify_id: entity_id) do |error_entry|
      error_entry.error_type = get_type e
      error_entry.shopify_name = get_order_name(shop_id, ref_order_id, entity)
      error_entry.error_info_internal = e.respond_to?(:response) ? e.response : e.message
      error_entry.order_id = ref_order_id
      error_entry.shopify_type = infer_shopify_type(entity)
      error_entry.error_info_external = error_entry.build_external_info e
      error_entry.exception_type = e.to_s
      if entity.respond_to?(:retry_params) && entity.retry_params.present?
        error_entry.retry_params = entity.retry_params
      end
    end
  end

  def self.get_order_name(shop_id, ref_order_id, entity)
    name = nil
    if (entity.respond_to?(:name) == false || entity&.name.nil?) && ref_order_id
      shop = Shop.find(shop_id)
      shop.with_shopify_session do
        order = ShopifyAPI::Order.find(id: ref_order_id)
        name = order.name unless order.nil?
      rescue ShopifyAPI::Errors::HttpResponseError
        return nil
      end
    end

    name = entity&.name if name.nil? && entity.respond_to?(:name)
    name
  end

  def build_external_info(_e)
    if error_type.nil?
      I18n.t 'exceptions.unknown'
    else
      I18n.t error_type.external_info_text_key
    end
  end

  def self.get_type(e)
    error_message = e.message
    error_message = e.response if e.respond_to?(:response)
    ErrorType.all.find_each do |type|
      return type if error_message.to_s.include? type.signal
    end
    nil
  end

  def retry_job(retry_at = Time.zone.now)
    case shopify_type
    when 'ShopifyAPI::Order'
      ErrorRetry::RetryOrderJob.perform_at(retry_at, shop.shop_setting.invoice_timing, shopify_id, shop_id)
    when 'ShopifyAPI::Refund'
      ErrorRetry::RetryRefundJob.perform_at(retry_at, 'refunds/create', shopify_id, order_id, shop_id)
    when 'tender_transaction'
      ErrorRetry::RetryTenderTransactionJob.perform_at(retry_at, retry_params, shop_id)
    when 'transaction_assignment_hint'
      ErrorRetry::RetryTahCreationJob.perform_at(retry_at, shop_id, order_id, retry_params['order_name'],
                                                 retry_params['sync_info_target_id'], retry_params['arguments'])
    end
    delete
  end

  def helpscout_article_id
    error_type&.helpscout_article_id || ENV.fetch('DEFAULT_HS_ERROR_ARTICLE', nil) # default article
  end

  def self.infer_shopify_type(entity)
    return nil if entity.nil?

    case entity
    when ShopifyAPI::Order
      'ShopifyAPI::Order'
    when ShopifyAPI::Refund
      'ShopifyAPI::Refund'
    else
      entity['shopify_type']
    end
  end

  def self.find_entity(shop_id, ref_order_id)
    shop = Shop.find(shop_id)
    shop.with_shopify_session { ShopifyAPI::Order.find(id: ref_order_id) }
  rescue ShopifyAPI::Errors::HttpResponseError
    nil
  end

  private

  def ensure_error_type
    return if error_type_id.present?

    self.error_type = ErrorType.find_by(signal: 'Unknown')
  end
end
