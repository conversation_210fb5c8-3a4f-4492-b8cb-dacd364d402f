# frozen_string_literal: true

# Validator to check if a title with expanded Liquid variables exceeds the maximum length
class ExpandedTitleLengthValidator < ActiveModel::Validator
  include StringSanitizer

  MAX_LENGTH = 25
  # Fallback sample order structure with common placeholders
  SAMPLE_ORDER_STRUCTURE = {
    id: 123_456_789,
    name: "#10001",
    email: "<EMAIL>",
    created_at: nil, # will be set dynamically
    processed_at: nil, # will be set dynamically
    currency: "EUR",
    total_price: "99.99",
    subtotal_price: "89.99",
    total_tax: "10.00",
    financial_status: "paid",
    fulfillment_status: "fulfilled",
    customer: {
      id: 987_654_321,
      email: "<EMAIL>",
      first_name: "<PERSON>",
      last_name: "<PERSON><PERSON>",
      phone: "+49123456789"
    },
    billing_address: {
      first_name: "<PERSON>",
      last_name: "<PERSON><PERSON>",
      address1: "123 Billing St",
      city: "Berlin",
      zip: "10115",
      province: "Berlin",
      country: "Germany",
      country_code: "DE"
    },
    shipping_address: {
      first_name: "<PERSON>",
      last_name: "<PERSON><PERSON>",
      address1: "123 Shipping St",
      city: "Berlin",
      zip: "10115",
      province: "Berlin",
      country: "Germany",
      country_code: "DE"
    },
    line_items: [
      {
        id: 111_222_333,
        title: "Sample Product",
        price: "49.99",
        quantity: 2,
        sku: "SAMPLE-001"
      }
    ]
  }.freeze

  def validate(record)
    validate_field(record, :invoice_title)
    validate_field(record, :refund_title)
  end

  private

  def validate_field(record, field)
    return if record[field].blank?

    if contains_liquid_variables?(record[field])
      validate_expanded_length(record, field)
    else
      validate_simple_length(record, field)
    end
  end

  def contains_liquid_variables?(text)
    text.include?("{{") && text.include?("}}")
  end

  def validate_expanded_length(record, field)
    sample_order = get_sample_order(record.shop)
    return if sample_order.nil?

    expanded_text = expand_liquid_variables(record[field], sample_order)
    return if expanded_text.nil?

    sanitized_expanded_text = remove_expanding_chars(expanded_text)
    return unless sanitized_expanded_text.length > MAX_LENGTH

    add_expanded_too_long_error(record, field, sanitized_expanded_text.length)
  end

  def validate_simple_length(record, field)
    sanitized_text = remove_expanding_chars(record[field])
    return unless sanitized_text.length > MAX_LENGTH

    add_too_long_error(record, field)
  end

  def add_expanded_too_long_error(record, field, current_length)
    record.errors.add(
      field,
      :expanded_too_long,
      max_length: MAX_LENGTH,
      current_length: current_length
    )
  end

  def add_too_long_error(record, field)
    record.errors.add(
      field,
      :too_long,
      max_length: MAX_LENGTH
    )
  end

  def get_sample_order(shop)
    sample_order = Rails.cache.read("sample_order_#{shop.id}")
    return ShopifyAPI::Order.new(from_hash: JSON.parse(sample_order)) if sample_order.present?

    shop.with_shopify_session do
      order = ShopifyAPI::Order.all(limit: 1).first
      return order unless order.nil?

      return sample_order_structure
    rescue StandardError => e
      Rails.logger.error("Error fetching sample order for validation: #{e.message}")
      return sample_order_structure
    end
  end

  def expand_liquid_variables(text, order)
    template = Liquid::Template.parse(text, error_mode: :strict)
    template.render(JSON.parse(order.to_json))
  rescue StandardError => e
    Rails.logger.error("Error expanding liquid variables: #{e.message}")
    nil
  end

  def sample_order_structure
    structure = SAMPLE_ORDER_STRUCTURE.deep_dup
    structure[:created_at] = Time.now.iso8601
    structure[:processed_at] = Time.now.iso8601
    ShopifyAPI::Order.new(from_hash: structure)
  end
end
