class LexliquidValidator < ActiveModel::Validator
  def validate(record)
    to_be_validated = %w(invoice_title invoice_pretext invoice_posttext refund_title refund_pretext refund_posttext invoice_mail_body invoice_mail_subject credit_note_mail_body credit_note_mail_subject)
    to_be_validated.each do |attribute|
      validator = LiquidValidator::Validator.new(record[attribute])
      unless validator.valid?
        validator.errors.each do |error|
          record.errors.add "liquid_error_#{attribute}", error
        end
      end
    end
  end
end
