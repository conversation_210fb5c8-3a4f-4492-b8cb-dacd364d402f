// This is a manifest file that'll be compiled into application.js, which will include all the files
// listed below.
//
// Any JavaScript/Coffee file within this directory, lib/assets/javascripts, vendor/assets/javascripts,
// or vendor/assets/javascripts of plugins, if any, can be referenced here using a relative path.
//
// It's not advisable to add code directly here, but if you do, it'll appear at the bottom of the
// compiled file.
//
// Read Sprockets README (https://github.com/sstephenson/sprockets#sprockets-directives) for details
// about supported directives.
//
//= require jquery
//= require rails-ujs
//= require turbolinks
//= require bootstrap-sprockets
//= require bootstrap-material-design
//= require bootstrap-editable
//= require bootstrap-editable-rails
//= require bootstrap-datepicker/core
//= require bootstrap-datepicker/locales/bootstrap-datepicker.en-GB.js
//= require bootstrap-datepicker/locales/bootstrap-datepicker.de.js
//= require_tree .


$(function(){
    $.material.init();

    //disable click on disabled buttons
    $('a[disabled=disabled]').click(function(event){
        event.preventDefault(); // Prevent link from following its href
    });
});
document.addEventListener("turbolinks:load", function() {
    $.material.init();
    //disable click on disabled buttons
    $('a[disabled=disabled]').click(function(event){
        event.preventDefault(); // Prevent link from following its href
    });
})