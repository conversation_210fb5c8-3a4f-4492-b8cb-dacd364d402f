// import the CSS framework
@import "bootstrap-sprockets";
@import "bootstrap";

// make all images responsive by default
img {
  @extend .img-responsive;
  margin: 0 auto;
  }
// override for the 'Home' navigation link
.navbar-brand {
  font-size: inherit;
  }

// THESE ARE EXAMPLES YOU CAN MODIFY
// create your own classes
// to make views framework-neutral
.column {
  @extend .col-md-6;
  @extend .text-center;
  }
.form {
  @extend .col-md-6;
  }
.form-centered {
  @extend .col-md-6;
  @extend .text-center;
  }
.submit {
  @extend .btn;
  @extend .btn-primary;
  @extend .btn-lg;
  }
// apply styles to HTML elements
// to make views framework-neutral
main {
  @extend .container;
  background-color: #eee;
  padding-bottom: 80px;
  width: 100%;
  margin-top: 51px; // accommodate the navbar
  }
section {
  @extend .row;
  margin-top: 20px;
  }

// Styles for form views
// using Bootstrap
// generated by the rails_layout gem
.authform {
  padding-top: 30px;
  max-width: 320px;
  margin: 0 auto;
}
.authform form {
  @extend .well;
  @extend .well-lg;
  padding-bottom: 40px;
}
.authform .right {
  float: right !important;
}
.authform .button {
  @extend .btn;
  @extend .btn-primary;
}
.authform fieldset {
  @extend .well;
}
#error_explanation:not(:empty) {
  @extend .alert;
  @extend .alert-danger;
}
#error_explanation h2 {
  font-size: 16px;
}
.button-xs {
  @extend .btn;
  @extend .btn-primary;
  @extend .btn-xs;
}
