/*COLORS*/
$basic-color: #f07c00;
$pro-color: #31373d;
$import-color: #0288D1;
$coupon-color: #5c626b;
$medium-grey: #5C626B;
$light-grey: #f4f4f4;
$anthrazite: #333;
$white: #fff;

.btn-primary:not(.btn-link):not(.btn-flat) {
  background-color: #0078bd;
  color: rgba(255, 255, 255, 0.84);
}

.btn-primary:hover:not(.btn-link):not(.btn-flat) {
  background-color: #0085CC;
}

.navbar-header .logo {
  display: inline-block;
  float: left;
  height: 60px;
  padding: 15px;
}

a.navbar-brand {
    font-size: 16px;
}
.navbar-inverse.navbar{
  background-color: #343b42;
}

.table-overflow{
  overflow: auto;
}

.table-margined{
  margin: 2%;
}

.table-shop-settings{
  width:2000px;
}

.flex-header{
  display: flex;
  justify-content: space-between;
  align-items: center;
}

hr {
  margin-top: 0;
  margin-bottom: 0;
  border: 0;
  border-top: 1px solid #eeeeee;
}

a, a:hover, a:focus {
  color: #343b42;
}

a:hover
{
  text-decoration: none;
}
.p-t-30
{
  padding-top: 30px;
}

.well h2 {
  margin-top: 0;
}

/*********MATERIAL CHECKBOXES OVERRIDE*****/
.checkbox input[type=checkbox]:checked + .checkbox-material .check:before, .checkbox-default input[type=checkbox]:checked + .checkbox-material .check:before {
  color: hsla(210,12%,27%,1);
}
.checkbox input[type=checkbox]:checked + .checkbox-material .check, .checkbox-default input[type=checkbox]:checked + .checkbox-material .check {
  color: hsl(210, 12%, 23%);
}

/**********POPOVER HELP**********/

.checkbox-with-help .checkbox{
  display: inline-block;
  margin-right: 5px;
  margin-top: 0;
  margin-bottom: 10px;
}

 .checkbox + .checkbox-with-help {
  margin-top: -5px;
}

.popover{
  font-family: roboto;
  max-width:400px;
  min-width: 300px;
}

.popover-title{
  font-size: 16px;
  font-weight: bold;
}

.popover-content{
  font-size: 14px;
  line-height: 1.3;
}

body.tucan-blue
{
background-color: hsla(202,98%,43%,1);
}

/*************WELL Extension************/
.well.well-steps
{
  position:relative;

  h3
  {
    margin-top: 0;
  }

  &.well-success#step1
  {
    border-left: 50px solid hsla(122,39%,37%,1);;
  }
}
.sevdesk{
  .well.well-steps#step1{
    border-left: 50px solid hsl(0, 55%, 40%);
  }
  .well.well-steps#step2{
    border-left: 50px solid hsl(0, 70%, 40%);
  }
  .well.well-steps#step3{
    border-left: 50px solid hsl(0, 85%, 40%);
  }
  .well.well-steps#step4{
    border-left: 50px solid hsl(0, 100%, 40%);
  }
  .well.well-steps#step5{
    border-left: 50px solid hsl(0, 100%, 40%);
  }
}
.pebesmart{
  .well.well-steps#step1{
    border-left: 50px solid hsla(220, 40%, 28%, 1)
  }
  .well.well-steps#step2{
    border-left: 50px solid hsla(220, 60%, 28%, 1)
  }
  .well.well-steps#step3{
    border-left: 50px solid hsla(220, 80%, 28%, 1)
  }
  .well.well-steps#step4{
    border-left: 50px solid hsla(220, 90%, 28%, 1)
  }
  .well.well-steps#step5{
    border-left: 50px solid hsla(220, 100%, 28%, 1)
  }
}
.einsundeins{
  .well.well-steps#step1{
    border-left: 50px solid hsl(219, 40%, 33%)
  }
  .well.well-steps#step2{
    border-left: 50px solid hsl(219, 60%, 33%)
  }
  .well.well-steps#step3{
    border-left: 50px solid hsl(219, 80%, 33%)
  }
  .well.well-steps#step4{
    border-left: 50px solid hsl(219, 90%, 33%)
  }
  .well.well-steps#step5{
    border-left: 50px solid hsl(219, 100%, 33%)
  }
}
.lexoffice{
  .well.well-steps#step1{
    border-left: 50px solid hsl(31, 100%, 47%)
  }
  .well.well-steps#step2{
    border-left: 50px solid hsl(31, 100%, 67%)
  }

}

.lexoffice_orange_light{
  background-color: #f07c002b;
}

.well-steps:before
{
  position: absolute;
  left: -40px;
  top: 0;
  color: $white;
  font-size: 50px;
  font-weight: bold;
}

.well-steps#step1:before{
  content:"1";
}
.well-steps#step2:before{
  content:"2";
}
.well-steps#step3:before{
  content:"3";
}
.well-steps#step4:before{
  content:"4";
}
.well-steps#step5:before{
  content:"5";
}

.well.well-sync-all
{
  background: #343b42;
  transition: background-size 0.3s ease;

  &:not(.disabled)
  {
    &:hover
    {
      background-size: 80px, 80px, 150%;
    }

    &:hover .sync-tucan {
      transform: translateX(-50%);
      opacity: 1;
      transition: transform 0.3s ease, opacity 0.1s ease;
    }
  }

  .no-rules-msg,.no-plan-msg
  {
    display:none;
  }

  .no-plan-msg.visible,.no-rules-msg.visible
  {
    display:block;
    text-align: center;
    margin-bottom: 10px;

    span
    {
      color: white;
      font-size: 20px;
      text-shadow: 0 0 4px black;
      background-color: rgba(0, 0, 0, 0.42);
      padding: 8px;
      border-radius: 17px;

      a{color: white;}
    }
  }


  .sync-indicator
  {
    opacity: 0;
    width: 230px;
    top:50%;
    left:10%;
    background: linear-gradient(to right, hsla(0,0%,100%,0) 0%,hsla(0, 0%, 100%, 0.78) 53%,hsla(0,0%,0%,0) 100%);
    border-radius: 5px;
    height: 80px;
    position: absolute;
    transition: all 0.3s ease;

  }

  &.syncing
  {
    animation: syncanim 1s infinite alternate;
    background-size: 80px, 80px, 150%;
  }
}
@keyframes syncanim{
  from {background-position: 5%,95%,0;}
  to {background-position: 5%,95%,100%;}
}

.btn.btn-sync-all
{
  margin-top: 10px;
}
.sync-tucan
{
  width: 100px;
  height: 80px;
  left: 50%;
  position: relative;
  transform: translateX(-50%);
  transform-origin: bottom;
  background-image: image-url("sevdesk_logo.png");
  transition: transform 0.3s ease, opacity 0.2s 0.1s ease;
  opacity: 0;
  pointer-events: none;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.job-done {
  height: 200px;
  width: 200px;
  background-image: image_url("job_done.png");
  position: absolute;
  background-size: contain;
  right: 10%;
  top: 66px;
  pointer-events: none;
  z-index: 10;
  opacity: 0;

  &.animate
  {
    animation: stamp 5s cubic-bezier(0.44, 0.01, 1, 0.05);
  }
}

@keyframes stamp {
  0% {opacity: 0;
      transform: scale(1.0) rotate(-10deg);}

  15% {opacity: 1;
    transform: scale(0.75) rotate(0deg);}


  90% {opacity: 1;opacity: 1;
    transform: scale(0.75) rotate(0deg);}

  100%{opacity: 0;
    transform: scale(1) rotate(10deg);}
}

/*********STATUS********************/
.well.well-status{
  transform: translateY(10px);
  z-index: 1;
  opacity: 0;
  transition: transform 0.5s ease, opacity 0.1s ease;
  background-color: hsla(210,12%,39%,1);
  border-radius: 20px;

  &.syncing{
    transform: translateY(0px);
    opacity:1;
  }

  span.stage {
    color: white;
    font-size: 21px;
    font-weight: bold;
    text-shadow: 0 0 11px rgba(0, 0, 0, 0.29);
    line-height: 20px;
  }
  span.status {
    color: white;
    font-size: 21px;
    font-weight: bold;
    text-shadow: 0 0 11px rgba(0, 0, 0, 0.29);
    line-height: 20px;
  }




  .progress
  {
    height: 20px;
    margin-bottom: 0;
    border-radius: 15px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.14);
    background-color: $white;
    margin-top: 8px;

    .progress-bar{
      background-color:#028ad9;
      font-weight: bold;
      font-size: 15px;
      text-shadow: 0 0 3px black;
    }

  }
}

/*******************************************/

/*****************BUTTON STYLES*********/
.btn-dark:not(.btn-link):not(.btn-flat) {
  background-color: #343b42;
  color: rgba(255, 255, 255, 0.84);
}
.btn-dark:hover:not(.btn-link):not(.btn-flat) {
  background-color: #15181b;
}

.btn-basic:not(.btn-link):not(.btn-flat) {
  background-color: darken($basic-color,10%);
  color: rgba(255, 255, 255, 0.84);
}
.btn-basic:hover:not(.btn-link):not(.btn-flat) {
  background-color: darken($basic-color,15%);
}

.btn-pro:not(.btn-link):not(.btn-flat) {
  background-color: darken($pro-color,10%);
  color: rgba(255, 255, 255, 0.84);
}
.btn-pro:hover:not(.btn-link):not(.btn-flat) {
  background-color:  darken($pro-color,15%);
}

.btn-import:not(.btn-link):not(.btn-flat) {
  background-color: darken($import-color,10%);
  color: rgba(255, 255, 255, 0.84);
}

.btn-review:not(.btn-link):not(.btn-flat) {
  background-color: #5c626b;
  color: rgba(255, 255, 255, 0.84);
}
.btn-review:hover:not(.btn-link):not(.btn-flat) {
  background-color: #454545;
  border-bottom: 4px solid #f07c00;
}

.btn-refresh-history{
  display: inline-block;
  margin-top: 22px;
  margin-left: 10px;
}

.btn-activate:not(.btn-link):not(.btn-flat){
  top: 20%;
  background-color: $basic-color ;
  color: white ;
}
.btn-activate:hover:not(.btn-link):not(.btn-flat) {
  background-color: darken($basic-color, 10%) ;
}

/**************Table styles*******/
table td .btn.btn-xs{
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 0;
  padding-bottom: 0;
}
th,tfoot{
  background: $medium-grey;
  color: $white;
}
.table > thead > tr > th{
  border-bottom: none;
}
.table > tbody > tr > td{
  border-top:none;
}


/********FAB NEW-Group****/
.btn.btn-new-group {
  position: absolute;
  right: 40px;
}


/********CARDS*********/
.card {
  display: inline-block;
  position: relative;
  width: 100%;
  margin-bottom: 30px;
  border-radius: 6px;
  color: rgba(0,0,0, 0.87);
  background: $white;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
}

.card .board-name
{
  font-weight: bold;
}
.card [class*="content-"] ,.card [class*="content-"] a {
  color: $white;
}
.card [class*="content-"] {
  border-radius: 6px;
}
.card .content {
  min-height: 120px;
  padding: 15px 30px 0;
  height: 100%;
}
.card .footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  left: 0;
  padding-left: 30px;
  background-color: hsla(0, 0%, 0%, 0.1);
  min-height: 30px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;

  .stats
  {
    font-size:18px;
    h3{
      font-weight: bold;
    }
  }
}

.edit-hover {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.52);
  width: 100%;
  height: 100%;
  color: white;
  border-radius: 8px;
  transition: all 0.3s ease;
  opacity: 0;

  &.tucan-wrapper
  {
    background-color: rgb(0, 121, 191);
  }

  .tucan{
    transition: right 0.5s ease;
    width: 104px;
    height:100px;
    background-image: image-url("tucan.png");
    position: absolute;
    bottom:0;
    right: -104px;
    background-repeat: no-repeat;
    background-position: right;
    .tucan-speech
    {
      transition: transform 0.5s ease;
      font-weight: bold;
      font-size: 18px;
      position: absolute;
      top: 34px;
      left: -44px;
      transform: rotate(0deg) scale(0);

      &.board {
        top: -11px;
      }
    }
  }
}

.icon-wrapper {
  top: 50%;
  left: 50%;
  transform: translate(-50%, 0%);
  position: absolute;
  transition: transform 0.3s ease;
  color:white;
}

/*********INLINE EDITING OVERRIDES******************/
.editable-input
{
  padding: 7px 0;
}


/*************Hover Card**********************/
.card.card-hover
{
  transition: box-shadow 0.3s ease, transform 0.3s ease;
  &:hover
  {
    box-shadow: 0 2px 16px 2px rgba(0, 0, 0, 0.44), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
    transform: translate3d(0,-5px,0);
    z-index: 1000;
  }
}

img.no-rules
{
  height: 100px;
  float:left;
}

/************RULE CARDS************************/
.card.rule-card
{
  border-top: 10px solid #EF6C00;



  /*&:after
  {
    content: 'Rule';
      position: absolute;
      color: #ff9d24;
      font-size: 40px;
      top: -46px;
      left: 20px;
      font-weight: bold;
      text-shadow: 1px -2px 5px rgba(0, 0, 0, 0.58);
  }

  &#card1:after
  {
    content: 'Rule 1';
  } &#card2:after
  {
    content: 'Rule 2';
  } &#card3:after
  {
    content: 'Rule 3';
  } &#card4:after
  {
    content: 'Rule 4';
  }*/

  .rule-header
  {
    height:50px;
    background: linear-gradient(to bottom, hsla(33,100%,65%,1) 0%,hsla(33,100%,52%,1) 50%,hsla(33,100%,52%,1) 50%,hsla(50,100%,52%,1) 50%,hsla(54,100%,52%,1) 100%);

    .shopify-type
    {
      background-color: #343b42;
      background-image: image-url("shopify-bag.png");
      background-size: 30px;
      background-position: 10px 50%;
      background-repeat: no-repeat;
      height:50px;
      margin-right: -13px;

      padding: 14px 0 14px 50px;

      a{
          /* padding: 10px 0 10px 0; */
          color: white;
          border-bottom: none;
        font-size: 18px;
      }

      .editable-input
      {
        padding:0;
      }

      select{
        color: white;
        background-color: #343b42;
        font-size:18px;
        height:24px;
      }
    }
  }

  .stats {
    border-top: 2px solid #ef6c00;
    background-color: hsla(27,100%,95%,1);
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 5px;

    a{
      margin-top:2px;
      margin-bottom:2px;
    }
  }
}

label.disabled {
  opacity: 0.2;
}

.pro-plan-lock
{
  cursor:pointer;
  color: #343b42;
}

/**********POPOVER/TOOLTIP OVERRIDE**********/
.popover{
  color: rgb(236, 236, 236);
  line-height: 1em;
  box-shadow: rgba(0, 0, 0, 0.117647) 0 1px 6px 0, rgba(0, 0, 0, 0.117647) 0 1px 6px 0;
  background: rgba(255, 255, 255, 1);
  border-width: initial;
  border-style: none;
  border-color: initial;
  border-image: initial;
  border-radius: 2px;
}
.tooltip-inner {
    color: #343b42;
    line-height: 1em;
    box-shadow: rgba(0, 0, 0, 0.117647) 0 1px 6px 0, rgba(0, 0, 0, 0.117647) 0 1px 6px 0;
    background: rgba(255, 255, 255, 1);
    border-width: initial;
    border-style: none;
    border-color: initial;
    border-image: initial;
    border-radius: 2px;
  }

.popover
{
  .popover-title,.popover-content
  {
    color: black;
  }
}
/*******BILLING PAGE****/



.well.tucan-bg
{
  background-image: image-url("lexoffice_logo.png");
  background-position: 98% center;
  background-repeat: no-repeat;
  background-color: #ffba70;
  margin-bottom: 20px;
  color: white;
  background-size: 70px;
}

.discount-badge, .discount-badge-import {
  text-align: center;
  color: white;
  background: lighten($basic-color, 10%);
  height: 30px;
  position: absolute;
  width: 127px;
  right: 30px;
  top: -10px;
  padding-top: 5px;
  &.import {
    background: lighten($import-color, 10%);
  }
}
@mixin discount-badge {
  border-left: 64px solid transparent;
  border-right: 64px solid transparent;
  height: 0;
  content: "";
  left: 0;
  position: absolute;
  top: 30px;
  width: 0;
  transform: rotate(180deg);
}
.discount-badge:after {
  @include discount-badge;
  border-bottom: 24px solid lighten($basic-color, 10%);

}
.discount-badge-import:after {
  @include discount-badge;
  border-bottom: 24px solid lighten($import-color, 10%);
}
i.active
{
  position: absolute;
  top: -10px;
  left: 0;
  background-color: hsla(0,0%,32%,1);
  transform: rotate(-10deg);
  border: 3px solid white;
  font-size: 18px;
  padding: 10px 15px;
  box-shadow: 7px 7px 9px -2px rgba(0, 0, 0, 0.25);
}
i.inactive { display: none }
.well.well-billingplan
{
  color:white;
  text-align: center;

  &.import.active::after {
    content: 'Aktiviert';
  }

  &.active.basic::after
  {
    background-color: darken($basic-color,10%);
  }
  &.active.pro::after
  {
    background-color: darken($pro-color,10%);
  }



  .title{
    font-weight: bold;
    font-size: 24px;
    position: relative;

    &::after{
      content: ' ';
      height: 2px;
      width: 200%;
      position: absolute;
      left: -50%;
      bottom: -10px;
      background: linear-gradient(to right,rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.69) 50%, rgba(255, 255, 255, 0) 100%);
    }
  }

  &.coupon{
    background-color: $coupon-color;
  }

  &.basic{
    background-color: $basic-color;
  }

  &.pro{
    background-color: $pro-color;
  }

  &.import{
    background-color: $import-color;
    .locked-hint{
      display: none;
    }
    &.locked .locked-hint{
      display: block;
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      background-color: rgba(2, 41, 65, 0.74);
      margin: 0 15px 20px 15px;
      padding: 100px;
      font-weight: 700;
    }
  }

  .whatuget
  {
    margin-top: 15px;
    min-height:100px;
    .header{
      font-size: 20px;
    }
  }

  .whatuget .form-group .form-control{
    color: white;
    background-image: linear-gradient($pro-color, $pro-color), linear-gradient(#d2d2d2, #d2d2d2);
  }

.price-and-button
{
text-align: left;
}

span.price-tag {
background-color: rgba(0, 0, 0, 0.09);
padding: 10px;
margin: 10px;
border-radius: 19px;
font-size: 18px;
font-weight: bold;
border: 1px solid rgba(255, 255, 255, 0.45);
box-shadow: 0 0 9px rgba(255, 255, 255, 0.43);
}

}

.nav-tabs
{
background-color:#5C626B;
li.active{
background-color:#8e959f;
border-bottom: 4px solid #cc0000;

.sevdesk & {
  border-bottom: 4px solid #cc0000;
}
.pebesmart & {
  border-bottom: 4px solid #002f90;
}
.einsundeins & {
  border-bottom: 4px solid #134094;
}
.lexoffice & {
  border-bottom: 4px solid #f07c00;
}
}
}

/**********Settings***********/
.panel.panel-dark .panel-heading {
  display: flex;
  place-content: space-between;
  background-color: #5c626b;
  h3{
    font-size: 19px;
  }
}
.help-block
{
  font-size: 10px;
}
/**********Beacon***********/
.beacon-help-wrapper .popover-content{
  padding: 0;

  .beacon-message-link{
    padding: 5px 15px;
    cursor: pointer;
    display: block;
    background-color: $light-grey;
    border-radius: 15px;
    margin: 13px 20px;
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
  }
  .beacon-message-link:hover{
    background-color: $medium-grey;
    color: $white;
  }
}


a.beacon-button {
  color: $white;
  cursor: pointer;
}

.help-frame {
  border: 0;
  height: 830px;
  width: 100%;
}

/******PLAN NOT ACTIVATED HINT**********/
.activate-billing-hint {
  position: absolute;
  display: flex;
  height: 100%;
  width: 100%;
  background: -webkit-radial-gradient(ellipse, rgba(49, 55, 61, 0.28), rgba(49, 55, 61, 0.42));
  background: -moz-radial-gradient(ellipse, rgba(49, 55, 61, 0.28), rgba(49, 55, 61, 0.42));
  z-index: 100;
  padding-right: 30px;
  padding-bottom: 20px;
  background-clip: content-box;
  align-items: flex-start;
  justify-content: center;

}

.activate-billing-wrapper {
  display: grid;
  grid-row-gap: 20px;
  margin-top: 180px;
  width: 50%;
}
.activate-billing-wrapper span {
  background-color: $pro-color;
  border-radius: 7px;
  opacity: 0.75;
  color: white;
  text-align: center;
  box-shadow: 0 0 9px rgba(255, 255, 255, 0.43);
  padding: 2%;
}

/*FIX für springenden Input*/
.input-daterange {
  input:focus
  {
    float:left;
  }
}

.input-daterange + input.btn{
  margin-top:30px;
}
#start-import{
  text-align: center;
}
.import-header,
.errors-log-header {
  display: flex;
}

.import-header .beacon-button,
.errors-log-header .beacon-button {
  color: $pro-color;
}

.import-header .beacon-help-wrapper {
  margin-left: 10px;
  margin-top: 5px;
}

.errors-log-header {
  align-items: center;
  .beacon-help-wrapper {
    margin-left: 10px;
    margin-top: 10px;
  }
}

.ordercount
{
  font-size: 30px;
  padding: 11px;
  background-color:#F07D06;
}

.instance-card{
  height: 90px;
  border: 1px solid #868686;
  border-radius: 10px;
  padding: 10px;
  box-shadow: 0 0 6px 0 #a5a5a5;
  cursor: pointer;
  display: flex;
  transition: all .3s ease;
  opacity: .6;
  img{
    max-height:65px;
  }

  &.active{
      background-color: white;
      opacity: 1;
      box-shadow: 0 4px 11px 0 #5d5d5d;
      transform: translateY(-10px);

    &.sevdesk {
       box-shadow: 0 3px 11px 0 #cc0000;
    }
    &.pebesmart {
      box-shadow: 0 3px 11px 0 #002f90;
    }
    &.einsundeins {
      box-shadow: 0 3px 11px 0 #134094;
    }

  }
  &:hover:not(.active){
      background-color: white;
      opacity: .7;
      box-shadow: 0 4px 11px 0 #5d5d5d;
      transform: translateY(-5px);
  }
}


.sync_info_wrapper{
  margin-top:10px;
  overflow-x: auto;

  h2{ margin-top: 20px;}
}
.support_import_wrapper{
  display: flex;
}
.rest_button_wrapper{
  justify-content: flex-end;
  display: flex;
}

/*PAGINATION*/
.pagination {
  font-size: 12px;
}

.pagination > * {
  margin-right: 0.1em;
  padding: 0.3em 0.4em;
  border-radius: 5px;
}

.pagination a:hover {
  background: #202020 none repeat scroll 0 0;
  text-shadow: 1px 1px 1px #171717;
  color: white;
  border-radius: 5px;
}

.pagination a:active {
  text-shadow: none;
  color: white;

}

.pagination .current {
  background: #202020 none repeat scroll 0 0;
  color: white;
  text-shadow: 1px 1px 1px #171717;
}

.pagination .disabled {
  color: #C0C0C0;
}

table.table-dummy {
  -webkit-filter: blur(3px);
  -moz-filter: blur(3px);
  -o-filter: blur(3px);
  -ms-filter: blur(3px);
  filter: blur(3px);
}

.pac-loader {
  position: absolute;
  z-index: 10;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.error_log_wrapper .table .btn{
  margin:0;
}

.helpscout-sidebar-link::after {
  padding-left: 5px;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  font-family: "FontAwesome"; font-weight: 900; content: "\f059";
}

.DV_link
{
  text-decoration: underline;
}

/*Review Buttons*/
.review-buttons-wrapper{
  overflow: hidden;
  height: 80px;

  .quality-buttons button {
    font-size: 1.5em;
  }

  .quality-wrapper{
    position: relative;
    transition: bottom .2s ease-in-out, opacity .2s ease-in-out;
    opacity: 1;
    bottom: 0;
  }
  .store-review-button-wrapper,
  .feedback-form-wrapper{
    position: absolute;
    transition: top .2s ease-in-out, opacity .2s ease-in-out;
    opacity: 0;
    top: 80px;
  }

  &.good{
    .quality-wrapper{
      bottom: 80px;
      opacity: 0;
    }
    .store-review-button-wrapper{
      top: 0;
      opacity: 1;
    }
  }
  &.meh{
    .quality-wrapper{
      bottom: 80px;
      opacity: 0;
    }
    .feedback-form-wrapper{
      top: 0;
      opacity: 1;
    }
  }
}

/*Helpers*/
.m-bot-30{
  margin-bottom:30px;
}

.text-white{
  color:white;
}

.lexoffice-plan-features{
  font-size: smaller;
  color: white;
}
.lexoffice-plan-features a {
  text-decoration: underline;
  color: white;
}

.badge-lg{
  padding:8px;
}

/*Support UI*/
.recurring_charge_hint,
.sync_infos,
.reset_plan_wrapper,
.entity_actions_wrapper,
.new_coupon_code_generator {
  display: flex;
  gap: 20px;
  align-items: center;
}

.discount_system_wrapper, .beacon-preview-wrapper {
  display: flex;
  place-content: space-around;
}

.support_select {
  border: none;
  padding: 19px 12px;
}

.single-input-label {
  color: #555555;
  font-size: 14px;
  font-weight: 400;
  line-height: 1;
  padding: 6px 12px;
  text-align: center;
}

.errors-log-wrapper {
  width: min-content;
}

.modal-content .modal-footer button {
  margin: 10px 1px;
}
/* auth form */
.dialog {
  width: 95%;
  max-width: 33em;
  margin: 4em auto 0;
}

.dialog > div {
  border: 1px solid $white;
  border-top-color: #CCC;
  border-right-color: #999;
  border-top-left-radius: 9px;
  border-top-right-radius: 9px;
  background-color: white;
  padding: 7px 12% 0;
  box-shadow: 0 3px 8px rgba(50, 50, 50, 0.17);
}

.dialog > p, div.dialog > form {
  background-color: #f4f4f4;
  border: 1px solid #999;
  border-left: 60px $white;
  border-radius: 4px;
  box-shadow: 0 3px 8px rgba(50, 50, 50, 0.17);
  color: #666;
  margin: 0 0 1em;

  padding: 1em;
  position: relative;
}

div.dialog > p:first-of-type {
  border-top-color: #DADADA;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.icon {
  height: 40px;
  left: -50px;
  position: absolute;
  top: 10px;
  width: 40px;
}

.settings-table {
  border: solid $anthrazite;
  border-width: 1px 1px 0 0;
  display: flex;
  flex-flow: row wrap;

  dt {
    background: $medium-grey;
    color: $white;
    flex-basis: 20%;
    padding: 2px 4px;
    text-align: right;
  }

  dd {
    border-bottom: 1px solid $anthrazite;
    flex-basis: 70%;
    flex-grow: 1;
    margin: 0;
    padding: 2px 4px;
  }
}

.input-group-addon:first-child {
  padding-left: 0;
}

.doc-type-checkboxes {
  align-items: end;
  display: flex;
  margin: 15px 0 5px;

  input {
    height: 15px;
    margin: 0 0 3px;
    width: 30px;
  }

  label {
    margin: 0;
  }
}
