// Place all the styles related to the ShopSettings controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/

.body-preview{

  main{
    margin-top: 0;
    padding-left: 0;
    padding-right: 0;
  }

  .alert-danger{
    margin-top: 0;
  }
}

.checkbox .checkbox-material .check:before {
  content: '.';
  color: transparent;
}

.transaction-setting .checkbox-with-help {
  margin-bottom: -15px;
}

.padding-left-12 {
  padding-left: 12px;
}

.eye-preview {
  width: 16px;
  height: 16px;
  fill: currentColor;
}

.preview {
  justify-content: flex-end;
  display: flex;
}

.preview-frame {
  background-color: white;
  padding: 30px;
  margin: 20px 40px;
  box-shadow: 2px 2px 5px 0px #c5c5c5;
}

.alert-danger {
  margin-top: 20px;
}

.open > .dropdown-menu {
  display: grid;
}
.date-picker, .dateintervallpicker{
  margin-left: 10px;
}
.date-picker-hide, .dateintervallpicker-hide{
  margin-left: 10px;
  display: none;
}
.dateintervallpicker .control-label {
  display: none;
}

.shipping-min-days .control-label, .shipping-max-days .control-label {
  display: flex;
}
.shipping-min-days .form-group, .shipping-max-days .form-group {
  margin-bottom: 0px;
}
.days-label{
  font-weight: 300;
  margin-bottom: 30px;
}
.shipping-select {
  a {
    display: none;
  }
}
.shipping-select-with-help {
  display: flex;
  a {
    display: block;
    margin-left: 10px;
    height: 16px;
  }
}
.dates-alert, .negative-dates-alert, .input-group .form-group .control-label {
  display: none;
}
.dates-alert-show, .negative-dates-alert-show {
  font-weight: 100;
  font-size: small;
  color: red;
  max-width: fit-content;
}