# frozen_string_literal: true

class InvalidBulkQueryError < StandardError; end

class RunBulkQueryService < ApplicationService
  def initialize(query)
    @query = query
  end

  def call
    bulk_query = ShopifyGraphql::CreateBulkQuery.call(query: @query)
    bulk_query_id = bulk_query.data.bulkOperation.id

    # Check status of bulk query every 2 seconds
    statuses = %w[CREATED RUNNING]

    while true
      bulk_query_result = ShopifyGraphql::GetBulkOperation.call(id: bulk_query_id)
      break unless statuses.include?(bulk_query_result.data.status)

      sleep 2.seconds
    end

    # Raise error if bulk query is not completed
    raise InvalidBulkQueryError unless bulk_query_result.data.status == 'COMPLETED'

    bulk_query_result
  end
end
