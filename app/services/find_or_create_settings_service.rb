# frozen_string_literal: true

class FindOrCreateSettingsService < EshopGuide::BaseReportingService
  attr_reader :shop_session

  def initialize(shop_session)
    @shop_session = shop_session
  end

  def call
    shop = Shop.find_by(shopify_domain: shop_session.shop)
    @remote_shop = shop.update_shop_info
    return shop if shop.shop_setting.present? && shop.transaction_setting.present?

    # If Shop isnt fully installed, we need to install it

    shop.send_install_notifications
    shop.add_to_mailchimp_subscribers
    ShopSetting.create(shop:) if shop.shop_setting.blank?
    TransactionSetting.create(shop:) if shop.transaction_setting.blank?

    report_install(shop)
    shop
  end

  private

  def report_install(shop)
    report_event(
      event_name: 'app_installed',
      event_type: CentralEventLogger::EventTypes::USER_ACQUISITION,
      customer_myshopify_domain: shop.shopify_domain,
      customer_info: {
        name: shop.name,
        owner: shop.shop_owner,
        email: shop.email
      },
      event_value: @remote_shop&.plan_name,
      payload: {
        shop_name: shop.name,
        shop_owner: shop.shop_owner,
        country: shop.country,
        shopify_plan: @remote_shop&.plan_name,
        shopify_plan_display_name: @remote_shop&.plan_display_name
      },
      timestamp: Time.zone.now,
      external_id: "install:#{shop.shopify_domain}"
    )
  end
end
