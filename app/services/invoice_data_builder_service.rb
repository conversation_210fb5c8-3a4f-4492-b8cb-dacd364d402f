class InvoiceDataBuilderService
  def call(shop, order, rule, is_import_job, use_brutto, shop_settings, tax_type, contact_id = nil, contact = nil)
    context = InvoiceDataBuilderInteractor.call(shop:, order:, is_import_job:,
                                                use_brutto:, rule:, shop_settings:,
                                                contact_id:, contact:, tax_type:)

    return unless context.success?

    [context.invoice, context.sync_info]
  end
end
