# frozen_string_literal: true

class OrderJobFilter
  attr_reader :shop_settings

  def initialize(shop, shop_settings)
    @shop = shop
    @shop_settings = shop_settings
  end

  def filter?(order)
    excluded_by_pos?(order) ||
      excluded_by_financial_status?(order) ||
      excluded_by_tax?(order) ||
      cancelled_order?(order)
  end

  private

  def excluded_by_pos?(order)
    order.source_name == 'pos' && @shop_settings.excludePOS
  end

  def excluded_by_financial_status?(order)
    order.financial_status.casecmp('voided').zero?
  end

  def excluded_by_tax?(order)
    order.has_taxes? && !order.zero_sum_taxes? && @shop.lexoffice_small_business
  end

  def cancelled_order?(order)
    order.cancelled_and_pending?
  end
end
