# frozen_string_literal: true

class WithLockService
  class << self
    def call(lock_key, options = {}, &)
      raise ArgumentError, "Block is required" unless block_given?

      RedLockPool.instance.with do |lock_manager|
        lock_manager.lock!(
          lock_key.to_s.to_sym,
          ENV.fetch("LOCK_EXPIRATION", 30).to_i * 1000,
          retry_count: ENV.fetch("LOCK_RETRY_COUNT", 20).to_i,
          retry_delay: ENV.fetch("LOCK_RETRY_DELAY", 1).to_i * 1000, &
        )
      end
    rescue Redlock::LockError => e
      Rails.error.report(e, context: { tags: "LockError", options: options[:context] })
      raise e
    end
  end
end
