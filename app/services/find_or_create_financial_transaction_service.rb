# frozen_string_literal: true

# finance transaction handling service
class FindOrCreateFinancialTransactionService < ApplicationService
  def initialize(shop, transaction_setting, transaction_params)
    @shop = shop
    @transaction_setting = transaction_setting
    @financial_transaction = transaction_params
  end

  def call
    gateway = @financial_transaction['payment_method'].tr(' ', '_').downcase
    account_setting = if @transaction_setting.respond_to?("#{gateway}_account_id")
                        @transaction_setting.send("enable_#{gateway}")
                      else
                        @transaction_setting.extra_accounts_info["enable_#{gateway}"]
                      end
    return if account_setting.blank?

    sync_info = SyncInfo.find_or_initialize_by(shop_id: @shop.id, shopify_id: @financial_transaction['id'],
                                               target_type: 'Transaction')
    return if sync_info.persisted? && sync_info.doc_created?

    order = @shop.with_shopify_session do
      ShopifyAPI::Order.find(id: @financial_transaction['order_id'])
    end
    transaction_endpoint = Lexoffice::FinancialTransaction.new(@shop.lexoffice_token, @transaction_setting,
                                                               @financial_transaction, order)

    return if sync_info.persisted? && sync_info.doc_created? && sync_info.extra_infos.present?

    WithLockService.call("transaction_lock_#{@shop.id}", context: { user_id: @shop.shopify_domain }) do
      update_sync_info(sync_info, transaction_endpoint.create.first, order.name)
    end
  end

  private

  def update_sync_info(sync_info, financial_transaction, order_name)
    sync_info.update(
      target_id: financial_transaction['financialTransactionId'],
      shopify_order_id: @financial_transaction['order_id'],
      last_action: 'Created',
      shopify_order_name: order_name,
      extra_infos: { 'amount' => @financial_transaction['amount'] }
    )
  end
end
