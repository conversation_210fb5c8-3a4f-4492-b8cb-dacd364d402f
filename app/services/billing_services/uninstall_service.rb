# frozen_string_literal: true

module BillingServices
  class UninstallService < EshopGuide::BaseReportingService
    def initialize(shop)
      @shop = shop
    end

    def call
      report_uninstall
      perform_uninstall_tasks
    end

    private

    def report_uninstall
      report_event(
        event_name: 'app_uninstalled',
        event_type: CentralEventLogger::EventTypes::CHURN,
        customer_myshopify_domain: @shop.shopify_domain,
        event_value: @shop.billing_plan&.name,
        payload: {
          reason: 'user_choice',
          had_active_subscription: @shop.plan_active?
        },
        timestamp: Time.zone.now
      )
    end

    def perform_uninstall_tasks
      shop_infos = {
        shop_owner: @shop.shop_owner,
        shopify_domain: @shop.shopify_domain,
        email: @shop.email
      }

      # Reset app installation cache
      @shop.reset_app_installation_cache

      if Rails.env.production?
        NotificationsJob.perform_async(shop_infos.to_json, 'uninstall', 'email')
        NotificationsJob.perform_async(shop_infos.to_json, 'uninstall', 'notification')
        MailchimpUnsubscribeJob.perform_async(shop_infos.to_json)
      end

      @shop.destroy
    end
  end
end
