# frozen_string_literal: true

module BillingServices
  class CheckPlanMismatchService < BaseService
    def initialize(shop:)
      @shop = shop
    end

    def call
      return if @shop.nil? || @shop.plan_mismatch_since? || @shop.billing_plan.nil?

      return unless !@shop.development_shop? && @shop.billing_plan.development_plan?

      Shop.transaction do
        @shop.update!(plan_mismatch_since: DateTime.now)

        # Send plan update mail now
        SendPlanMismatchNotificationJob.perform_async(@shop.id)

        # Send reminder in 13 days
        SendPlanMismatchNotificationJob.perform_in(13.days, @shop.id)

        # Reset billing plan in 14 days
        ResetBillingPlanJob.perform_in(14.days, @shop.id)
      end
    end
  end
end
