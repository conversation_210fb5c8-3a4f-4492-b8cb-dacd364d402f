# frozen_string_literal: true

require 'net/http'

class BulkQueryShopHasMultipleTaxLinesService < ApplicationService
  QUERY = <<~GRAPHQL
    query {
      orders(query: "created_at:>=2024-10-01") {
        edges {
          node {
            id
            taxLines {
              rate
            }
          }
        }
      }
    }
  GRAPHQL

  def call
    orders = fetch_orders
    orders.each_line do |line|
      order = JSON.parse(line)

      return true if order['taxLines'].count > 1
    end

    false
  end

  private

  def fetch_orders
    bulk_query_result = RunBulkQueryService.call(QUERY)
    uri = URI(bulk_query_result.data.url)
    Net::HTTP.get(uri)
  end
end
