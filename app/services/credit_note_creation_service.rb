# frozen_string_literal: true

class CreditNoteCreationService < EshopGuide::BaseReportingService
  attr_reader :finalize, :original_invoice_id, :credit_note, :sync_info, :settings, :mail_attributes, :send_mail

  def initialize(finalize, original_invoice_id, credit_note, sync_info)
    @finalize = finalize
    @original_invoice_id = original_invoice_id
    @credit_note = credit_note
    @sync_info = sync_info
  end

  def call
    credit_note_id = nil
    unless sync_info.doc_created?
      credit_note_id = credit_note.create(finalize: finalize, _original_invoice_id: original_invoice_id)
      # create the sync info and add the voucher properties
      sync_info.target_doc_create(credit_note_id)
      log_credit_note_creation
    end
    credit_note_id ||= sync_info.target_id
    credit_note.find(credit_note_id)

    sync_info.add_doc_properties(credit_note)
    credit_note
  end

  private

  def log_credit_note_creation
    shop = Shop.find(sync_info.shop_id)
    # Cache shopify_plan for 4 hours
    shopify_plan = Rails.cache.fetch("shopify_plan:#{shop.id}", expires_in: 4.hours) do
      shop.shopify_plan
    end
    report_event(
      event_name: 'credit_note_created',
      event_type: 'customer_usage',
      customer_myshopify_domain: shop.shopify_domain,
      event_value: @sync_info.extra_infos[:total_price],
      payload: {
        shopify_plan: { display_name: shopify_plan['displayName'] },
        app_plan: shop.billing_plan.name
      },
      timestamp: Time.current,
      external_id: sync_info.shopify_id
    )
  end
end
