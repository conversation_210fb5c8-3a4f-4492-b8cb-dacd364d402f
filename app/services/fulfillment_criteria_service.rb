# frozen_string_literal: true

# This service adds a use_fulfill_criteria attribute to an order based on the order's financial status and refunds.
# The use_fulfill_criteria attribute is used to determine if an order's line items should use the fulfillment array.
# The service is tested with three different contexts: when an order has refunds and the financial status is pending,
# when an order has refunds, is paid, and has no total outstanding, and when an order has refunds and the financial
# status is partially paid.

class FulfillmentCriteriaService
  def initialize(order, shop)
    @order = order
    @shop = shop
  end

  def call
    return if @order.refunds.blank?

    case @order.financial_status
    when 'pending', 'partially_paid'
      if no_transactions?
        add_fulfill_attribute(true)
        remove_shipping if shipping_refunded?
      else
        add_fulfill_attribute(false)
      end
    when 'paid'
      if @order.no_total_outstanding?
        add_fulfill_attribute(true)
      else
        add_fulfill_attribute(false)
      end
    else
      add_fulfill_attribute(false)
    end
  end

  private

  def no_transactions?
    @order.transactions.detect { |t| t.status == 'success' && t.amount.to_f.positive? }.nil?
  end

  def remove_shipping
    @order.shipping_lines = []
  end

  # Checks if there is a difference between the total subtotal price of refunds and total price. If the difference
  # between the total price and sum of subtotal refunds equals the shipping cost, we know that shipping costs were
  # removed from the order
  def shipping_refunded?
    total_price = @order.total_price.to_f
    current_total = total_refund + @order.current_total_price.to_f
    shipping_price = @order.total_shipping_price_set['shop_money']['amount'].to_f
    (total_price - current_total).round(2) == shipping_price
  end

  def total_refund
    @order.refunds.sum do |refund|
      refund.refund_line_items.sum do |item|
        item = item.deep_symbolize_keys
        item[:subtotal].to_f
      end
    end
  end

  # Add use_fulfill_criteria attribute to order
  def add_fulfill_attribute(value)
    @order.instance_variable_set(:@use_fulfill_criteria, value)
  end
end
