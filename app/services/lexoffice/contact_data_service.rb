# frozen_string_literal: true

module Lexoffice
  # service class to prepare data for contact creation
  class ContactDataService
    def initialize(order, use_shipping_address_for_invoices)
      @order = order
      @customer = @order&.customer
      @use_shipping_address_for_invoices = use_shipping_address_for_invoices
    end

    def call
      return nil if @customer.nil?

      customer_attributes[:shipping_address] = @order&.shipping_address
      customer_attributes[:vatId] = @order.vat_number

      customer_attributes.tap { |attributes| check_fake_email(attributes) }
                         .tap { |attributes| check_northern_ireland_country_code(attributes) }
    end

    private

    def customer_attributes
      @customer_attributes ||= if @order&.billing_address.present?
                                 build_customer_attributes_with_billing_address
                               else
                                 build_customer_attributes_without_billing_address
                               end
    end

    def build_customer_attributes_with_billing_address
      address_to_merge = @order.address_to_use_for_billing(@use_shipping_address_for_invoices)
      address_to_merge = @order&.billing_address&.symbolize_keys if address_to_merge.nil?
      @customer.to_hash.symbolize_keys.merge(address_to_merge&.compact)
    end

    def build_customer_attributes_without_billing_address
      base_attributes = @customer.to_hash.symbolize_keys
      if @customer.default_address.present?
        base_attributes.merge(@customer.default_address.symbolize_keys)
      else
        base_attributes
      end
    end

    def check_fake_email(attributes)
      return if @order.valid_email?

      attributes[:email] = "#{@customer.id}@lexoffice-shopify-app.de"
    end

    def check_northern_ireland_country_code(attributes)
      attributes[:country_code] = 'XI' if attributes[:province_code] == 'NIR'

      return if attributes[:shipping_address].nil? || attributes[:shipping_address][:province_code] != 'NIR'

      attributes[:shipping_address][:country_code] = 'XI'
    end
  end
end
