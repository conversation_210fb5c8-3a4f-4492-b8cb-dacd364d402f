# frozen_string_literal: true

module Lexoffice
  class AddressBuilderService < ApplicationService
    include StringSanitizer

    def initialize(order, use_shipping_address_for_invoices)
      @order = order
      @use_shipping_address_for_invoices = use_shipping_address_for_invoices
    end

    def call
      return build_default_address if @order.nil?

      billing_address = @order.address_to_use_for_billing(@use_shipping_address_for_invoices)
      if @order.customer.is_a?(::ShopifyAPI::Customer) && !valid_billing_address?(billing_address)
        return build_customer_address
      end
      return build_detailed_address(billing_address) if valid_billing_address?(billing_address)

      build_default_address
    end

    private

    def valid_billing_address?(billing_address)
      billing_address.is_a?(Hash) &&
        (billing_address.key?(:address1) || billing_address.key?("address1")) &&
        (billing_address.key?(:zip) || billing_address.key?("zip")) &&
        (billing_address.key?(:city) || billing_address.key?("city")) &&
        (billing_address.key?(:country_code) || billing_address.key?("country_code"))
    end

    def build_detailed_address(billing_address)
      billing_address = billing_address.with_indifferent_access
      address = {
        street: limit_address_field(billing_address[:address1]),
        zip: billing_address[:zip],
        city: billing_address[:city],
        countryCode: determine_country_code(billing_address[:country_code])
      }

      address[:supplement] = limit_address_field(billing_address[:company]) if valid_company?(billing_address[:company])
      address[:name] = build_full_name(billing_address)
      address
    end

    def determine_country_code(country_code)
      return "DE" if country_code.nil? || country_code == "*"

      country_code
    end

    def valid_company?(company)
      company.present? && company != "null"
    end

    def build_full_name(billing_address)
      billing_address = billing_address.with_indifferent_access
      "#{billing_address[:first_name]} #{billing_address[:last_name]}"
    end

    def build_customer_address
      {
        name: "#{@order.customer.first_name} #{@order.customer.last_name}",
        countryCode: "DE"
      }
    end

    def build_default_address
      {
        name: "Sammelkunde Shopify",
        countryCode: "DE"
      }
    end
  end
end
