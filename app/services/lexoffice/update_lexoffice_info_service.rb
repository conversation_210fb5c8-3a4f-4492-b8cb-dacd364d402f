# frozen_string_literal: true

module Lexoffice
  class UpdateLexofficeInfoService < ApplicationService
    def initialize(shop)
      @shop = shop
    end

    def call
      @shop.refresh_token_if_expired
      profile_endpoint = Lexoffice::Profile.new(@shop.lexoffice_token)
      profile_info = profile_endpoint.get_info
      @shop.update(
        lexoffice_organization_id: profile_info['organizationId'],
        lexoffice_tax_type: profile_info['taxType'],
        lexoffice_small_business: profile_info['smallBusiness'],
        lexoffice_connection_id: profile_info['connectionId'],
        distance_sales_principle: profile_info['distanceSalesPrinciple'].presence || 'not defined'
      )
      profile_info
    end
  end
end
