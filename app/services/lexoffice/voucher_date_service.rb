# frozen_string_literal: true

module Lexoffice
  class VoucherDateService < ApplicationService
    def initialize(order, invoice_timing = nil, refund = nil)
      @order = order
      @invoice_timing = invoice_timing
      @refund = refund
    end

    def call
      return formatted_time(@refund.processed_at) if refund_applicable?

      date_present? ? date : formatted_time(@order.processed_at)
    end

    private

    def date
      case @invoice_timing
      when 'orders/fulfilled'
        fulfillments_date
      when 'orders/create'
        formatted_time(@order.processed_at)
      end
    end

    def fulfillments_date
      first_fulfillment = @order.fulfillments&.first
      formatted_time(first_fulfillment&.created_at) unless first_fulfillment.nil?
    end

    def date_present?
      case @invoice_timing
      when 'orders/fulfilled'
        @order.fulfillments&.first&.created_at.present?
      when 'orders/create'
        @order.processed_at.present?
      else
        false
      end
    end

    def refund_applicable?
      !@refund.nil? && !@refund.processed_at.nil? && @invoice_timing.nil?
    end

    def formatted_time(time)
      Time.parse(time).iso8601(3) if time.present?
    end
  end
end
