# frozen_string_literal: true

module Lexoffice
  # create contact data depending on contact type, for lexoffice endpoint call.
  class CreateContactDataService < ApplicationService
    include StringSanitizer
    def initialize(customer, tax_type, roles = {}, version = 0, contact_id = nil)
      @customer = customer
      @roles = roles
      @version = version
      @contact_id = contact_id
      @tax_type = tax_type
      @data = {}
    end

    def call
      customer_attributes
      set_company_or_person_info
      set_email_address
      @data
    end

    private

    def company?
      @customer[:company].present? && @customer[:company] != 'null'
    end

    def set_company_or_person_info
      salutation = @customer[:salutation]
      last_name = safe_get_name(@customer[:last_name])
      first_name = safe_get_name(@customer[:first_name])
      if company?
        @data[:company] = {
          name: @customer[:company],
          allowTaxFreeInvoices: allow_tax_free_invoices?,
          contactPersons: [{
            salutation:,
            firstName: first_name,
            lastName: last_name,
            primary: true,
            emailAddress: @customer[:email],
            phoneNumber: @customer[:phone]
          }]
        }
        @data[:company][:vatRegistrationId] = @customer[:vatId] if @customer[:vatId].present?
      else
        @data[:person] = {
          salutation:,
          firstName: first_name,
          lastName: last_name
        }
      end
    end

    def set_email_address
      @data[:emailAddresses] = if company?
                                 {
                                   business: [@customer[:email]]
                                 }
                               else
                                 {
                                   private: [@customer[:email]]
                                 }
                               end
    end

    def customer_attributes
      @roles[:customer] = {} if @roles.nil? || @roles[:customer].nil?
      note = @customer[:note] || ''
      @data = {
        version: @version,
        roles: @roles,
        addresses: {
          billing: [
            { street: limit_address_field(@customer[:address1]),
              supplement: limit_address_field(@customer[:address2]),
              zip: @customer[:zip],
              city: @customer[:city],
              countryCode: @customer[:country_code] }
          ]
        },
        note: note[0..999]
      }

      # add phone number if present
      @data[:phoneNumbers] = { private: [@customer[:phone]] } if @customer[:phone].present?

      # add delivery_address if present
      return if @customer[:shipping_address].blank?

      @data[:addresses][:shipping] = [
        { street: limit_address_field(@customer[:shipping_address][:address1]),
          supplement: limit_address_field(@customer[:shipping_address][:address2]),
          zip: @customer[:shipping_address][:zip],
          city: @customer[:shipping_address][:city],
          countryCode: @customer[:shipping_address][:country_code] }
      ]
    end

    def safe_get_name(customer_name)
      if customer_name.present? && customer_name != 'null'
        customer_name
      else
        '-'
      end
    end

    # limit_address_field method moved to StringSanitizer concern

    def allow_tax_free_invoices?
      (@tax_type != 'gross' && @tax_type != 'net') || @tax_type == 'intraCommunitySupply'
    end
  end
end
