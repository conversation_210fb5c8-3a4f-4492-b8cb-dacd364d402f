# frozen_string_literal: true

module Lexoffice
  # Sends Emails via the lexoffice API
  class MailSenderService
    def initialize(order, sync_info)
      @sync_info = sync_info
      @order = order
      @shop = sync_info.shop
      @settings = @shop.shop_setting
      # duplicate DB access could be improved by giving the shop and settings as params.
      # lets see if that's an issue. Is probably cached anyway
    end

    def call
      return if @sync_info.invoice_mail_sent

      set_error_context
      perform_request
      @sync_info.set_mail_sent
    end

    private

    def set_error_context
      Rails.error.set_context(user_id: @shop.shopify_domain)
    end

    def perform_request
      @shop.refresh_token_if_expired
      invoice = @sync_info.lexoffice_doc_type.new(@shop.lexoffice_token)
      invoice.id = @sync_info.target_id
      @shop.refresh_token_if_expired
      invoice.send_mail(@order, @settings)
    end
  end
end
