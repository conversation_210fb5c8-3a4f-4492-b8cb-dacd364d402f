# frozen_string_literal: true

require 'countries'
module Lexoffice
  # lexoffice tax types service. Takes the order and the shop and returns the appropriate tax type
  class TaxTypesService < ApplicationService
    def initialize(shop, order)
      @shop = shop
      @order = order
      @country_code = country_check
      @country_code_iso = ISO3166::Country.new(@country_code)
      @deliverable = @order.at_least_one_delivery?
    end

    def call
      tax_type
    end

    private

    # This function handles the conditions to set the TaxType for the order; gross, net, thirdPartyCountryDelivery,
    # thirdPartyCountryService, vatfree
    # rubocop:disable all
    def tax_type
      return 'vatfree' if vat_free?

      case
      when @order.pos_order? || @order.no_shipping_information?
        @order.taxes_included ? 'gross' : 'net'
      when valid_intra_community_supply?
        'intraCommunitySupply'
      when country_eea? && settings_vat? && positive_tax_rate?
        @order.taxes_included ? 'gross' : 'net'
      when country_eea? && (settings_vat? || settings_oss?) && zero_tax_rate?
        determine_third_party_country_tax_type
      when country_eea? && settings_oss? && !zero_tax_rate?
        raise StandardError, 'false_eea_tax_settings'
      when switzerland_without_tax? || non_eu_outside_eea_without_tax? || reunion_island
        determine_third_party_country_tax_type
      when valid_eu_country?
        @order.taxes_included ? 'gross' : 'net'
      else
        raise StandardError, 'Invalid tax settings'
      end
    end
    # rubocop:enable all

    # If order is to a company and has shipping address, its thirdPartyCountryDelivery, when no shipping address,
    # its thirdPartyCountryService, if no company its thirdPartyCountryDelivery
    def determine_third_party_country_tax_type
      if company_customer?
        if @deliverable
          'thirdPartyCountryDelivery'
        else
          'thirdPartyCountryService'
        end
      else
        'thirdPartyCountryDelivery'
      end
    end

    def valid_intra_community_supply?
      @order.tax_exempt? &&
        company_customer? &&
        valid_eu_country? &&
        @order.vat_number.present? &&
        @country_code != 'DE'
    end

    # Check if country is Reunion Islands and tax rate is 0
    def reunion_island
      @country_code == 'RE'
    end

    def north_ireland
      return false if address_to_check.blank?

      address_to_check[:province_code] == 'NIR'
    end

    # Condition checks if the country is Switzerland and the tax rate is 0
    def switzerland_without_tax?
      @country_code == 'CH' && zero_tax_rate?
    end

    # Condition checks if the country is not in the EU, not in the EEA, and the tax rate is 0
    def non_eu_outside_eea_without_tax?
      @country_code_iso.present? && !@country_code_iso.in_eu? && !north_ireland && !country_eea? && zero_tax_rate?
    end

    # Checks if the country is in the EU and not Switzerland
    def valid_eu_country?
      (@country_code_iso.present? && @country_code_iso.in_eu?) || !@country_code == 'CH' || north_ireland
    end

    def country_check
      return if address_to_check.blank?

      address_to_check[:country_code]
    end

    def company_customer?
      address_to_check[:company].present?
    end

    def vat_free?
      @shop.lexoffice_small_business || @shop.lexoffice_tax_type == 'vatfree'
    end

    def address_to_check
      @address_to_check ||= [@order&.shipping_address, @order&.billing_address].find(&:present?)&.symbolize_keys
    end

    # Condition checks if the country is in the EEA
    def country_eea?
      %w[IS LI NO].include?(@country_code)
    end

    # Condition checks that the Lexoffice settings are set to origin
    def settings_vat?
      @shop.distance_sales_principle == 'ORIGIN'
    end

    # Condition checks that the Lexoffice settings are set to destination
    def settings_oss?
      @shop.distance_sales_principle == 'DESTINATION'
    end

    # Find the first tax line in an order and check if the tax rate is 0.19 or 0.07
    def positive_tax_rate?
      line_item = @order.line_items.first.deep_symbolize_keys
      return false if line_item[:tax_lines].blank?

      # Disabled Rubocop for --> rubocop: Avoid (in)equality comparisons of floats as they are unreliable
      (line_item[:tax_lines]&.first[:rate] == 0.19 || line_item[:tax_lines]&.first[:rate] == 0.07) # rubocop:disable all
    end

    # Find the first tax line in an order and check if the tax rate is 0
    def zero_tax_rate?
      return true if @order.tax_lines.blank?

      line_item = @order.line_items.first.deep_symbolize_keys
      return true if line_item[:tax_lines].blank?

      # Disabled Rubocop for --> rubocop: Avoid (in)equality comparisons of floats as they are unreliable
      line_item[:tax_lines].first[:rate] == 0.0 # rubocop:disable all
    end
  end
end
