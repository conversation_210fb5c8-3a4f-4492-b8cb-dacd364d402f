# frozen_string_literal: true

class TransferLogFetcherService < ApplicationService
  attr_reader :shop_id, :search, :filter, :page, :order_id

  def initialize(shop_id, search: nil, filter: nil, page: 1, order_id: nil)
    @shop_id = shop_id
    @search = search
    @filter = filter
    @page = page
    @order_id = order_id
  end

  def call
    if order_id.present?
      fetch_record
    else
      [fetch_records, total_count]
    end
  end

  private

  def total_count
    result = nil
    time = Benchmark.measure do
      result = execute_count_query
    end
    Rails.logger.info("TRANSFERLOG - Time taken to get total count: #{time.real} seconds")
    result
  end

  def execute_count_query
    count_sql = build_count_sql
    ActiveRecord::Base.connection.exec_query(
      count_sql,
      'SQL',
      [shop_id]
    ).first['count']
  end

  def build_count_sql
    if filter == 'Error'
      build_error_count_sql
    else
      build_standard_count_sql
    end
  end

  def build_error_count_sql
    <<-SQL.squish
        SELECT COUNT(DISTINCT order_id)
        FROM error_logs
        WHERE shop_id = $1
        AND order_id IS NOT NULL
        AND error_info_external IS NOT NULL
        #{search_condition_for_error_logs}
    SQL
  end

  def build_standard_count_sql
    <<-SQL.squish
      SELECT COUNT(DISTINCT shopify_order_id)
      FROM sync_infos
      WHERE shop_id = $1
      #{search_condition}
      #{filter_condition}
    SQL
  end

  def search_condition
    return '' if search.blank?

    "AND shopify_order_name ILIKE #{ActiveRecord::Base.connection.quote("%#{search}%")}
OR CAST(shopify_order_id AS TEXT) ILIKE #{ActiveRecord::Base.connection.quote("%#{search}%")}"
  end

  def search_condition_for_error_logs
    return '' if search.blank?

    "AND shopify_name ILIKE #{ActiveRecord::Base.connection.quote("%#{search}%")}"
  end

  def filter_condition
    case filter
    when 'Skipped'
      "AND last_action = 'Excluded'"
    when 'Refund', 'Invoice', 'Transaction'
      "AND target_type = #{ActiveRecord::Base.connection.quote(filter)}"
    else
      ''
    end
  end

  def fetch_order_ids
    sql = build_order_ids_sql(filter, page)
    ActiveRecord::Base.connection.exec_query(sql, 'SQL', [shop_id]).pluck('order_id').compact_blank
  end

  def build_order_ids_sql(filter, page)
    if filter == 'Error'
      build_error_order_ids_sql(page)
    else
      build_standard_order_ids_sql(page)
    end
  end

  def build_error_order_ids_sql(page)
    <<-SQL.squish
        SELECT DISTINCT order_id
        FROM error_logs
        WHERE shop_id = $1
        AND order_id IS NOT NULL
        AND error_info_external IS NOT NULL
        #{search_condition_for_error_logs}
      ORDER BY order_id DESC
      LIMIT 30 OFFSET #{(page.to_i - 1) * 30}
    SQL
  end

  def build_standard_order_ids_sql(page)
    <<-SQL.squish
      SELECT DISTINCT shopify_order_id as order_id
      FROM sync_infos
      WHERE shop_id = $1
      #{search_condition}
      #{filter_condition}
      ORDER BY shopify_order_id DESC
      LIMIT 30 OFFSET #{(page.to_i - 1) * 30}
    SQL
  end

  def fetch_sync_infos(order_ids)
    # Filter out nil and empty values
    filtered_order_ids = order_ids.compact_blank
    return {} if filtered_order_ids.empty?

    sql = build_sync_infos_sql
    result = ActiveRecord::Base.connection.exec_query(sql, 'SQL', [shop_id, "{#{filtered_order_ids.join(',')}}"])
    result.index_by { |row| row['shopify_order_id'] }
  end

  def build_sync_infos_sql
    <<-SQL.squish
      SELECT
        shopify_order_id,
        shopify_order_name,
        ARRAY_AGG(id) as sync_info_ids,
        ARRAY_AGG(target_id) as target_ids,
        ARRAY_AGG(target_type) as target_types,
        ARRAY_AGG(extra_infos) as extra_infos,
        ARRAY_AGG(last_action) as last_action,
        MIN(shopify_created_at) as shopify_created_at,
        MIN(created_at) as created_at,
        shop_id
      FROM sync_infos
      WHERE shop_id = $1
      AND shopify_order_id = ANY($2)
      GROUP BY shopify_order_id, shopify_order_name, shop_id
      ORDER BY shopify_order_id DESC
    SQL
  end

  def fetch_error_logs(order_ids)
    # Filter out nil and empty values
    filtered_order_ids = order_ids.compact_blank
    return {} if filtered_order_ids.empty?

    sql = build_error_logs_sql
    result = ActiveRecord::Base.connection.exec_query(sql, 'SQL', [shop_id, "{#{filtered_order_ids.join(',')}}"])
    result.index_by { |row| row['order_id'] }
  end

  def build_error_logs_sql
    <<-SQL.squish
      SELECT
        order_id,
        shopify_name,
        ARRAY_AGG(error_info_internal) as error_info_internal,
        ARRAY_AGG(error_info_external) as error_messages,
        bool_or(error_info_external IS NOT NULL) as has_error_message,
        ARRAY_AGG(error_logs.id) as error_ids,
        ARRAY_AGG(shopify_type) as type,
        ARRAY_AGG(et.helpscout_article_id) as error_helpscout_id,
        MIN(error_logs.created_at) as created_at
      FROM error_logs
      LEFT JOIN error_types et ON et.id = error_logs.error_type_id
      WHERE shop_id = $1
      AND order_id = ANY($2)
      GROUP BY order_id, shopify_name
    SQL
  end

  def fetch_record
    order_ids = [order_id]
    combine_results(
      fetch_sync_infos(order_ids),
      fetch_error_logs(order_ids)
    )
  end

  def fetch_records
    results = nil
    time = Benchmark.measure do
      order_ids = fetch_order_ids
      results = combine_results(
        fetch_sync_infos(order_ids),
        fetch_error_logs(order_ids)
      )
    end
    Rails.logger.info("TRANSFERLOG - Time taken to get data: #{time.real} seconds")
    results
  end

  def combine_results(sync_infos, error_logs)
    all_order_ids = (sync_infos.keys + error_logs.keys).uniq
    # Sort the order IDs in descending order to maintain the ordering
    all_order_ids = all_order_ids.sort.reverse

    all_order_ids.map do |order_id|
      build_combined_result(order_id, sync_infos[order_id], error_logs[order_id])
    end
  end

  def build_combined_result(order_id, sync_info, error_data)
    sync_info = sync_info&.symbolize_keys || {}
    error_data = error_data&.symbolize_keys || {}

    {
      shopify_order_id: order_id,
      shopify_order_names: sync_info[:shopify_order_name] || error_data[:shopify_name],
      sync_info_ids: transform_to_array(sync_info[:sync_info_ids]),
      target_ids: transform_to_array(sync_info[:target_ids]),
      target_types: transform_to_array(sync_info[:target_types]),
      extra_infos: process_extra_infos(sync_info[:extra_infos]),
      last_action: transform_to_array(sync_info[:last_action]),
      error_messages: transform_to_array(error_data[:error_messages]),
      error_info_internal: error_data[:error_info_internal],
      has_error_message: error_data[:has_error_message] || false,
      error_ids: transform_to_array(error_data[:error_ids]),
      type: transform_to_array(error_data[:type]),
      error_helpscout_id: transform_to_array(error_data[:error_helpscout_id]),
      shopify_created_at: earliest_date(sync_info, error_data),
      shop_id: sync_info[:shop_id] || shop_id
    }
  end

  def process_extra_infos(extra_infos)
    transform_to_array(extra_infos)&.map { |info| YAML.load(info) }
  end

  def earliest_date(sync_info, error_data)
    [
      sync_info[:shopify_created_at],
      sync_info[:created_at],
      error_data[:created_at]
    ].compact.min
  end

  def transform_to_array(string)
    return [] if string.nil?
    return string if string.is_a?(Array)

    string.gsub(/[{}"]/, '').split(',')
  end
end
