# frozen_string_literal: true

class FindOrCreateShopBySessionService
  attr_reader :shop_session

  def initialize(shop_session)
    @shop_session = shop_session
  end

  def call
    shop_exists? ? find_shop : create_shop
  end

  private

  def shop_exists?
    Shop.exists?(shopify_domain: shop_session.shop)
  end

  def find_shop
    FindOrCreateSettingsService.new(shop_session).call
  end

  def create_shop
    CreateShopWithSettingsService.new(shop_session).call
  end
end
