# frozen_string_literal: true

class CreateExcludedInfoService
  def initialize(entity, shop, target_type, transaction_id = nil, refund_id = nil)
    @entity = entity
    @shop = shop
    @target_type = target_type
    @transaction_id = transaction_id
    @refund_id = refund_id
    @shopify_id = define_entity_id
  end

  def call
    create_sync_info
  end

  private

  def create_sync_info
    SyncInfo.create(shop_id: @shop.id,
                    shopify_id: @shopify_id,
                    shopify_order_id: @entity.id,
                    shopify_order_name: @entity.name,
                    target_type: @target_type,
                    last_action: 'Excluded',
                    extra_infos: @target_type == 'Transaction' ? { amount: 'Excluded/0' } : nil)
  end

  def define_entity_id
    case @target_type
    when 'Transaction'
      @transaction_id
    when 'Refund'
      @refund_id
    else
      @entity.id
    end
  end
end
