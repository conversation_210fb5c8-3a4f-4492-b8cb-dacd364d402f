# frozen_string_literal: true

class JobIntervalCalculator
  attr_reader :items_count, :interval, :job_base_time, :job_start_time

  def initialize(items_count)
    @items_count = items_count
    @interval = ENV.fetch('IMPORT_JOB_INTERVAL', 4)
    @job_start_time = Time.zone.now
    # allow some time for the queueing job to finish before starting to process
    @job_base_time = job_start_time + ((items_count / 9) + 1).seconds
  end

  def calculate(index)
    calculate_interval(index)
  end

  def calculate_interval(index)
    job_base_time + (index * interval.to_i).seconds
  end
end
