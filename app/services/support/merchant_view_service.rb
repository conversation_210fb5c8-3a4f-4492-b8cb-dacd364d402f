# frozen_string_literal: true

module Support
  class MerchantViewService < ApplicationService
    def initialize(shop, user, request)
      @shop = shop
      @user = user
      @request = request
    end

    def call
      audit_view
      fetch_data
    end

    private

    def audit_view
      AuditLog.audit!(
        :view_merchant,
        user: @user,
        request: @request,
        payload: { shop: @shop.shopify_domain }
      )
    end

    def fetch_data
      shop_info = [] << @shop
      shop_settings = @shop.shop_setting
      transaction_settings = @shop.transaction_setting
      audits = (shop_settings.audits + transaction_settings.audits).sort_by(&:created_at).reverse

      shopify_data = fetch_shopify_data
      billing_data = fetch_billing_data

      {
        shop_info:,
        shop_settings:,
        shopify_shop_info: shopify_data[:shop],
        app_recurring_charges: shopify_data[:recurring_charges],
        app_import_charges: shopify_data[:import_charges],
        current_plan: billing_data[:current_plan],
        audits:,
        import_plan: billing_data[:import_plan],
        old_imports: billing_data[:old_imports],
        available_plans: fetch_available_plans
      }
    end

    def fetch_shopify_data
      installation_data = @shop.app_installation
      {
        shop: @shop,
        recurring_charges: installation_data.activeSubscriptions.first,
        import_charges: installation_data.oneTimePurchases.edges.first.node
      }
    end

    def fetch_billing_data
      billing_data = {
        current_plan: @shop.billing_plan,
        old_imports: JobStatus.where(shop_id: @shop.id).order(created_at: :desc)
      }
      billing_data[:import_plan] = ShopifyBilling::BillingPlan.find_by(short_name: "import") if @shop.import_unlocked?

      billing_data
    end

    def fetch_available_plans
      ShopifyBilling::SelectAvailableBillingPlansService.call(
        shop: @shop,
        coupon_code: @request.params[:coupon_code]
      )
    end
  end
end
