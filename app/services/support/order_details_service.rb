# frozen_string_literal: true

module Support
  class OrderDetailsService < ApplicationService
    def initialize(shop, order_id)
      @shop = shop
      @order_id = order_id.to_s
    end

    def call
      sync_infos = fetch_sync_infos
      shopify_data = fetch_shopify_data
      errors = fetch_errors

      {
        order: shopify_data[:order],
        transactions: shopify_data[:transactions],
        tender_transaction: shopify_data[:tender_transaction],
        sync_infos:,
        errors:
      }
    end

    private

    def fetch_sync_infos
      SyncInfo.where(
        shop_id: @shop.id,
        shopify_order_id: @order_id
      )
    end

    def fetch_errors
      ErrorLog.where(
        shop_id: @shop.id,
        order_id: @order_id
      )
    end

    def fetch_shopify_data
      data = {}
      @shop.with_shopify_session do
        data[:order] = ShopifyAPI::Order.find(id: @order_id)
        data[:transactions] = data[:order].transactions
        data[:tender_transaction] = fetch_tender_transaction(data[:order])
      end
      data
    end

    def format_order(order_data)
      {
        id: order_data.id.split("/").last,
        name: order_data.name,
        financial_status: order_data.displayFinancialStatus,
        fulfillment_status: order_data.displayFulfillmentStatus,
        processed_at: order_data.processedAt,
        total_price: order_data.totalPriceSet.shopMoney.amount,
        currency: order_data.totalPriceSet.shopMoney.currencyCode
      }
    end

    def fetch_tender_transaction(order)
      transaction = ShopifyAPI::TenderTransaction.all(
        processed_at_min: (order.processed_at.to_date - 1.day).strftime("%Y-%m-%d"),
        processed_at_max: (order.processed_at.to_date + 1.day).strftime("%Y-%m-%d"),
        order: "processed_at ASC",
        limit: 250
      ).find { |t| t.order_id.to_s == order.id.to_s }

      transaction || {}
    end

    def format_transactions(transactions)
      transactions&.map do |transaction|
        {
          id: transaction.id.split("/").last,
          status: transaction.status,
          kind: transaction.kind,
          gateway: transaction.gateway,
          amount: transaction.amountSet.shopMoney.amount,
          currency: transaction.amountSet.shopMoney.currencyCode,
          processed_at: transaction.processedAt
        }
      end
    end

    def format_tender_transaction(transaction)
      return {} if transaction.nil?

      {
        id: transaction.id,
        amount: transaction.amount,
        currency: transaction.currency,
        payment_method: transaction.payment_method,
        processed_at: transaction.processed_at
      }
    end
  end
end
