# frozen_string_literal: true

module Support
  class StartImportService < ApplicationService
    class ImportError < StandardError; end

    def initialize(shop, start_date, end_date, import_options, locale)
      @shop = shop
      @start_date = start_date.to_date.next_day
      @end_date = end_date.to_date.next_day
      @import_options = import_options
      @locale = locale
    end

    def call
      validate_import_conditions!

      job_id = SyncAllJob.perform_async(
        @shop.id,
        @start_date,
        @end_date,
        @import_options,
        @locale
      )

      { job_id: }
    end

    private

    def validate_import_conditions!
      raise ImportError, I18n.t("import.message.plan_not_active") unless @shop.plan_active?

      raise ImportError, I18n.t("import.message.already_running") if @shop.has_running_import?

      raise ImportError, I18n.t("import.message.nothing_to_import") unless any_import_selected?
    end

    def any_import_selected?
      @import_options[:invoices] ||
        @import_options[:refunds] ||
        @import_options[:transactions]
    end
  end
end
