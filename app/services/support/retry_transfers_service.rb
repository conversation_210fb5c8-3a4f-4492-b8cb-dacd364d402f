# frozen_string_literal: true

module Support
  class RetryTransfersService < ApplicationService
    def initialize(shop, retry_all, single_error_id, order_ids)
      @shop = shop
      @retry_all = retry_all
      @single_error_id = single_error_id
      @order_ids = order_ids
    end

    def call
      transfer_errors = fetch_transfer_errors
      count = transfer_errors.count

      transfer_errors.each(&:retry_job)

      { job_count: count }
    end

    private

    def fetch_transfer_errors
      if @retry_all
        ErrorLog.where(shop_id: @shop.id)
      elsif @single_error_id
        ErrorLog.where(shop_id: @shop.id, id: @single_error_id)
      else
        ErrorLog.where(shop_id: @shop.id, order_id: @order_ids)
      end
    end
  end
end
