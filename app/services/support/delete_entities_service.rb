# frozen_string_literal: true

module Support
  class DeleteEntitiesService < ApplicationService
    VALID_ENTITY_TYPES = %w[SyncInfo Error].freeze

    def initialize(shop, entity_type, entity_ids, delete_all)
      @shop = shop
      @entity_type = entity_type
      @entity_ids = entity_ids
      @delete_all = delete_all
    end

    def call
      raise ArgumentError, "Invalid entity type: #{@entity_type}" unless VALID_ENTITY_TYPES.include?(@entity_type)

      case @entity_type
      when "SyncInfo"
        delete_sync_info
      when "Error"
        delete_error_logs
      end

      { success: true }
    end

    private

    def delete_sync_info
      if @delete_all
        SyncInfo.where(shop_id: @shop.id).destroy_all
      else
        SyncInfo.where(shop_id: @shop.id, shopify_order_id: @entity_ids).destroy_all
      end
    end

    def delete_error_logs
      if @delete_all
        ErrorLog.where(shop_id: @shop.id).destroy_all
      else
        ErrorLog.where(shop_id: @shop.id, order_id: @entity_ids).destroy_all
      end
    end
  end
end
