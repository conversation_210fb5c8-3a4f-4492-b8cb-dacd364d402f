# frozen_string_literal: true

module Support
  class PlanService < ApplicationService
    def initialize(shop, plan_type, plan_id)
      @shop = shop
      @plan_type = plan_type
      @plan_id = plan_id
    end

    def call
      case @plan_type
      when "import"
        activate_import_plan
      when "app"
        BillingServices::ResetBillingPlanService.call(shop: @shop)
      else
        raise ArgumentError, "Invalid plan type: #{@plan_type}"
      end

      { success: true }
    end

    private

    def activate_import_plan
      @shop.update!(import_manually_unlocked_at: Time.zone.now)
    end
  end
end
