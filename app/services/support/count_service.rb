# frozen_string_literal: true

module Support
  class CountService < ApplicationService
    def initialize(shop, start_date, end_date)
      @shop = shop
      @start_date = start_date.to_date.next_day
      @end_date = end_date.to_date.next_day
    end

    def call
      @shop.with_shopify_session do
        {
          orders: count_orders,
          refunds: count_refunds,
          transactions: count_transactions
        }
      end
    end

    private

    def count_orders
      ShopifyAPI::Order.count(
        created_at_min: format_date(@start_date),
        created_at_max: format_date(@end_date),
        status: "any"
      ).body["count"]
    end

    def count_refunds
      refunded = count_orders_by_status("refunded")
      partially_refunded = count_orders_by_status("partially_refunded")
      refunded + partially_refunded
    end

    def count_orders_by_status(status)
      ShopifyAPI::Order.count(
        created_at_min: format_date(@start_date),
        created_at_max: format_date(@end_date),
        financial_status: status,
        status: "any"
      ).body["count"]
    end

    def count_transactions
      Shopify::TenderTransactionsCountService.call(@shop, @start_date, @end_date)
    end

    def format_date(date)
      date.strftime("%FT%T%:z")
    end
  end
end
