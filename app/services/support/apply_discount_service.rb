# frozen_string_literal: true

module Support
  class ApplyDiscountService < ApplicationService
    def initialize(shop, plan_type, discount)
      @shop = shop
      @plan_type = plan_type
      @discount = discount
    end

    def call
      discount_type = determine_discount_type
      @shop.update!(discount_type => @discount)
      { success: true }
    end

    private

    def determine_discount_type
      @plan_type == "import" ? :import_discount_percent : :discount_percent
    end
  end
end
