module SyncHelper
  class InitializeTargetDocService
    attr_reader :shopify_entity, :shop

    def initialize(shopify_entity, shop)
      @shop = shop
      @shopify_entity = shopify_entity
    end

    def call
      case shopify_entity
      when ShopifyAPI::Order
        Lexoffice::Invoice.new(shop.lexoffice_token)
      when ShopifyAPI::Refund
        Lexoffice::CreditNote.new(shop.lexoffice_token)
      end
    end
  end
end
