module SyncHelper
  class HandleShopifyEntityService
    attr_reader :shopify_entity, :rule

    def initialize(shopify_entity, rule, sync_target)
      @shopify_entity = shopify_entity
      @rule = rule
      @sync_target = sync_target
    end

    def call
      case shopify_entity
      when ShopifyAPI::Order
        @sync_target.create_order(shopify_entity, rule)
      when ShopifyAPI::Refund
        @sync_target.handle_refund(shopify_entity)
      end
    end

    private

    attr_reader :sync_target
  end
end
