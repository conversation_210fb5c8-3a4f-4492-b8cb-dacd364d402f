module SyncHelper
  class FindTargetDocService
    attr_reader :target_doc, :sync_info, :shopify_entity, :rule, :sync_helper

    def initialize(target_doc, sync_info, shopify_entity, rule, sync_helper)
      @target_doc = target_doc
      @sync_info = sync_info
      @shopify_entity = shopify_entity
      @rule = rule
      @sync_helper = sync_helper
    end

    def call
      target_doc.find(sync_info.target_id)
    rescue RestClient::NotFound # standard error already rescued in job
      # Failed to find document, delete sync_info
      sync_info.destroy

      # Restart sync process
      sync_helper.sync_entity(shopify_entity, rule)
    end

    private

    attr_reader :sync_helper
  end
end
