# frozen_string_literal: true

module SyncHelper
  # This service checks if an invoice needs to be created and queues it if so.
  class CheckAndQueueInvoiceService < ApplicationService
    attr_reader :shop, :order_id

    def initialize(shop, order_id)
      @shop = shop
      @order_id = order_id
      @settings = shop.shop_setting
    end

    def call
      return unless @settings.invoice_timing_fulfill?

      invoice_info = SyncInfo.find_by(shop:, shopify_id: order_id, target_type: 'Invoice')
      return if invoice_info.present? && invoice_info.target_id.present?

      SyncOrderJob.perform_async(@settings.invoice_timing, order_id, shop.id)
    end
  end
end
