# frozen_string_literal: true

# Find existing finance accounts or call create
class FindOrEnsureFinancialAccountService < ApplicationService
  def initialize(transaction_setting, lexoffice_token, account_types)
    @lexoffice_token = lexoffice_token
    @settings = transaction_setting
    @account_types = account_types
  end

  def call
    @account_types.each do |type|
      financial_account_endpoint = Lexoffice::FinancialAccount.new(@lexoffice_token, type)
      type_id = "#{type}_account_id"
      extra_account_type_id = @settings.extra_accounts_info[type_id.tr(' ', '_').downcase]
      # should the user have the account manually deleted, the transaction won't be synced but save as an error
      next if extra_account_type_id.present? # next if the account id is present in transaction settings

      type_setting = if @settings.has_attribute?(type_id)
                       @settings.send(type_id)
                     else
                       extra_account_type_id
                     end
      # skip, if the account id is present in transaction settings and a financial account exists
      next if type_setting.present? && financial_account_endpoint.find_by_id(type_setting).present? # rubocop:disable Rails/DynamicFindBy

      CreateFinancialAccountService.call(@settings, financial_account_endpoint, type_id)
    end
  end
end
