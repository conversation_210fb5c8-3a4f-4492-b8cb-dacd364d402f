# frozen_string_literal: true

# Create a finance account in lexoffice
class CreateFinancialAccountService < ApplicationService
  def initialize(settings, financial_account_endpoint, account_id_attr)
    @settings = settings
    @financial_account_endpoint = financial_account_endpoint
    @account_id = account_id_attr
  end

  def call
    financial_account = @financial_account_endpoint.create
    if @settings.respond_to?(@account_id) # checks if the account is one of the standard accounts
      @settings.send("#{@account_id}=", financial_account['id'])
    elsif @settings.extra_accounts_info.key?(@account_id.tr(' ', '_').downcase)
      # case of a user defined account without a financialAccountID
      @settings.extra_accounts_info[@account_id.tr(' ', '_').downcase] = financial_account['id']
    else
      @settings.extra_accounts_info[@account_id.tr(' ', '_').downcase] = financial_account['id']
      @settings.extra_accounts_info["enable_#{@account_id.tr(' ', '_').downcase}"] = @settings.enable_other
    end
    @settings.save
  end
end
