# frozen_string_literal: true

# service to check if collective customers are allowed depending on the context
class ContactRequiredService < ApplicationService
  def initialize(order, shop)
    @order = order
    @shop = shop
    @settings = @shop.shop_setting
  end

  def call
    # for orders without customer no contact is required
    return false unless @order&.customer.is_a?(::ShopifyAPI::Customer)

    # if create customer option is active, a contact is always required
    return true if @settings.create_customer

    # always create a contact when order is b2b
    return true if b2b

    # contact creation is only required if
    !shop_is_small_business? && address_to_check.present? && !shipping_and_billing_within_germany?
  end

  private

  def b2b
    address_to_check.present? && address_to_check.symbolize_keys[:company].present? && @order.vat_number.present?
  end

  def shop_is_small_business?
    @shop.lexoffice_small_business
  end

  def address_to_check
    @address_to_check ||= [@order&.shipping_address, @order&.billing_address].find(&:present?) || {}
  end

  def shipping_and_billing_within_germany?
    # for shipping from germany within germany no contact is required
    @shop.country == 'DE' && address_to_check.symbolize_keys[:country_code] == 'DE' && german_billing_address?
  end

  def german_billing_address?
    @order.billing_address.present? && @order.billing_address.symbolize_keys[:country_code] == 'DE'
  end
end
