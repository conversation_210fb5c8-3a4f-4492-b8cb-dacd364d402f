# frozen_string_literal: true

class LiquidCodeService < ApplicationService
  def initialize(shop, errors, text, order_id = nil)
    @current_shop = shop
    @errors = errors
    @text = text
    @liquid_error = false
    @order_id = order_id
  end

  def call
    validator = LiquidValidator::Validator.new(@text)
    if validator.valid?
      template = Liquid::Template.parse(@text)
      @current_shop.with_shopify_session do
        entity = shopify_entity
        if entity.nil?
          @errors += I18n.t('settings.liquid_no_order_error')
        else
          render_liquid(template, entity)
        end
      end
    else
      errors(validator)
    end
    [@preview, @errors, @liquid_error]
  end

  private

  def shopify_entity
    entity = if @order_id
               ShopifyAPI::Order.find(id: @order_id)
             else
               ShopifyAPI::Order.all(limit: 1).first
             end
    entity.order_transactions = entity.transactions if @text.include?('order_transactions')
    entity
  end

  def render_liquid(template, entity)
    @preview = template.render(JSON.parse(entity.to_json))
  end

  def errors(validator)
    validator.errors.each do |error|
      @errors += error
      @liquid_error = true
    end
  end
end
