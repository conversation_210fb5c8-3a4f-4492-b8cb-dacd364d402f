# frozen_string_literal: true

module Shopify
  class TenderTransactionsCountService < ApplicationService
    def initialize(shop, start_date, end_date)
      @shop = shop
      @start_date = start_date
      @end_date = end_date
      @transaction_settings = shop.transaction_setting
      @transactions = []
    end

    def call
      count
    end

    private

    def count
      @shop.with_shopify_session do
        @transactions = ShopifyAPI::TenderTransaction.all(processed_at_min: @start_date,
                                                          processed_at_max: @end_date,
                                                          limit: 250)
        return 251 if ShopifyAPI::TenderTransaction.next_page?
      end
      @transactions.count do |transaction|
        transaction.payment_method != 'paypal'
      end
    end
  end
end
