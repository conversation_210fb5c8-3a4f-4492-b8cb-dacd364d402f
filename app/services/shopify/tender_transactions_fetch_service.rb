# frozen_string_literal: true

module Shopify
  class TenderTransactionsFetchService < ApplicationService
    def initialize(shop, start_date, end_date)
      @shop = shop
      @transaction_settings = shop.transaction_setting
      @start_date = start_date
      @end_date = end_date
      @transactions = []
      @skipped_transactions = 0
      @all_transactions_count = 0
    end

    def call
      collect_transactions
      [filter_transactions, @skipped_transactions]
    end

    private

    def collect_transactions
      @shop.with_shopify_session do
        transactions = ShopifyAPI::TenderTransaction.all(processed_at_min: @start_date,
                                                         processed_at_max: @end_date,
                                                         order: 'processed_at ASC',
                                                         limit: 250)
        @transactions = transactions
        while ShopifyAPI::TenderTransaction.next_page?
          transactions = ShopifyAPI::TenderTransaction.fetch_next_page(processed_at_min: @start_date,
                                                                       processed_at_max: @end_date,
                                                                       limit: 250)
          @transactions.concat(transactions)
        end
      end

      @transactions
    end

    def filter_transactions
      @all_transactions_count = @transactions.count
      @transactions = @transactions.reject { |transaction| should_skip_transaction?(transaction) }
      @skipped_transactions = @all_transactions_count - @transactions.count
      @transactions
    end

    def should_skip_transaction?(transaction)
      document_exists?(transaction) ||
        a_paypal_transaction?(transaction)
    end

    def document_exists?(transaction)
      SyncInfo.doc_exists?(transaction.id, @shop.id, 'Transaction')
    end

    def a_paypal_transaction?(transaction)
      transaction.payment_method == 'paypal'
    end
  end
end
