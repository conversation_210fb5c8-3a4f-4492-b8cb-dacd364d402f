# frozen_string_literal: true

module Shopify
  class SingleTenderTransactionService < ApplicationService
    def initialize(shop, transaction_id)
      @shop = shop
      @transaction_id = transaction_id
    end

    def call
      fetch_transaction
    end

    private

    def fetch_transaction
      @shop.with_shopify_session do
        transaction = GetTenderTransaction.call(query: "id:#{@transaction_id}")
        transaction.amount = transaction.amount.amount.to_d
        transaction
      end
    end
  end
end
