# frozen_string_literal: true

class SetAbsoluteDiscountService
  def initialize(shop, order, invoice, settings)
    @shop = shop
    @order = order
    @invoice = invoice
    @settings = settings
  end

  def call
    if @order.use_fulfillment? && @settings.invoice_timing_fulfill?
      set_discount_for_fulfilled_order
    else
      set_discount_for_live_order
    end
  end

  private

  def set_discount_for_live_order
    order_total_discount = if @order.fulfillment_status == 'fulfilled' &&
                              @order.financial_status == 'partially_refunded'
                             @order.total_discounts.to_d
                           else
                             @order.current_total_discounts.to_d
                           end

    discount_difference = order_total_discount - @order.line_items_total_discount
    return if discount_difference <= 0

    @invoice.set_discount(discount_difference)
  end

  def set_discount_for_fulfilled_order
    fulfillment_discounts = calculate_fulfillment_discounts(@order)
    discount_difference = @order.current_total_discounts.to_d - fulfillment_discounts
    return if discount_difference <= 0

    @invoice.set_discount(discount_difference)
  end

  def calculate_fulfillment_discounts(order)
    total = order.fulfillments.to_a.sum do |fulfillment|
      DiscountCalculator.calculate_line_item_discounts(fulfillment.line_items, order.discount_applications)
    end

    total.round(2)
  end
end
