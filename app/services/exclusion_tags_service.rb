# frozen_string_literal: true

class ExclusionTagsService < ApplicationService
  def initialize(shop, order)
    @shop = shop
    @shops_settings = shop.shop_setting
    @order_tags_array = order.order_tags_array
  end

  def call
    return false unless @shop.feature_enabled?('exclude_orders_by_tag')
    return false if @shops_settings.order_exclusion_tags.blank? || @order_tags_array.blank?

    exclusion_tags_array.each do |tag|
      return true if @order_tags_array.include?(tag)
    end
    false
  end

  private

  def exclusion_tags_array
    @shops_settings.order_exclusion_tags.present? ? @shops_settings.order_exclusion_tags.split(',').map(&:downcase) : []
  end
end
