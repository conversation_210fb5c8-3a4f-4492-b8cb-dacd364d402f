# frozen_string_literal: true

# The service is used to find a sync info with a target id for a refund.
# If the refund is not found, the service will queue a job to create the refund.
#
class FindOrCreatePendingRefundService < ApplicationService
  def initialize(order_id, transaction_amount, shop)
    @order_id = order_id
    @transaction_amount = transaction_amount.to_d
    @shop = shop
    @refund = nil
  end

  def call
    find_refund_and_queue_job
  end

  private

  def find_refund_and_queue_job
    order = @shop.with_shopify_session { ShopifyAPI::Order.find(id: @order_id) }
    find_refund(order)
    return if @refund.nil?

    sync_info = SyncInfo.find_by(shopify_order_id: @order_id, target_type: 'Refund', shopify_id: @refund.id)
    return if sync_info&.target_id.present? || sync_info&.last_action != 'Pending Transaction'

    RefundJob.perform_async('refunds/create', @refund.id, @order_id, @shop.id)
    delete_error
  end

  def delete_error
    ErrorLog.find_by(shopify_id: @refund.id, shop_id: @shop.id,
                     error_info_internal: 'Refund has pending transactions')&.destroy
  end

  def find_refund(order)
    @refund = if currency_mismatch?(order)
                find_refund_by_adjustments(order) || find_refund_by_subtotal(order)
              else
                find_refund_by_transactions(order)
              end
  end

  def currency_mismatch?(order)
    order.refunds.any? { |r| r.transactions.any? { |t| t.currency != order.currency } }
  end

  def find_refund_by_adjustments(order)
    order.refunds.find do |refund|
      refund.order_adjustments&.any? do |adjustment|
        adjustment.deep_symbolize_keys[:amount].to_d.abs == @transaction_amount.abs
      end
    end
  end

  def find_refund_by_subtotal(order)
    order.refunds.find do |refund|
      refund.calculate_subtotal_amount == @transaction_amount.abs
    end
  end

  def find_refund_by_transactions(order)
    order.refunds.find { |r| r.transactions&.any? { |t| t.amount.to_d.abs == @transaction_amount.abs } }
  end
end
