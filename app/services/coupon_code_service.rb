class CouponCodeService

  def initialize(number_of_coupons, free_days)
    @number_of_coupons = number_of_coupons
    @free_days = free_days
  end

  def call
    characters = %w[A B C D E F G H J K L M P Q R T W X Y Z 0 1 2 3 4 5 6 7 8 9]
    code = ''

    @number_of_coupons.to_i.times do
      6.times { code << characters.sample }
      CouponCode.create!({coupon_code: code, free_days: @free_days, redeemed: false})
      code = ''
    end
  end
end