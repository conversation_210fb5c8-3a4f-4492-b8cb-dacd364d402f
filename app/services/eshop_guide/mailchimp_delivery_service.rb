# frozen_string_literal: true

# Eshop Guide extensions module
module EshopGuide
  require 'MailchimpTransactional'
  # A service class encapsulating the mailchimp mail transactional service.
  # This class allows sending emails through mailchimp depending on the given service parameter.
  # Takes a shop, a service type e.g. Uninstall and instructions in case of a warn email.
  class MailchimpDeliveryService < ApplicationService
    def initialize(shop, service, instructions = nil)
      @shop = shop
      @instructions = instructions
      @service = service
      @client = MailchimpTransactional::Client.new(ENV.fetch('MANDRILL_APIKEY', nil))
    end

    def call
      @client.messages.send(message: create_message)
    rescue MailchimpTransactional::ApiError => e
      Rails.logger.error e
    end

    private

    def merge_shop_vars
      vars = [{ name: 'shop_owner', content: @shop['shop_owner'] }]
      return vars if @instructions.nil?

      button_url = "https://#{@shop['shopify_domain']}/admin/apps/#{ENV.fetch('ADMIN_APP_NAME', nil)}"
      vars << { name: 'error_msg', content: @instructions }
      vars << { name: 'button_url',
                content: button_url }
      vars
    end

    def render_template
      @client.templates.render(
        { template_name: define_template,
          template_content: merge_shop_vars,
          merge_vars: merge_shop_vars }
      )
    end

    def create_message
      {
        subject: I18n.t("notifications.mailchimp.#{@service}"),
        from_name: 'Eshop Guide Apps Team',
        text: '',
        to: [{ email: @shop['email'], name: @shop['shop_owner'] }],
        html: render_template['html'],
        from_email: '<EMAIL>'
      }
    end

    def define_template
      temps = {
        new_install: 'lexoffice-welcome-mail',
        uninstall: 'lexoffice-uninstall-mail',
        warn: 'lexoffice-errormail',
        plan_mismatch: 'lexoffice-plan-mismatch',
        plan_activation: 'lexware-install-without-plan-activation'
      }
      temps[@service]
    end
  end
end
