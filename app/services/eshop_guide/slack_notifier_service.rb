# frozen_string_literal: true

# Eshop Guide extensions module
module EshopGuide
  require 'slack-notifier'
  # A service class encapsulating the slack notifying function.
  # This class allows sending slack notifications depending on the given service parameter.
  # Takes a shop, a service type e.g. Uninstall.
  class SlackNotifierService < ApplicationService
    def initialize(shop, service)
      @shop = shop
      @service = service
      @notifier = Slack::Notifier.new '*****************************************************************************',
                                      channel: '#notifications', username: 'New Store'
    end

    def call
      push_notification
    rescue StandardError
      nil
    end

    private

    def push_notification
      @notifier.ping(notification_data, icon_emoji: emoji, username: @shop['name'])
    end

    def notification_data
      "#{I18n.t("notifications.slack.#{@service}")} - SHOP: #{@shop['name']}, EMAIL: #{@shop['email']}, URL: #{@shop['shopify_domain']}"
    end

    def emoji
      emojis = { install: ':lexoffice:', uninstall: ':lexoffice_uninstall:', import: ':moneybag:' }
      emojis[@service]
    end
  end
end
