# frozen_string_literal: true

# Eshop Guide extensions module
module EshopGuide
  # A Service class encapsulating the mailchimp list subscriber service.
  class MailchimpListSubscriberService < ApplicationService
    def initialize(shop)
      @shop = shop
      @gibbon = Gibbon::Request.new(api_key: ENV.fetch('MAILCHIMP_API_KEY', nil))
      @list_id = ENV.fetch('MAILCHIMP_NEWCUSTOMERS_LIST', nil)
      @email = @shop['email']&.downcase
    end

    def call
      return if @email.blank?

      # Split the @shop['shop_owner'] string into first_name and last_name
      first_name, *last_name = @shop['shop_owner'].split
      @gibbon.lists(@list_id).members.create(body: { email_address: @shop['email'],
                                                     status: 'subscribed',
                                                     merge_fields: { FNAME: first_name, LNAME: last_name.join(' ') } })
    rescue Gibbon::MailChimpError => e
      Rails.logger.error e
    end
  end
end
