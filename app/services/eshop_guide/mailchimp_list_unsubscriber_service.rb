# frozen_string_literal: true

# Eshop Guide extensions module
module EshopGuide
  # A Service class encapsulating the mailchimp list subscriber service.
  class MailchimpListUnsubscriberService < ApplicationService
    def initialize(shop)
      @shop = shop
      @gibbon = Gibbon::Request.new(api_key: ENV.fetch('MAILCHIMP_API_KEY', nil))
      @list_id = ENV.fetch('MAILCHIMP_NEWCUSTOMERS_LIST', nil)
      @email = @shop['email']&.downcase
    end

    def call
      return if @email.blank?

      # Mailchimp requires the member ID to be an MD5 hash of the lowercase email address
      member_id = Digest::MD5.hexdigest(@email)
      @gibbon.lists(@list_id).members(member_id).update(body: { status: 'unsubscribed' })
    rescue Gibbon::MailChimpError => e
      Rails.logger.error e
    end
  end
end
