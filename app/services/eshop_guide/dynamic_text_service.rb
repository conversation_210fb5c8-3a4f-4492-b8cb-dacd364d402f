# frozen_string_literal: true

# Eshop Guide extensions module
module EshopGuide
  # A service class for parsing liquid texts and generating variables values from order.
  # This class takes a template and a shopify order and returns the resulting text.
  class DynamicTextService < ApplicationService
    def initialize(template, order)
      @template = template
      @order = order
    end

    def call
      format_text_with_liquid
    end

    private

    def format_text_with_liquid
      # TODO: Check if attributes like "transaction" are used by customers in the liquid templates, because
      # order/refund.to_hash shrinks down the accessible attributes quite a bit
      hash = JSON.parse(@order.to_json)
      @template = Liquid::Template.parse(@template, error_mode: :strict) # Parses and compiles the template
      @template.render(hash)
    end
  end
end
