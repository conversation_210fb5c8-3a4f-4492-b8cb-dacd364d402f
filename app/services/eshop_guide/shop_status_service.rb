# frozen_string_literal: true

module EshopGuide
  # This service is responsible for pulling the shop status and returning it in an aggregated object
  class ShopStatusService < ApplicationService
    def initialize(shop)
      @shop = shop
      @settings = shop.shop_setting
    end

    def call
      {
        id: @shop.id,
        shop_domain: @shop.shopify_domain,
        install_date: @shop.install_date,
        billing: billing_status,
        service: service_status,
        settings: settings_status,
        import: import_status,
        available_plans:,
        orders_tags:
      }
    end

    def billing_status
      {
        plan_active: @shop.plan_active?,
        billing_plan_id: @shop.billing_plan&.id,
        billing_plan_name: @shop.billing_plan&.name,
        billing_plan_price: @shop.billing_plan&.price.to_s,
        import_unlocked: @shop.import_unlocked?,
        remaining_trial_days: @shop.remaining_trial_days,
        trial_ends_on: @shop.trial_ends_on,
        plan_mismatch_since: @shop.plan_mismatch_since,
        features: @shop.billing_plan&.features,
        legacy: @shop.billing_plan&.is_legacy
      }
    end

    def service_status
      if @shop.connected_to_lexoffice?
        @shop.refresh_token_if_expired
        begin
          profile_info = Lexoffice::UpdateLexofficeInfoService.call(@shop)
        rescue RestClient::Unauthorized
          @shop.mark_for_connection_reset
        end
      end

      {
        connected_to_service: @shop.connected_to_lexoffice?,
        connection_info: {
          lexoffice_organization_id: @shop.lexoffice_organization_id,
          lexoffice_organization_name: profile_info&.dig("companyName"),
          lexoffice_tax_type: @shop.lexoffice_tax_type,
          lexoffice_small_business: @shop.lexoffice_small_business,
          lexoffice_connection_id: @shop.lexoffice_connection_id,
          connection_established_at: @shop.connection_established_at
        }
      }
    end

    def settings_status
      {
        invoice_creation_enabled: @settings.create_invoices,
        refund_creation_enabled: @settings.create_refunds,
        tax_settings_confirmed: @settings.confirm_tax_settings
      }
    end

    def import_status
      {
        import_running: @shop.has_running_import?,
        import_job_id: JobStatus.running_for_shop(@shop.id)&.pick(:guid)
      }
    end

    def orders_tags
      @shop.with_shopify_session { GetOrderTags.new.call }
    end

    def available_plans
      # split Billing plans by play_type
      ShopifyBilling::BillingPlan.where("id > 0")
        .group_by(&:plan_type)
        .transform_values do |plans|
        plans.map do |plan|
          {
            id: plan.id,
            name: plan.name,
            price: plan.price.to_s,
            features: I18n.t(plan.features),
            type: plan.plan_type
          }
        end
      end
    end
  end
end
