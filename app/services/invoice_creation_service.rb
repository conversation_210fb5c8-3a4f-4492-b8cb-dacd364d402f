# frozen_string_literal: true

class InvoiceCreationService < EshopGuide::BaseReportingService
  attr_reader :invoice, :finalize, :sync_info, :send_mail, :order, :settings

  alias send_mail? send_mail

  def initialize(invoice, finalize, sync_info, send_mail, order, settings)
    @invoice = invoice
    @finalize = finalize
    @sync_info = sync_info
    @send_mail = send_mail
    @order = order
    @settings = settings
  end

  def call
    invoice_id = nil
    WithLockService.call("invoice_creation_lock_#{@order.id}", context: { user_id: settings.shop.shopify_domain }) do
      sync_info.reload
      unless sync_info.doc_created?
        invoice_id = invoice.create(finalize:)
        sync_info.target_doc_create(invoice_id)
        log_invoice_creation
      end
      invoice_id ||= sync_info.target_id

      invoice.find(invoice_id)
    end
    sync_info.add_doc_properties(invoice)
    invoice
  end

  private

  def log_invoice_creation
    shop = @settings.shop
    # Cache shopify_plan for 4 hours
    shopify_plan = Rails.cache.fetch("shopify_plan:#{shop.id}", expires_in: 4.hours) do
      shop.shopify_plan
    end
    report_event(
      event_name: "invoice_created",
      event_type: "customer_usage",
      customer_myshopify_domain: shop.shopify_domain,
      event_value: @sync_info.extra_infos[:total_price],
      payload: {
        shopify_plan: { display_name: shopify_plan["displayName"] },
        app_plan: shop.billing_plan.name
      },
      timestamp: Time.current,
      external_id: @order.id
    )
  end
end
