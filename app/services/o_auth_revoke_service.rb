# frozen_string_literal: true

class OAuthRevokeService < ApplicationService
  attr_reader :shop, :force

  alias forced? force

  def initialize(shop)
    @shop = shop
  end

  def call
    revoke_connection
  end

  private

  def revoke_connection
    perform_request
  rescue RestClient::BadRequest => e
    Rails.error.report(e, context: { tags: "token_revoke_failed", user_id: shop.shopify_domain })
  ensure
    shop.update(
      lexoffice_token: nil,
      lexoffice_refresh_token: nil,
      lexoffice_token_expires_at: nil
    )
  end

  def perform_request
    RestClient.post "#{ENV.fetch("LEXOFFICE_SITE", nil)}/oauth2/revoke",
      { token: shop.lexoffice_refresh_token },
      { Authorization: basic_auth_header(ENV.fetch("LEXOFFICE_KEY", nil),
        ENV.fetch("LEXOFFICE_SECRET", nil)) }
  end

  def basic_auth_header(client_id, client_secret)
    "Basic #{Base64.encode64("#{client_id}:#{client_secret}").delete("\n")}"
  end
end
