# frozen_string_literal: true

# This job takes care of queuing the mail jobs depending on the mail sending offset in days
# Of the offset is greater thatn 0 the job will be queued for the given offset, else it will be queued immediately
class MailJobQueuingService < ApplicationService
  def initialize(settings, sync_info)
    @mail_offset = settings.invoice_mail_offset_days
    @sync_info = sync_info
  end

  def call
    if @mail_offset.zero?
      SendMailJob.perform_async(@sync_info.id)
    else
      SendMailJob.perform_in(@mail_offset.days, @sync_info.id)
    end
    @sync_info.update(last_action: 'Send Mail Job Queued')
  end
end
