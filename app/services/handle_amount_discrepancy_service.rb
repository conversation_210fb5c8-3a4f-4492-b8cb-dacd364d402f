# frozen_string_literal: true

# service class to handle amount discrepancies
class HandleAmountDiscrepancyService
  def initialize(refund, use_brutto, data, tax_rates)
    @refund = refund
    @use_brutto = use_brutto
    @data = data
    @tax_rates = tax_rates
  end

  def call
    return nil unless currency_is_euro?

    difference = total_transaction_amount - total_line_item_amount
    return nil if difference.zero? || total_transaction_amount.zero?

    line_item(total_transaction_amount.to_d)
  end

  private

  def line_item(difference)
    {
      type: 'custom',
      name: @data[:language] == 'de' ? 'Gutschriftanpassung' : 'Credit note adjustment',
      quantity: 1,
      unitName: 'Stück',
      unitPrice: unit_price(difference),
      discountPercentage: 0
    }
  end

  def unit_price(difference)
    unit_price = {
      currency: 'EUR',
      taxRatePercentage: @tax_rates.nil? ? 0 : amount_discrepancy_tax
    }
    unit_price[gross_or_net] = difference
    unit_price
  end

  def currency_is_euro?
    @refund.transactions.all? { |transaction| transaction.currency == 'EUR' }
  end

  def total_transaction_amount
    total_transaction_amount = @refund.transactions.select { |ta| ta.status == 'success' }.sum { |x| x.amount.to_d }

    return 0 if total_transaction_amount.nil?

    total_transaction_amount
  end

  def total_line_item_amount
    total_line_item_amount = line_items_amount + total_order_adjustments
    total_line_item_amount += line_items_taxes unless @use_brutto
    total_line_item_amount
  end

  def line_items_amount
    total_amount = @refund.refund_line_items.sum { |x| x.symbolize_keys[:subtotal].to_d }
    return 0 if total_amount.nil?

    total_amount
  end

  def line_items_taxes
    total_amount = @refund.refund_line_items.sum { |x| x.symbolize_keys[:total_tax].to_d }
    return 0 if total_amount.nil?

    total_amount
  end

  def total_order_adjustments
    order_adjustments = @refund.order_adjustments.select { |x| x.symbolize_keys[:kind] == 'shipping_refund' }
                               .sum { |x| (x.symbolize_keys[:amount].to_d + x.symbolize_keys[:tax_amount].to_d) }
    order_adjustments_amount = order_adjustments.nil? ? 0 : order_adjustments * -1
    return 0 if order_adjustments_amount.nil?

    order_adjustments_amount
  end

  def gross_or_net
    @data[:taxConditions][:taxType] == 'gross' ? 'grossAmount' : 'netAmount'
  end

  def amount_discrepancy_tax
    tax_rate = @tax_rates.select { |_k, v| v == @tax_rates.values.max }.keys.max
    tax_rate.present? ? tax_rate.round(2) : 0
  end
end
