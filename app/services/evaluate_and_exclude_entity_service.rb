# frozen_string_literal: true

class EvaluateAndExcludeEntityService < ApplicationService
  def initialize(order, shop, target_type = nil, transaction_id = nil, refund_id = nil)
    @order = order
    @shop = shop
    @shop_settings = shop.shop_setting
    @target_type = target_type
    @transaction_id = transaction_id
    @refund_id = refund_id
  end

  def call
    if ExclusionTagsService.call(@shop, @order)
      create_excluded_sync_info
      return
    end

    return if exclude_pos_order? || (exclude_non_fulfilled_order? && @order.cancelled_at.nil?)

    @order
  end

  private

  def exclude_pos_order?
    @order.is_a?(ShopifyAPI::Order) && @shop_settings.excludePOS && @order.source_name == 'pos'
  end

  # When editing an order the SyncOrderJob is triggered. If the order is not fulfilled and timing is on order/fulfilled,
  # exclude the order from syncing. The order will be created when the order is fulfilled on Shopify.
  def exclude_non_fulfilled_order?
    @order.is_a?(ShopifyAPI::Order) &&
      @target_type == 'Invoice' &&
      @shop_settings.invoice_timing_fulfill? &&
      @order.fulfillment_status != 'fulfilled'
  end

  def create_excluded_sync_info
    SyncInfo.find_or_create_by(shop_id: @shop.id,
                               shopify_id: define_entity_id,
                               target_type: @target_type) do |info|
      info.shopify_order_id = @order.id
      info.shopify_order_name = @order.name
      info.last_action = 'Excluded'
      info.extra_infos = @target_type == 'Transaction' ? { amount: 'Excluded/0' } : nil
    end
  end

  def define_entity_id
    case @target_type
    when 'Transaction'
      @transaction_id
    when 'Refund'
      @refund_id
    else
      @order.id
    end
  end
end
