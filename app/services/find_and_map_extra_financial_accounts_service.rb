# frozen_string_literal: true

# Service class to find and map extra financial accounts.
# Extra financial accounts are accounts for user defined payment methods.
# The name of the payment method does not appear in the tender transaction that is why we look for the order.
# The class does also extend the payment settings with the extra accounts pieces of information.
class FindAndMapExtraFinancialAccountsService < ApplicationService
  def initialize(transaction_amount, shop, order)
    @transaction_amount = transaction_amount
    @shop = shop
    @extra_accounts_info = shop.transaction_setting.extra_accounts_info || {}
    @order = order
  end

  def call
    find_and_add_gateway
  end

  private

  def find_and_add_gateway
    @shop.with_shopify_session do
      transaction = order_transactions(@order.id).find do |t|
        t.amount.to_d == @transaction_amount.abs && t.status == 'success' &&
          (t.kind == transaction_kind || (t.kind == 'authorization' && @transaction_amount.positive?))
      end
      WithLockService.call("extra_accounts_lock_#{@shop.id}", context: { user_id: @shop.shopify_domain }) do
        extend_settings(transaction) unless transaction.nil?
      end
      transaction&.gateway
    end
  end

  def transaction_kind
    @transaction_amount.positive? ? 'sale' : 'refund'
  end

  def extend_settings(transaction)
    return if transaction.nil?

    gateway = transaction.gateway.tr(' ', '_').downcase
    return if @extra_accounts_info.key?("enable_#{gateway}") || @extra_accounts_info.key?("#{gateway}_account_id")

    @extra_accounts_info["enable_#{gateway}"] =
      @shop.transaction_setting.enable_other
    @extra_accounts_info["#{gateway}_account_id"] = nil
    # update the extra accounts info
    @shop.transaction_setting.update!(extra_accounts_info: @extra_accounts_info)
  end

  def order_transactions(order_id)
    ShopifyAPI::Transaction.all(order_id:)
  end
end
