# frozen_string_literal: true

class CreateCancelledInfoService
  def initialize(order, shop)
    @order = order
    @shop = shop
  end

  def call
    create_sync_info
  end

  private

  def create_sync_info
    SyncInfo.create(shop_id: @shop.id,
                    shopify_id: @order.id,
                    shopify_order_id: @order.id,
                    shopify_order_name: @order.name,
                    target_type: 'Invoice',
                    last_action: 'Cancelled')
  end
end
