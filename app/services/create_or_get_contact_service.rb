# frozen_string_literal: true

# The purpose of this service is to create or get a contacts from Lexoffice secured by a semaphore to avoid simultaneous
# creation of the same contact.
class CreateOrGetContactService
  attr_reader :order, :shop

  def initialize(order, shop, tax_type = nil)
    @order = order
    @shop = shop
    @tax_type = tax_type
  end

  def call
    WithLockService.call("contact_lock_#{@shop.id}", context: { user_id: @shop.shopify_domain }) do
      create_or_get_contact if ContactRequiredService.call(@order, @shop)
    end
  end

  private

  def create_or_get_contact
    contact_endpoint.find_or_create(Lexoffice::ContactDataService
                                      .new(order, shop.shop_setting.use_shipping_address_for_invoices).call, @tax_type)
  end

  def contact_endpoint
    shop.refresh_token_if_expired
    @contact_endpoint ||= Lexoffice::Contact.new(shop.reload.lexoffice_token)
  end
end
