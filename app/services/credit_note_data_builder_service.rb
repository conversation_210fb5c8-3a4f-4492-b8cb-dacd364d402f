# frozen_string_literal: true

class CreditNoteDataBuilderService
  def call(shop, refund, is_import_job, order, shop_settings, contact_id = nil, contact = nil, use_brutto, tax_type)
    context = CreditNoteDataBuilderInteractor.call(shop:, refund:, is_import_job:, order:, shop_settings:,
                                                   contact_id:, contact:, use_brutto:, tax_type:)
    return unless context.success?

    [context.credit_note, context.sync_info]
  end
end
