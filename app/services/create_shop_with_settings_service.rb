# frozen_string_literal: true

class CreateShopWithSettingsService < EshopGuide::BaseReportingService
  attr_reader :shop_session

  def initialize(shop_session)
    @shop_session = shop_session
  end

  def call
    create_shop_with_settings
  end

  private

  def create_shop_with_settings
    shop = Shop.create(shopify_domain: @shop_session.shop, shopify_token: @shop_session.access_token)

    # create settings
    ShopSetting.create(shop:)
    TransactionSetting.create(shop:)

    # set shop attributes from remote shop
    update_shop_info(shop)

    shop.send_install_notifications
    shop.add_to_mailchimp_subscribers

    report_install(shop)
    shop
  end

  def update_shop_info(shop)
    shop.with_shopify_session do
      @remote_shop = ShopifyAPI::Shop.current
      shop.update(email: @remote_shop&.email,
                  name: @remote_shop&.name,
                  country: @remote_shop&.country,
                  shop_owner: @remote_shop&.shop_owner,
                  credit_note_scope: true)
    end
  end

  def report_install(shop)
    report_event(
      event_name: 'app_installed',
      event_type: CentralEventLogger::EventTypes::USER_ACQUISITION,
      customer_myshopify_domain: shop.shopify_domain,
      event_value: @remote_shop&.plan_name,
      customer_info: {
        name: shop.name,
        owner: shop.shop_owner,
        email: shop.email
      },
      payload: {
        shop_name: shop.name,
        shop_owner: shop.shop_owner,
        country: shop.country,
        shopify_plan: @remote_shop&.plan_name,
        shopify_plan_display_name: @remote_shop&.plan_display_name
      },
      timestamp: Time.zone.now,
      external_id: "install:#{shop.shopify_domain}"
    )
  end
end
