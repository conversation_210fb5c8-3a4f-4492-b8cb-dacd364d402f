# frozen_string_literal: true

# Service for fetching transfer logs with support-specific features like date filtering
class SupportTransferLogFetcherService < TransferLogFetcherService
  attr_reader :interval

  # rubocop:disable Metrics/ParameterLists
  def initialize(shop_id, search: nil, filter: nil, interval: nil, page: 1, order_id: nil)
    @interval = interval
    super(shop_id, search:, filter:, page:, order_id:)
  end
  # rubocop:enable Metrics/ParameterLists

  private

  # Builds SQL conditions for filtering sync_infos by date range
  def date_condition_for_sync_infos
    return "" if interval.blank?

    start_date = interval[:start_date]
    end_date = interval[:end_date]

    conditions = []
    if start_date.present? && end_date.present?
      conditions << "(sync_infos.shopify_created_at BETWEEN #{ActiveRecord::Base.connection.quote(start_date)}
AND #{ActiveRecord::Base.connection.quote(end_date)})"
      conditions << "(sync_infos.created_at BETWEEN #{ActiveRecord::Base.connection.quote(start_date)}
AND #{ActiveRecord::Base.connection.quote(end_date)})"
    end

    conditions.empty? ? "" : "AND (#{conditions.join(" OR ")})"
  end

  # Builds SQL conditions for filtering error_logs by date range
  def date_condition_for_error_logs
    return "" if interval.blank?

    start_date = interval[:start_date]
    end_date = interval[:end_date]

    conditions = []
    conditions << "error_logs.created_at >= #{ActiveRecord::Base.connection.quote(start_date)}" if start_date.present?
    conditions << "error_logs.created_at <= #{ActiveRecord::Base.connection.quote(end_date)}" if end_date.present?

    conditions.empty? ? "" : "AND #{conditions.join(" AND ")}"
  end

  # Builds SQL query to count orders with errors
  def build_error_count_sql
    <<-SQL.squish
      SELECT COUNT(DISTINCT order_id)
      FROM error_logs
      WHERE error_logs.shop_id = $1
      #{search_condition_for_error_logs}
      #{date_condition_for_error_logs}
      AND error_logs.order_id IS NOT NULL
      AND error_logs.error_info_external IS NOT NULL
    SQL
  end

  # Builds SQL query to count standard orders based on filter conditions
  def build_standard_count_sql
    <<-SQL.squish
      SELECT COUNT(DISTINCT sync_infos.shopify_order_id)
      FROM sync_infos
      WHERE sync_infos.shop_id = $1
      #{search_condition}
      #{filter_condition}
      #{date_condition_for_sync_infos}
    SQL
  end

  # Builds SQL conditions for filtering orders based on various criteria
  def filter_condition
    case filter
    when "Skipped"
      "AND sync_infos.last_action = 'Excluded'"
    when "Refund", "Invoice"
      "AND sync_infos.target_type = #{ActiveRecord::Base.connection.quote(filter)}"
    when "Transaction"
      "AND sync_infos.target_type = #{ActiveRecord::Base.connection.quote("Transaction")}"
    when "Transaction Assignment Hint"
      "AND sync_infos.target_type = #{ActiveRecord::Base.connection.quote("Transaction Assignment Hint")}"
    when "Import"
      "AND sync_infos.import"
    else
      ""
    end
  end

  # Builds SQL query to fetch order IDs for error cases with pagination
  def build_error_order_ids_sql(page)
    <<-SQL.squish
      SELECT DISTINCT error_logs.order_id
      FROM error_logs
      WHERE error_logs.shop_id = $1
      #{search_condition_for_error_logs}
      #{date_condition_for_error_logs}
      AND error_logs.order_id IS NOT NULL
      AND error_logs.error_info_external IS NOT NULL
      ORDER BY error_logs.order_id DESC
      LIMIT 30 OFFSET #{(page.to_i - 1) * 30}
    SQL
  end

  # Builds SQL query to fetch order IDs for standard cases with pagination
  def build_standard_order_ids_sql(page)
    <<-SQL.squish
      SELECT DISTINCT sync_infos.shopify_order_id as order_id
      FROM sync_infos
      WHERE sync_infos.shop_id = $1
      #{search_condition}
      #{filter_condition}
      #{date_condition_for_sync_infos}
      ORDER BY sync_infos.shopify_order_id DESC
      LIMIT 30 OFFSET #{(page.to_i - 1) * 30}
    SQL
  end

  # Fetches sync infos for given order IDs in two steps for better performance
  def fetch_sync_infos(order_ids)
    return {} if order_ids.empty?

    initial_sync_infos = fetch_initial_sync_infos(order_ids)
    related_order_ids = initial_sync_infos.pluck("shopify_order_id").uniq
    fetch_all_related_sync_infos(related_order_ids)
  end

  # Fetches initial sync infos for given order IDs
  def fetch_initial_sync_infos(order_ids)
    sql = build_initial_sync_infos_sql(order_ids)
    ActiveRecord::Base.connection.exec_query(sql, "SQL", [shop_id])
  end

  # Builds SQL query for initial sync infos fetch
  def build_initial_sync_infos_sql(order_ids)
    quoted_order_ids = order_ids.map { |id| ActiveRecord::Base.connection.quote(id) }.join(",")
    <<-SQL.squish
      SELECT
        sync_infos.shopify_order_id,
        sync_infos.shopify_order_name,
        sync_infos.id,
        sync_infos.target_id,
        sync_infos.target_type,
        sync_infos.extra_infos,
        sync_infos.last_action,
        sync_infos.shopify_created_at,
        sync_infos.created_at,
        sync_infos.shop_id
      FROM sync_infos
      WHERE sync_infos.shop_id = $1
      AND sync_infos.shopify_order_id IN (#{quoted_order_ids})
      #{filter_condition}
      #{date_condition_for_sync_infos}
    SQL
  end

  # Fetches all related sync infos for given order IDs
  def fetch_all_related_sync_infos(order_ids)
    return [] if order_ids.empty?

    sql = build_all_related_sync_infos_sql(order_ids)
    result = ActiveRecord::Base.connection.exec_query(sql, "SQL", [shop_id])
    result.index_by { |row| row["shopify_order_id"] }
  end

  # Builds SQL query to fetch all related sync infos with aggregated information
  def build_all_related_sync_infos_sql(order_ids)
    quoted_order_ids = order_ids.map { |id| ActiveRecord::Base.connection.quote(id) }.join(",")
    <<-SQL.squish
      SELECT
        sync_infos.shopify_order_id,
        sync_infos.shopify_order_name,
        ARRAY_AGG(sync_infos.id) as sync_info_ids,
        ARRAY_AGG(sync_infos.target_id) as target_ids,
        ARRAY_AGG(sync_infos.target_type) as target_types,
        ARRAY_AGG(sync_infos.extra_infos) as extra_infos,
        ARRAY_AGG(sync_infos.last_action) as last_action,
        MIN(sync_infos.shopify_created_at) as shopify_created_at,
        MIN(sync_infos.created_at) as created_at,
        sync_infos.shop_id
      FROM sync_infos
      WHERE sync_infos.shop_id = $1
      AND sync_infos.shopify_order_id IN (#{quoted_order_ids})
      #{date_condition_for_sync_infos}
      GROUP BY sync_infos.shopify_order_id, sync_infos.shopify_order_name, sync_infos.shop_id
      ORDER BY sync_infos.shopify_order_id DESC
    SQL
  end

  def fetch_error_logs(order_ids)
    # Filter out nil and empty values
    filtered_order_ids = order_ids.compact_blank
    return {} if filtered_order_ids.empty?

    sql = build_error_logs_sql(filtered_order_ids)
    result = ActiveRecord::Base.connection.exec_query(sql, "SQL", [shop_id])
    result.index_by { |row| row["order_id"] }
  end

  # Builds SQL query to fetch error logs with aggregated information
  def build_error_logs_sql(order_ids)
    quoted_order_ids = order_ids.map { |id| ActiveRecord::Base.connection.quote(id) }.join(",")
    <<-SQL.squish
      SELECT
        error_logs.order_id,
        error_logs.shopify_name,
        ARRAY_AGG(error_logs.error_info_internal) as error_info_internal,
        ARRAY_AGG(error_logs.error_info_external) as error_messages,
        bool_or(error_logs.error_info_external IS NOT NULL) as has_error_message,
        ARRAY_AGG(error_logs.id) as error_ids,
        ARRAY_AGG(error_logs.shopify_type) as type,
        ARRAY_AGG(et.helpscout_article_id) as error_helpscout_id,
        MIN(error_logs.created_at) as created_at
      FROM error_logs
      LEFT JOIN error_types et ON et.id = error_logs.error_type_id
      WHERE error_logs.shop_id = $1
      AND error_logs.order_id IN (#{quoted_order_ids})
      #{date_condition_for_error_logs}
      GROUP BY error_logs.order_id, error_logs.shopify_name
    SQL
  end
end
