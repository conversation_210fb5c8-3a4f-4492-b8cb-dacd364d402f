<%# navigation styled for Bootstrap 3.0 %>
<nav class="navbar navbar-inverse">
  <div class="container">
    <div class="navbar-header">
      <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
        <span class="sr-only">Toggle navigation</span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
      </button>
      <%= image_tag("lexoffice_logo.png", :class => "logo") %>
      <%= link_to 'Eshop Guide lexoffice App Support', '/', class: 'navbar-brand' %>
    </div>
    <%# on the right side displays a logout link, only if the user is logged in with devise%>
    <div class="navbar-collapse collapse">
      <ul class="nav navbar-nav navbar-right">
        <%= render 'layouts/navigation_links' %>
        <% if support_user_signed_in? %>
        <li><%= link_to 'Logout', destroy_support_user_session_path, method: :delete %></li>
        <% end %>
      </ul>
    </div>
  </div>
</nav>
