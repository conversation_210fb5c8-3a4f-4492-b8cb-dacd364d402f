<!DOCTYPE html>
<html>
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lexoffice Integration</title>
    <%=favicon_link_tag 'lexoffice.ico'%>

    <%= vite_client_tag %>
    <%= vite_react_refresh_tag %>
    <%= vite_javascript_tag 'admin.jsx' %>
  </head>
  <body>
    <div id="app"><!--index.jsx injects App.jsx here--></div>
    <script type="text/javascript">!function(e,t,n){function a(){var e=t.getElementsByTagName("script")[0],n=t.createElement("script");n.type="text/javascript",n.async=!0,n.src="https://beacon-v2.helpscout.net",e.parentNode.insertBefore(n,e)}if(e.Beacon=n=function(t,n,a){e.Beacon.readyQueue.push({method:t,options:n,data:a})},n.readyQueue=[],"complete"===t.readyState)return a();e.attachEvent?e.attachEvent("onload",a):e.addEventListener("load",a,!1)}(window,document,window.Beacon||function(){});</script>
    </script><script type="text/javascript">window.Beacon('init', '<%= ENV.fetch('BEACON_MESSAGE_ID', 'c1a1cebc-5429-49b7-829d-4e8a42ae1291') %>')</script>
  </body>
</html>
