
<% if @entity_infos.present? %>
  <table class="table-sync-infos table table-hover table-bordered">
    <% @entity_infos.each do  |entity| %>
      <% if entity.target_id.nil? || entity.target_id == '' %>
        <% @import_needed = true %>
      <% end %>
      <tbody class="shopify-shop-infos-table">
        <tr>
          <th class="lexoffice_orange_light"> <a href="<%="#{ENV['APP_HOME']}/support/view_order?shop=#{@current_shop.shopify_domain}&order_id=#{entity.shopify_id}" %>" target="_blank" >Entity ID</a></th>
          <td><%= entity.shopify_id %></td>
        </tr>
        <tr>
          <th class="lexoffice_orange_light">Target ID</th>
          <td><%= entity.target_id %></td>
        </tr>
        <tr>
          <th class="lexoffice_orange_light">Target Type</th>
          <td><%= entity.target_type %></td>
        </tr>
        <tr>
          <th class="lexoffice_orange_light">Last Action</th>
          <td><%= entity.last_action %></td>
        </tr>
        <tr>
          <th class="lexoffice_orange_light">Shopify order ID</th>
          <td><%= entity.shopify_order_id %></td>
        </tr>
      </tbody>
    <% end %>
  </table>
<% end %>
<% if @import_needed %>
  <%= bootstrap_form_tag url: '/support/retry_one', remote:true do |f| %>
    <%= f.hidden_field :shop_id, value: @current_shop.id  %>
    <%= f.hidden_field :shopify_id, value: params['shopify_id']  %>
    <%= f.primary "Auftrag bzw. Gutschrift nachträglich importieren", class:"btn btn-dark" %>
  <% end %>
<% end %>