<%= table_for @shop_sync_infos, :table_html => { :class => "table-sync-infos table table-hover table-bordered sync-info-table" },
              :sortable => false,
              :data_row_html => {
                :class => lambda {cycle('lexoffice_orange_light')},
                :id => lambda { |sync_info| "syncinfo-#{sync_info.id}" }} do |table| %>
  <% table.column :shopify_order_name, :header => "Shopify Auftrag", :link_url => lambda { |info| "#{ENV["APP_HOME"]}/support/view_order?shop=#{@current_shop.shopify_domain}&order_id=#{info.shopify_id}" }, :link_html => { "target" => "_blank" } %>
  <% table.column :target_type, :header => "Target Type", :link_html => {"target" => "_blank" } %>
  <% table.column :last_action,  :header => "Letzte Aktion"   %>
  <% table.column :invoice_mail_sent,  :header => "Rechnungsmail gesendet"   %>
  <% table.column :created_at,
                  :header => "Ersteltt am",
                  :sortable => false ,
                  :formatter => [:strftime, "%d.%m.%y %H:%M"]%>
  <% table.column :import, :header => "Import" %>
  <% table.define :footer do %>
    <tfoot>
    <tr>
      <td colspan="<%= table.columns.length %>">
        <div class="pull-right">
          <% if @shop_sync_infos.length > 0 %>
            <%= custom_will_paginate @shop_sync_infos,remote: true, params: {:controller => 'support', :action => 'show_sync_infos',
                                                                      :sync_from_date => @sync_info_start, :sync_to_date => @sync_info_end,
                                                                      :shop => @current_shop.shopify_domain} %>
          <% end %>
        </div>
      </td>
    </tr>
    </tfoot>
  <% end %>
<% end %>
<% if @shop_sync_infos.length.positive? %>
  <div class="delete_sync_infos_wrapper">
    <%= render partial: 'show_modal' %>
  </div>
<% end %>
<script type="text/javascript" charset="utf-8">
    $('.helpscout-sidebar-link').each(function( index ) {
        $(this).attr('data-beacon-article-sidebar',$(this).attr('href').substring(1));
    });
    $('.pagination[remote=true] a').on('click', function() {
        window.history.pushState(null, 'hi', $(this).attr("href"));
        Rails.handleRemote($(this));
        return false;
    });
    $(".table-sync-infos a").attr("target", "_blank");
</script>
