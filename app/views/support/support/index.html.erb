<%= bootstrap_form_tag  url: '/support/merchant/', method: :get, id: 'shop_name_form', remote:false do |form| %>

  <%= form.text_field_tag :shop, params[:shop], placeholder: 'xyz.myshopify.com', class:'shop-name' %>
  <%= form.submit_tag "Open Shop" %>

  <ul class="autocomplete-list"></ul>
<% end %>
<script>
    $("#shop_name_form").submit(function(event) {
        event.preventDefault();
        window.location = "/support/merchant/"+$("#shop").val().replace('.myshopify.com', '');
    });

    $(document).ready(function() {
        const input = $('.shop-name');
        const autocompleteList = $('.autocomplete-list');

        // Debounce the autocomplete lookup
        const debouncedLookup = debounce(function() {
            var query = input.val();
            if (query.length < 3) {
                return;  // Don't perform the lookup if the input is too short
            }

            $.ajax({
                url: '/support/shops',
                data: { input: query },
                success: function(results) {
                    // Clear the existing autocomplete suggestions
                    autocompleteList.empty();

                    // Add a new list item for each suggestion
                    results.forEach(function(result) {
                        const listItem = $('<li>').text(result).data('id', result.id);
                        autocompleteList.append(listItem);
                    });
                }
            });
        }, 300);  // Lookup will be debounced for 300 milliseconds

        input.on('keyup', debouncedLookup);

        // Set up event handlers for selecting a suggestion
        autocompleteList.on('click', 'li', function() {
            input.val($(this).text());
            // Clear the autocomplete list
            autocompleteList.empty();
        });
        autocompleteList.on('keydown', 'li', function(e) {
            if (e.which === 13) {  // Enter key
                input.val($(this).text());
                // Clear the autocomplete list
                autocompleteList.empty();
            }
        });
    });

    // Debounce function (from underscore.js)
    function debounce(func, wait, immediate) {
        let timeout;
        return function() {
            const context = this, args = arguments;
            const later = function () {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

</script>