<div class="container p-t-30">
  <div class="row">
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
      <div class="well well-lg">
        <div>
          <div class="flex-header">
            <div>
              <h4>Ausgewählter Shop: <span class="badge"><%= @current_shop.shopify_domain %></span></h4>
            </div>
            <div>
              <%= form_with url: "/support/activate_import", remote:true do |form| %>
                <%= form.hidden_field :shop_id, value: @current_shop.id  %>
                <%= form.submit "import freischalten", class: "btn btn-dark btn-raised" %>
              <% end %>
            </div>
          </div>

            <div>
            <%= render partial: 'shop_info_table' %>
          </div>

          <h4>Einstellungen</h4>
          <div class="table-overflow">
            <%= render partial: 'shop_settings_table', locals:{ shop_settings:@shop_settings, transaction_settings:@current_shop.transaction_setting } %>
          </div>
        </div>

        <h4>Weitere Shop Infos:</h4>
        <div class="table-overflow">
          <%= render partial: 'shopify_shop_info', locals: { shopify_shop_info: @shopify_shop_info } %>
        </div>
        <div class="entity_actions_wrapper">
          <%= bootstrap_form_tag url: '/support/get_entity', remote:true do |f| %>
            <%= f.hidden_field :shop_id, value: @current_shop.id  %>
            <%= f.text_field :shopify_id , label: "Auftrag ID" %>
            <%= f.primary "Auftragsinfos holen", class:"btn btn-dark" %>
          <% end %>
        </div>
        <div class="table-overflow">
          <div class="order_infos_wrapper">
            <%= render partial: 'order_infos_table' %>
          </div>
        </div>
        <div>
          <div class="errors-log-wrapper">
            <div><h4>Übertragungsfehler</h4></div>
            <div class="errors_section_buttons">
              <%= form_with url: "/support/reset", remote:true do |form| %>
                <%= form.hidden_field :shop_id, value: @current_shop.id  %>
                <div class="input-daterange input-group" id="datepicker">
                  <input type="text" class="input-sm form-control" name="errors_from_date" id="sync_from_date" value="<%= @errors_start %>"/>
                  <span class="input-group-addon">bis</span>
                  <input type="text" class="input-sm form-control" name="errors_to_date" id="sync_to_date" value="<%= @errors_end %>"/>
                </div>
                <h6>*Falls kein Zeitraum ausgewählt ist, werden alle Einträge aus der Tabelle gelöscht</h6>
                <%= form.submit "Einträge löschen", class: "btn btn-dark btn-raised" %>
              <% end %>
              <%= form_with url: "/support/retry_all", remote:true do |form| %>
                <%= form.hidden_field :shop_id, value: @current_shop.id  %>
                <%= form.submit "Alle neu übertragen", class: "btn btn-dark btn-raised" %>
              <% end %>
            </div>
          </div>

          <div></div>
          <div class="table-overflow">
            <div class="error_log_wrapper">
              <%= render partial: 'error_log_table' %>
            </div>
          </div>
        </div>
        <div>
          <h4>SyncInfos</h4>
          <div class="sync_infos">
            <%= bootstrap_form_tag url: "/support/show_sync_infos", remote:true do |form| %>
              <%= form.hidden_field :shop, value: @current_shop.shopify_domain  %>
              <div class="input-daterange input-group" id="datepicker">
                <input type="text" class="input-sm form-control" name="sync_from_date" id="sync_from_date" value="<%= @sync_info_start %>"/>
                <span class="input-group-addon">bis</span>
                <input type="text" class="input-sm form-control" name="sync_to_date" id="sync_to_date" value="<%= @sync_info_end %>"/>
              </div>
              <%= form.submit "SyncInfos Laden", class: "btn btn-dark btn-raised" %>
            <% end %>
          </div>
          <div class="sync_infos_wrapper">
            <%= render partial: 'syncinfo_table' %>
          </div>
        </div>


        <h2>Auftragsimport</h2>
        <div class="support_import_wrapper">
            <%= bootstrap_form_tag url: '/support/count_orders', remote:true do |f| %>
            <%= f.hidden_field :shop_id, value: @current_shop.id  %>
              <h4>Importzeitraum auswählen:</h4>
              <div class="input-daterange input-group" id="datepicker">
                <input type="text" class="input-sm form-control" name="from_date" id="from_date" value="<%= @import_start %>"/>
                <span class="input-group-addon">bis</span>
                <input type="text" class="input-sm form-control" name="to_date" id="from_date" value="<%= @import_end %>"/>
              </div>
              <div class="doc-type-checkboxes">
                <input type="checkbox" class="input-sm form-control" name="orders" id="doc_type_orders"/>
                <label for="doc_type_orders">Orders</label>
                <input type="checkbox" class="input-sm form-control" name="refunds" id="doc_type_refunds"/>
                <label for="doc_type_refunds">Refunds</label>
                <input type="checkbox" class="input-sm form-control" name="transactions" id="doc_type_transactions"/>
                <label for="doc_type_transactions">Transactions</label>
              </div>
              <%= f.primary "Aufträge laden", class:"btn btn-dark" %>
            <% end %>

          <div class="col-sm-8">
            <div id="start-import">

            </div>
            <div class="well well-status">
              <div class="row">
                <div class="col-sm-9">
            <span class="stage">
               Starte Import...
            </span>
                </div>
                <div class="col-sm-3">
                  <span class="status pull-right"><i class="fa fa-spin fa-cog"></i> ...läuft</span>
                </div>
              </div>
              <div class="progress progress-striped active">
                <div class="progress-bar progress-bar-blue" role="progressbar" aria-valuenow="0"
                     aria-valuemin="0" aria-valuemax="100" style="width:0%">
                </div>
              </div>
            </div>
          </div>
        </div>

        <div>
          <div class="imports_history">
            <h4>Imports Historie</h4>
            <%= bootstrap_form_tag url: "/support/show_imports_history", remote:true do |form| %>
              <%= form.hidden_field :shop, value: @current_shop.shopify_domain  %>
              <%= form.submit "Imports History Laden", class: "btn btn-dark btn-raised" %>
            <% end %>
          </div>
          <div class="imports_history_wrapper">
            <%= render partial: 'imports_history_infos_table' %>
          </div>
        </div>

        <h4>Aktive Charges:</h4>
        <% unless @app_recurring_charge.nil? %>
          <div class="recurring_charge_hint">
            <table class="table-sync-infos table table-hover table-bordered table-margined">
              <tr>
                <th class="lexoffice_orange_light">Shop Plan</th>
                <td><%= @app_recurring_charge.name %></td>
              </tr>
              <tr>
                <th class="lexoffice_orange_light">Status</th>
                <td><%= @app_recurring_charge.status %></td>
              </tr>
              <tr>
                <th class="lexoffice_orange_light">Preis</th>
                <td><%= @app_recurring_charge.price %> $</td>
              </tr>
            </table>
          <% unless @app_import_charge.nil? %>
            <table class="table-sync-infos table table-hover table-bordered table-margined">
              <tr>
                <th class="lexoffice_orange_light">Shop Import Plan</th>
                <td><%= @app_import_charge.name %></td>
              </tr>
              <tr>
                <th class="lexoffice_orange_light">Preis</th>
                <td><%= @app_import_charge.price %>$</td>
              </tr>
            </table>
          <% end %>
        </div>
        <% end %>

        <h4>Rabattsystem:</h4>

        <div>
          <div class="discount_system_wrapper">
            <%= bootstrap_form_tag url: '/support/set_plan_discount', remote:true do |f| %>
              <%= f.hidden_field :shop_id, value: @current_shop.id  %>
              <%= f.text_field :plan_rabatt , label: "Monatlicher Plan Rabatt (Aktuell: #{@current_shop.discount_percent.to_s}%)" %>
              <%= f.primary "Rabatt zuweisen", class:"btn btn-dark" %>
            <% end %>

            <%= bootstrap_form_tag url: '/support/set_import_discount', remote:true do |f| %>
              <%= f.hidden_field :shop_id, value: @current_shop.id  %>
              <%= f.text_field :import_rabatt , label: "Import Plan Rabatt (Aktuell: #{@current_shop.import_discount_percent.to_s}%)" %>
              <%= f.primary "Rabatt zuweisen", class:"btn btn-dark" %>
            <% end %>
          </div>
          <h4>Plan zurücksetzen:</h4>
          <div class="reset_plan_wrapper">
            <%= bootstrap_form_tag url: '/support/reset_plan', remote:true do |f| %>
              <%= f.hidden_field :shop_id, value: @current_shop.id  %>
              <%= f.primary "Plan zurücksetzen", class:"btn btn-dark" %>
            <% end %>
          </div>
          <% unless @current_shop.billing_plan.present? %>
            <div class="recurring_charge_hint"><h5>Shop hat keine aktive AppCharge!</h5></div>
          <% end %>
          <% if @app_recurring_charge.nil? %>
            <div class="recurring_charge_hint"><h5>Shop hat keinen Plan! (Keine Recurring App Charge)</h5></div>
          <% end %>

          <% if @app_import_charge.nil? %>
            <div class="recurring_charge_hint"><h5>Shop hat keinen import Plan! (Keine App Charge)</h5></div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
    $('.input-daterange').datepicker({
        format: "dd.mm.yyyy",
        language: "de"
    });
    $(".error_log_wrapper").html("<%= escape_javascript(render partial: 'error_log_table' ) %>");
    $(".imports_history_wrapper").html("<%= escape_javascript(render partial: 'imports_history_infos_table' ) %>");
    $(".sync_infos_wrapper").html("<%= escape_javascript(render partial: 'syncinfo_table' ) %>");

</script>