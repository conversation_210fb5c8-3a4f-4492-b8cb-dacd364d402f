<%= table_for @shop_errors, :table_html => { :class => "table-sync-infos table table-hover table-bordered" },
              :sortable => false,
              :data_row_html => {
                  :class => lambda {cycle('lexoffice_orange_light')},
                  :id => lambda { |error| "error-#{error.id}" }} do |table| %>
  <% table.column :shopify_name, :header => "Shopify Auftrag", :link_url => lambda { |info| "#{ENV["APP_HOME"]}/support/view_order?shop=#{@current_shop.shopify_domain}&order_id=#{info.shopify_id}" }, :link_html => { "target" => "_blank" } %>
  <% table.column :order_id, :header => "Auftrag ID", :link_url => lambda { |info|"#{ENV["APP_HOME"]}/support/view_order?shop=#{@current_shop.shopify_domain}&order_id=#{info.order_id}" }, :link_html => { "target" => "_blank" } %>
  <% table.column :shopify_type, :header => "Shopify Type", :link_html => {"target" => "_blank" } %>
  <% table.column :error_info_external,  :header => "Fehlerinformation",:link_url =>  lambda { |error| "##{error.helpscout_article_id}" } , :link_html => {"class" => "helpscout-sidebar-link"}    %>
  <% table.column :error_info_internal,  :header => "Interner Fehler",:link_url =>  lambda { |error| "##{error.helpscout_article_id}" } , :link_html => {"class" => "helpscout-sidebar-link" }    %>
  <% table.column :updated_at,
                  :header => "Zuletzt aufgetreten",
                  :sortable => false ,
                  :formatter => [:strftime, "%d.%m.%y %H:%M"]%>
  <% table.column :data => "Erneut übertragen", :header => "Erneut übertragen",:link_method => :post, :link_html => {"data-remote" => true, :class => "link-requeue btn btn-basic btn-sm"}, :link_url => lambda { |error| "#{ENV["APP_HOME"]}/support/requeue_entity?id=#{error.id}&shop=#{@current_shop.shopify_domain}"} %>
  <% table.define :footer do %>
    <tfoot>
    <tr>
      <td colspan="<%= table.columns.length %>">
        <div class="pull-right">
          <%= custom_will_paginate @shop_errors,remote: true, params: {:controller => 'support', :action => 'show_errors_table'} %>
        </div>
      </td>
    </tr>
    </tfoot>
  <% end %>
<% end %>
<script type="text/javascript" charset="utf-8">
    $('.helpscout-sidebar-link').each(function( index ) {
        $(this).attr('data-beacon-article-sidebar',$(this).attr('href').substring(1));
    });
      $('.pagination[remote=true] a').on('click', function() {
        window.history.pushState(null, 'hi', $(this).attr("href"));
        Rails.handleRemote($(this));
        return false;
    });
      $(".table-sync-infos a").attr("target", "_blank");
    <% if @shop_errors.length == 0  && ErrorLog.for_shop(@current_shop.id).length > 0%>
    const urlParts = window.location.href.split('/');
    $.ajax({url: "/support/show_errors_table?shop=" + urlParts[urlParts.length - 1]});
    <% end %>
</script>