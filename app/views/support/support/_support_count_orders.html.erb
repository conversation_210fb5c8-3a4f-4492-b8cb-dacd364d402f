<div>

<% if order_count && order_import_time %>
  <div>
    <h1> <span class="ordercount badge badge-secondary"><%= order_count %> Aufträge</span></h1>
    <span class="badge badge-secondary">Geschätzte Dauer: <%=Time.at(order_import_time).utc.strftime("%H:%M:%S") %></span>
  </div>
<% end %>

<% if refund_count && refund_import_time %>
  <div>
    <h1> <span class="ordercount badge badge-secondary"><%= refund_count %> Gutschriften</span></h1>
    <span class="badge badge-secondary">Geschätzte Dauer: <%=Time.at(refund_import_time).utc.strftime("%H:%M:%S") %></span>
  </div>
<% end %>

<% if transaction_count && transaction_import_time %>
  <div>
    <h1> <span class="ordercount badge badge-secondary"><%= transaction_count %> Transaktionen</span></h1>
    <span class="badge badge-secondary">Geschätzte Dauer: <%=Time.at(transaction_import_time).utc.strftime("%H:%M:%S") %></span>
  </div>
<% end %>


<%= bootstrap_form_tag url: '/support/start_import', remote:true,data: {type: 'script'} do |f| %>
  <%= f.hidden_field :shop_id, value: current_shop.id  %>
  <%= f.primary "Import starten", class:"btn btn-dark btn-sync-all"%>
<% end %>

</div>