<%= table_for snippets, :table_html => { :class => "table-sync-infos table table-hover table-bordered" },
:sortable => false,
:data_row_html => {
:class => lambda {cycle('lexoffice_orange_light')},
:id => lambda { |snippet| "snippet-#{snippet.id}" }} do |table| %>
  <% table.column :name, :header => "Snippet Name" %>
  <% table.column :code, :header => "Liquid Code" %>
  <% table.column :description, :header => "Beschreibung" %>
  <% table.column :data => 'Vorschau', :header => 'Vorschau',:link_method => :get, :link_html => {"data-remote" => false, :class => "btn btn-basic btn-sm"}, :link_url => lambda { |snippet| "snippets/#{snippet.id}"} %>
  :sortable => false ,
:formatter => [:strftime, "%d.%m.%y %H:%M"]%>
    <% table.define :footer do %>
    <tfoot>
    <tr>
        <td colspan="<%= table.columns.length %>">
            <div class="pull-right">
                <%= custom_will_paginate snippets ,remote: true, params: {:controller => 'snippets', :action => 'show_snippets_table'} %>
            </div>
        </td>
    </tr>
    </tfoot>
    <% end %>
<% end %>
<script type="text/javascript" charset="utf-8">
    import Rails from "@rails/ujs";

    $('.pagination[remote=true] a').on('click', function() {
        window.history.pushState(null, 'hi', $(this).attr("href"));
        Rails.handleRemote($(this));
        return false;
    });
</script>
