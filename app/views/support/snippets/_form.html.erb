<div class="container p-t-30">
    <div class="well well-lg">
      <div class="row">
        <%= form_with(model: snippet, url: support_snippet_path(snippet)) do |form| %>
          <% if snippet&.errors&.any? %>
            <div style="color: red">
              <h2><%= pluralize(snippet.errors.count, "error") %> prohibited this snippet from being saved:</h2>

              <ul>
                <% snippet.errors.each do |error| %>
                  <li><%= error.full_message %></li>
                <% end %>
              </ul>
            </div>
          <% end %>

          <div class="col-md-6">
            <div class="panel panel-dark">
              <div class="panel-heading">
                <h3 class="panel-title">Snippet erstellen</h3>
              </div>

              <div class="panel-body">
                <div>
                  <%= form.label :name, style: "display: block" %>
                  <%= form.text_field :name, label:'Snippet Name', class:"form-control" %>
                </div>

                <div>
                  <%= form.label :code, style: "display: block" %>
                  <%= form.text_area :code, rows: "3", id: "snippet_code", class:"form-control" %>
                </div>

                <div>
                  <%= form.label :description, style: "display: block" %>
                  <%= form.text_area :description, rows: "3", class:"form-control" %>
                </div>

                <div>
                  <%= form.submit "Speichern", class: "btn btn-dark btn-raised", style: "float:left;" %>
                </div>
              </div>

            </div>
          </div>
        <% end %>

        <div class="col-md-6">
          <div class="panel panel-dark">
            <div class="panel-heading">
              <h3 class="panel-title">Vorschau</h3>
            </div>

            <div class="panel-body">
              <%= bootstrap_form_tag url: support_snippets_preview_url, remote:true do |form| %>
                <%= form.hidden_field :code, id: "hidden_code"  %>
                <div>
                  <%= form.text_field :shop_domain, label: "Shop Domain", class:"form-control" %>
                </div>

                <div>
                  <%= form.text_field :order_id, label: "Auftrag Id", class:"form-control" %>
                </div>

                <div class="liquid-preview">

                </div>
                <div>
                  <%= form.submit "Vorschau", class: "btn btn-dark btn-raised", style: "float:left;" %>
                </div>
              <% end %>
            </div>
          </div>
        </div>
    </div>

    <div style="display: flex">
      <%= button_to "Snippet löschen", support_snippet_path(snippet), method: :delete, class: "btn btn-danger" if snippet.present? %>
      <%= link_to "Zurück zu Snippets", support_snippets_path, class: "btn btn-dark btn-raised" %>
    </div>
  </div>
</div>

<script>
  $(document).ready(function () {
    $('#hidden_code').val($('#snippet_code').val());
    $('#snippet_code').on('keyup', function () {
      $('#hidden_code').val($(this).val());
    });
  });
</script>
