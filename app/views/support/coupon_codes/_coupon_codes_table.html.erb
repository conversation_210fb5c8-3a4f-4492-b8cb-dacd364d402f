<%= table_for @coupon_codes, :table_html => { :class => "table-coupon-codes table table-hover table-bordered" },
              :sortable => false,
              :data_row_html => {
                :class => lambda {cycle('lexoffice_orange_light')},
                :id => lambda { |error| "error-#{error.id}" }} do |table| %>
  <% table.column :coupon_code, :header => "Coupon Code" %>
  <% table.column :redeemed,  :header => "Eingelöst" %>
  <% table.column :shop_id,  :header => "Shop Id" %>
  <% table.column :free_days,  :header => "Konstenlose Phase" %>
  <% table.column :type,  :header => "Gutschein Typ" %>
  <% table.column :validity,  :header => "Gültigkeit" %>
  <% table.column :redeem_counter,  :header => "Übrige Einlösungen" %>
  <% table.column :created_at, :header => "Erstellt am",
                  :formatter => [:strftime, "%d.%m.%y %H:%M"]%>
  <% table.column :updated_at,
                  :header => "Zu letzt geupdated am",
                  :sortable => false ,
                  :formatter => [:strftime, "%d.%m.%y %H:%M"]%>
  <% table.column :data => "Gutschein Löschen", :header => "Gutschein Löschen",:link_method => :delete, :link_html => {"data-remote" => true, :class => "link-requeue btn btn-basic btn-sm"}, :link_url => lambda { |coupon| "#{ENV["APP_HOME"]}/support/coupon_codes/#{coupon.id}"} %>
  <% table.define :footer do %>
    <tfoot>
    <tr>
      <td colspan="<%= table.columns.length %>">
        <div class="pull-right">
          <% if @coupon_codes.length > 0 %>
            <%= custom_will_paginate @coupon_codes,remote: true, params: {:controller => 'coupon_codes', :action => 'show_table'} %>
          <% end %>
        </div>
      </td>
    </tr>
    </tfoot>
  <% end %>
<% end %>
<script>
    $('.pagination[remote=true] a').on('click', function() {
        window.history.pushState(null, 'hi', $(this).attr("href"));
        Rails.handleRemote($(this));
        return false;
    });
    $(".table-coupon-codes a").attr("target", "_blank");
</script>