<div class="row">
  <div class="col-md-6">
    <h4>One Time Coupon Code</h4>
    <div class="coupon-code-generator">
      <%= bootstrap_form_tag url: support_coupon_codes_url, remote:true do |form| %>
        <%= form.hidden_field :type, value: 'one_time' %>
        <%= form.text_field :number_of_coupons, label: '<PERSON><PERSON>hl' %>
        <%= form.text_field :free_days, label: 'Kostenlose Zeit (Tage)' %>
        <%= form.submit "Gutschein(e) erstellen", class: "btn btn-dark btn-raised" %>
      <% end %>
    </div>
  </div>
  <div class="col-md-6">
    <h4>Mehrfach Coupon Code</h4>
    <div class="coupon-code-generator">
      <%= bootstrap_form_tag url: support_coupon_codes_url, remote:true do |form| %>
        <%= form.hidden_field :type, value: 'campaign' %>
        <%= form.text_field :number_of_coupons, label: '<PERSON><PERSON><PERSON>' %>
        <%= form.text_field :free_days, label: '<PERSON>sten<PERSON> Zeit (Tage)' %>
        <%= form.text_field :redeem_counter, label: '<PERSON><PERSON><PERSON> an Einlösungen' %>
        <div class="input-daterange input-group" id="datepicker">
          <label>Gültigkeit</label>
          <input type="text" class="input-sm form-control" name="validity" id="validity" value=""/>
        </div>
        <label>Individualisierbares Element</label>
        <input type="text" class="input-sm form-control" name="individual_prefix" id="individual_prefix" value="EshopGuide"/>
        <%= form.submit "Mehrfach Gutschein(e) erstellen", class: "btn btn-dark btn-raised" %>
      <% end %>
    </div>
  </div>
</div>


<script>
    $('.input-daterange').datepicker({
        format: "dd.mm.yyyy",
        language: "de"
    });
</script>