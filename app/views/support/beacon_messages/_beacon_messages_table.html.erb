<%= table_for @beacon_messages, :table_html => { :class => "table-sync-infos table table-hover table-bordered" },
              :sortable => false,
              :data_row_html => {
                :class => lambda {cycle('lexoffice_orange_light')}} do |table| %>
  <% table.column :name, header: 'Message Name',:link_url => lambda { |message| "https://secure.helpscout.net/messages/#{message.help_scout_id}" }, :link_html => {"target" => "_blank" } %>
  <% table.column :help_scout_id,  header: 'Help Scout Id'    %>
  <% table.column :new_feature_message,  header: 'Neues Feature'     %>
  <% table.column :domain_name, header: 'Bereich' %>
  <% table.column :data => "Message löschen", :header => "Message löschen",:link_method => :delete, :link_html => {"data-remote" => true, :class => "link-requeue btn btn-basic btn-sm"}, :link_url => lambda { |message| support_beacon_message_url(message)} %>
  <% table.define :footer do %>
    <tfoot>
    <tr>
      <td colspan="<%= table.columns.length %>">

      </td>
    </tr>
    </tfoot>
  <% end %>
<% end %>