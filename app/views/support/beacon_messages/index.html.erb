<div class="container p-t-30">
  <div class="row">
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
      <div class="well well-lg">

        <div class="beacon_system_wrapper">
          <h4>Beacon Message hinzufügen</h4>
          <%= bootstrap_form_for :beacon_message, remote:true do |f| %>
            <div class="row">
              <div class="col-xs-12 col-sm-4">
                <%= f.text_field :name, label: 'Beacon Message Name' %>
              </div>
              <div class="col-xs-12 col-sm-4">
                <%= f.text_field :help_scout_id, label: 'Help Scout ID' %>
              </div>
              <div class="col-xs-12 col-sm-4">
                <%= f.select :domain_name, @beacon_areas.map { |area| [area[:name], area[:id]] }, label: 'Bereich' %>
              </div>
            </div>
            <%= f.check_box :new_feature_message, label: 'Neues Feature?' %>
            <%= f.primary "Hinzufügen", class:"btn btn-dark" %>
          <% end %>
          <h4>Beacon Messages Vorschau</h4>
          <div class="beacon-preview-wrapper">
            <label class="control-label" for="beacon_id_preview">Help Scout Id:</label>
            <input type="text" class="form-control" name="beacon_id" id="beacon_id_preview"/>
            <button class="btn btn-dark" name="Beacon Message Vorschau" id="beacon_preview" onclick="preview_message()">Beacon Message Vorschau</button>
          </div>
        </div>


      </div>
      <div class="well well-lg">
        <h4>Aktive Beacon Messages</h4>
        <div class="beacon_messages_table_wrapper">
          <%= render partial: 'beacon_messages_table' %>
        </div>
      </div>
    </div>
  </div>
</div>
<script>
    function preview_message() {
        Beacon('show-message', $('#beacon_id_preview').val(), { force: true, delay: 1 });
    }
</script>
