# frozen_string_literal: true

# TODO: refactor this class to a Service instead of helper
# rubocop:disable Rails/HelperInstanceVariable
module SyncHelper
  require 'open-uri'

  class SyncHelper
    include StatusUpdate
    attr_accessor :sync_target

    def initialize(shop, is_import_job = false)
      @shop_domain = "https://#{shop.shopify_domain}"
      @shop = shop
      @sync_target = SyncTarget.new(@shop, is_import_job)
    end

    def sync_entity(shopify_entity, rule)
      @shop.refresh_token_if_expired
      @shop.reload
      sync_info = SyncInfo.find_by(shopify_id: shopify_entity.id, shop_id: @shop.id, target_type: rule.target_type)

      if sync_info.present? && sync_info.target_id.present?
        target_doc = InitializeTargetDocService.new(shopify_entity, @shop).call
        FindTargetDocService.new(target_doc, sync_info, shopify_entity, rule, self).call
      else
        HandleShopifyEntityService.new(shopify_entity, rule, @sync_target).call
      end
    end
  end
end

# rubocop:enable Rails/HelperInstanceVariable
