# frozen_string_literal: true

module Api
  module Support
    class CouponCodesController < SupportAuthenticatedController
      before_action :set_coupon_code, only: :destroy
      before_action :authenticate_support_user!

      # GET /coupon_codes or /coupon_codes.json
      def index
        @coupon_codes = ShopifyBilling::CouponCode.all.paginate(page: coupon_code_params[:page])
          .order(created_at: :desc).per_page(10)
        render json: { coupon_codes: @coupon_codes, total_pages: @coupon_codes.total_pages }
      end

      # POST /coupon_codes or /coupon_codes.json
      def create
        if params[:type] == "campaign"
          CampaignCouponCodeService.call(params[:number_of_coupons], params[:free_days], params[:redeem_counter],
            params[:validity], params[:individual_prefix])
        else
          OneTimeCouponCodeService.call(params[:number_of_coupons], params[:free_days])
        end
        render json: { status: "success" }
      end

      # DELETE /coupon_codes/1 or /coupon_codes/1.json
      def destroy
        @coupon_code.destroy
        render json: { status: "success" }
      end

      private

      # Use callbacks to share common setup or constraints between actions.
      def set_coupon_code
        @coupon_code = ShopifyBilling::CouponCode.find(params[:id])
      end

      # Only allow a list of trusted parameters through.
      def coupon_code_params
        params.permit(:number_of_coupons, :free_days, :page, :validity, :individual_prefix, :redeem_counter, :type)
      end
    end
  end
end
