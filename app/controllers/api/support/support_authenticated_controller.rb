# frozen_string_literal: true

module Api
  module Support
    class SupportAuthenticatedController < ApplicationController
      before_action :authenticate_support_user!

      def after_sign_in_path_for
        "/support"
      end

      private

      def authenticate_support_user!
        render json: { success: false, message: "Unauthorized" }, status: :unauthorized unless support_user_signed_in?
      end
    end
  end
end
