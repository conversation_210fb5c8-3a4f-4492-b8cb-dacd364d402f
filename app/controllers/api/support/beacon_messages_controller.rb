# frozen_string_literal: true

# beacon messages controller
module Api
  module Support
    class BeaconMessagesController < SupportAuthenticatedController
      before_action :authenticate_support_user!
      # GET /beacon_messages or /beacon_messages.json
      def index
        @beacon_messages = BeaconMessage.all
        @beacon_areas = [{ id: "none", name: "<PERSON><PERSON>" },
                         { id: "general-settings", name: "Allgemeine Einstellungen" },
                         { id: "email", name: "Dokumentversand per Email" },
                         { id: "invoice", name: "<PERSON><PERSON><PERSON><PERSON><PERSON>" },
                         { id: "credit-note", name: "Gutschriften" },
                         { id: "payments", name: "Synchronisation von Zahlungsdaten" },
                         { id: "errors-log", name: "Übertragungslog" },
                         { id: "import", name: "Datenimport" }]
        render json: { beacon_messages: @beacon_messages, beacon_areas: @beacon_areas }
      end

      # GET /beacon_messages/1 or /beacon_messages/1.json
      def show; end

      # GET /beacon_messages/new
      def new
        @beacon_message = BeaconMessage.new
      end

      # GET /beacon_messages/1/edit
      def edit; end

      # POST /beacon_messages or /beacon_messages.json
      def create
        @beacon_message = BeaconMessage.new(beacon_message_params)

        if @beacon_message.save
          if params[:new_feature_message].present?
            BeaconMessage.where(new_feature_message: true).where.not(id: @beacon_message.id).find_each(&:destroy)
          end
          @beacon_messages = BeaconMessage.all
          render json: { beacon_messages: @beacon_messages }
        else
          render json: { errors: @beacon_message.errors }, status: :unprocessable_entity
        end
      end

      # PATCH/PUT /beacon_messages/1 or /beacon_messages/1.json
      def update
        respond_to do |_format|
          if @beacon_message.update(beacon_message_params)
            render json: { beacon_message: @beacon_message, status: :ok }
          else
            render json: { errors: @beacon_message.errors, status: :unprocessable_entity }
          end
        end
      end

      # DELETE /beacon_messages/1 or /beacon_messages/1.json
      def destroy
        set_beacon_message
        @beacon_message.destroy
        render json: { status: :ok }
      end

      private

      # Use callbacks to share common setup or constraints between actions.
      def set_beacon_message
        @beacon_message = BeaconMessage.find(params[:id])
      end

      # Only allow a list of trusted parameters through.
      def beacon_message_params
        params.permit(:name, :help_scout_id, :new_feature_message, :domain_name)
      end
    end
  end
end
