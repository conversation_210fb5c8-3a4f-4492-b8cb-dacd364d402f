# frozen_string_literal: true

module Api
  module Support
    class SupportController < SupportAuthenticatedController
      include ::Support::ErrorHandler

      before_action :init_shop
      layout "application"

      # @api {get} /api/support/support_user Get current support user
      # @apiName SupportUser
      # @apiGroup Support
      # @apiSuccess {Object} user Current support user information
      def support_user
        if current_support_user.present?
          render json: { user: current_support_user }
        else
          render json: { user: nil }
        end
      end

      # @api {get} /api/support/autocomplete Autocomplete shop domains
      # @apiName Autocomplete
      # @apiGroup Support
      # @apiParam {String} url Input for autocomplete
      # @apiSuccess {Array} data List of matching shop domains
      def autocomplete
        input = params[:url]
        results = Rails.cache.fetch("shop_autocomplete:#{input}", expires_in: 1.hour) do
          Shop.where("shopify_domain LIKE ?", "#{input}%")
            .limit(10)
            .pluck(:shopify_domain)
        end
        render json: { data: results }
      end

      # @api {get} /api/support/merchant View merchant details
      # @apiName ViewMerchant
      # @apiGroup Support
      # @apiParam {String} shop Shop domain
      # @apiSuccess {Object} data Merchant details
      def view_merchant
        result = ::Support::MerchantViewService.call(@current_shop, current_support_user, request)
        render json: result
      end

      # @api {post} /api/support/toggle_test_shop Toggle test shop status
      # @apiName ToggleTestShop
      # @apiGroup Support
      # @apiSuccess {Object} success Success status
      def toggle_test_shop
        result = ::Support::TestShopService.call(@current_shop)
        render json: result
      end

      # @api {get} /api/support/transfer_history Get transfer history
      # @apiName TransferHistory
      # @apiGroup Support
      def transfer_history
        transfers, total_count = SupportTransferLogFetcherService.call(
          @current_shop.id,
          search: params[:search],
          filter: params[:filter],
          interval: {
            start_date: params[:start_date],
            end_date: params[:end_date]
          },
          page: params[:page] || 1
        )

        render json: {
          transfers: TransfersTransformerService.call(transfers, for_support: true),
          totalPages: calculate_total_pages(total_count)
        }
      end

      # @api {post} /api/support/retry_transfers Retry failed transfers
      # @apiName RetryTransfers
      # @apiGroup Support
      def retry_transfers
        result = ::Support::RetryTransfersService.call(@current_shop, support_params[:retry_all],
          support_params[:single_error_id], params[:order_ids])
        render json: { success: true, job_count: result[:job_count] }
      end

      # @api {delete} /api/support/delete_entities Delete entities
      # @apiName DeleteEntities
      # @apiGroup Support
      def delete_entities
        result = ::Support::DeleteEntitiesService.call(@current_shop, support_params[:entity_type], params[:order_ids],
          support_params[:all])
        render json: { success: result[:success] }
      end

      # @api {get} /api/support/import_running Check if import is running
      # @apiName ImportRunning
      # @apiGroup Support
      def import_running
        render json: {
          success: true,
          import_running: @current_shop.has_running_import?
        }
      end

      # @api {post} /api/support/start Start import
      # @apiName Start
      # @apiGroup Support
      def start
        result = ::Support::StartImportService.call(@current_shop,
          import_params[:start_date],
          import_params[:end_date],
          {
            invoices: import_params[:invoices],
            refunds: import_params[:refunds],
            transactions: import_params[:transactions]
          },
          locale)
        render json: { success: true, job_id: result[:job_id] }
      end

      # @api {get} /api/support/count Count orders, refunds and transactions
      # @apiName Count
      # @apiGroup Support
      def count
        result = ::Support::CountService.call(@current_shop, params[:start_date], params[:end_date])
        render json: { success: true, **result }
      end

      # @api {post} /api/support/activate_plan Activate plan
      # @apiName ActivatePlan
      # @apiGroup Support
      def activate_plan
        result = ::Support::PlanService.call(@current_shop, support_params[:plan], support_params[:plan_id])
        render json: { success: result[:success] }
      end

      # @api {get} /api/support/order Get order details
      # @apiName Order
      # @apiGroup Support
      def order
        render json: ::Support::OrderDetailsService.call(@current_shop, support_params[:order_id])
      end

      # @api {post} /api/support/apply_discount Apply discount
      # @apiName ApplyDiscount
      # @apiGroup Support
      def apply_discount
        result = ::Support::ApplyDiscountService.call(@current_shop, support_params[:plan], support_params[:discount])
        render json: { success: result[:success] }
      rescue Support::DiscountError => e
        respond_with_error(e, 422, e.message)
      end

      private

      def init_shop
        shop_param = support_params[:shop] || import_params[:shop]
        @current_shop = Shop.find_by(shopify_domain: shop_param)
      end

      def support_params
        params.permit(
          :url, :shop, :order_ids, :page, :search, :filter,
          :retry_all, :plan, :plan_id, :order_id,
          :discount, :start_date, :end_date, :entity_type,
          :all, :single_error_id
        )
      end

      def import_params
        params.permit(
          :start_date, :end_date, :shop,
          :invoices, :refunds, :transactions
        )
      end

      def calculate_total_pages(total_count, per_page = 30)
        ((total_count - 1) / per_page) + 1
      end
    end
  end
end
