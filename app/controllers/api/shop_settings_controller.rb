# frozen_string_literal: true

module Api
  class ShopSettingsController < AuthenticatedController
    before_action :find_or_create_shop_from_session
    before_action :find_shop_settings, only: %i[shop_settings update]
    before_action :find_transaction_settings, only: %i[shop_settings update]

    def shop_settings
      render json: { shop_setting: @shop_settings, transaction_setting: @transaction_settings,
                     beacon_messages: BeaconMessage.all,
                     layouts: }
    end

    def update
      update_success = true
      update_success &= update_shop_settings if shop_setting_params.present?
      update_success &= update_transaction_settings if transaction_setting_params.present?

      if update_success
        render_update_success
      else
        render_update_failure
      end
    rescue ActiveRecord::RecordInvalid => e
      render json: {
        success: false,
        errors: e.record.errors.map do |error|
          {
            field: error.attribute,
            key: error.type,
            options: error.options.except(:message)
          }
        end,
        error_type: "validation_error"
      }
    end

    def liquid_preview
      @errors = ""
      @current_shop = Shop.find_by(shopify_domain: current_shopify_domain)
      text = params[:text]
      @preview, @errors, @liquid_error = LiquidCodeService.call(@current_shop, @errors, text)
      render json: { preview: @preview, errors: @errors, liquid_error: @liquid_error, success: true }
    end

    private

    def find_shop_settings
      @shop_settings = @current_shop.shop_setting
    end

    def find_transaction_settings
      @transaction_settings = @current_shop.transaction_setting
    end

    def shop_setting_params
      params.require(:shop_setting)
        .permit(:create_orders, :create_invoices, :calculate_shipping_tax, :send_invoice_mail,
          :invoice_mail_subject, :invoice_mail_body, :invoice_account,
          :excludePOS, :create_refunds, :invoice_pretext, :invoice_posttext,
          :mark_due_immediately, :invoice_title, :create_customer, :refund_pretext,
          :refund_posttext, :refund_title, :credit_note_mail_subject, :credit_note_mail_body,
          :invoice_language, :shipping_type, :shipping_min_days, :shipping_max_days,
          :enable_tender_transactions, :enable_tah_creation, :invoice_timing,
          :invoice_mail_offset_days, :use_SKUs, :confirm_tax_settings, :invoice_layout_id,
          :refund_layout_id, :mail_exclusion_tags, :order_exclusion_tags, :use_shipping_address_for_invoices)&.compact
    end

    def transaction_setting_params
      params[:transaction_setting]
        &.permit(:enable_amazon, :enable_apple_pay, :enable_credit_card, :enable_google_pay,
          :enable_klarna, :enable_samsung_pay, :enable_shopify_pay, :enable_sofort,
          :enable_klarna_pay_later, :enable_other)&.compact
    end

    def extra_accounts_infos
      return unless params[:transaction_setting].present? && params[:transaction_setting].key?(:extra_accounts_info)

      # `extra_accounts_info` is present in the parameters
      extra_accounts = params[:transaction_setting][:extra_accounts_info]
      allowed_params = extra_accounts.keys.select do |param|
        param.start_with?("enable")
      end

      return {} if extra_accounts.empty?

      extra_accounts.permit(allowed_params)
    end

    def layouts
      @current_shop.connected_to_lexoffice? ? Lexoffice::PrintLayouts.new(@current_shop.lexoffice_token).all : []
    rescue RestClient::Unauthorized
      []
    end

    def update_shop_settings
      @shop_settings.update!(shop_setting_params)
    end

    def update_transaction_settings
      success = @transaction_settings.update!(transaction_setting_params)
      if extra_accounts_infos.present?
        success &= @transaction_settings.extra_accounts_info.update(extra_accounts_infos)
        @transaction_settings.save!
      end
      trigger_financial_account_job if @shop_settings.enable_tender_transactions
      success
    end

    def trigger_financial_account_job
      FinancialAccountJob.perform_async(@current_shop.id)
    end

    def render_update_success
      render json: { success: true, shop_setting: @shop_settings,
                     transaction_setting: @transaction_settings }
    end

    def render_update_failure
      render json: { success: false }
    end
  end
end
