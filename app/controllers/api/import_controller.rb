# frozen_string_literal: true

module Api
  class ImportController < AuthenticatedController
    before_action :find_or_create_shop_from_session

    def import_running
      render json: { success: true, import_running: @current_shop.has_running_import? }.to_json
    end

    def start
      unless @current_shop.import_unlocked?
        render json: { error: I18n.t('import.message.not_allowed') }
        return
      end
      start_date = import_params[:start_date].to_date.next_day
      end_date = import_params[:end_date].to_date.next_day

      import_invoices = import_params[:invoices]
      import_refunds = import_params[:refunds]
      import_transactions = import_params[:transactions]

      if @current_shop.plan_active?
        if @current_shop.has_running_import?
          # job = JobStatus.running_for_shop(@current_shop.id)
          render json: { error: I18n.t('import.message.already_running') }
          return
        end
        if !import_invoices && !import_refunds && !import_transactions
          render json: { error: I18n.t('import.message.nothing_to_import') }
          return
        end

        job = SyncAllJob.perform_async(@current_shop.id, start_date, end_date,
                                       { invoices: import_invoices,
                                         refunds: import_refunds,
                                         transactions: import_transactions },
                                       locale)
        render json: { success: true, job_id: job }.to_json
      else
        render json: { error: I18n.t('import.message.plan_not_active') }
      end
    end

    # counts the number of orders, refunds and transactions for the given timeframe and returns the result in json
    def count
      unless @current_shop.import_unlocked?
        render json: { success: true, orders: 0, refunds: 0, transactions: 0 }
        return
      end
      start_date = params[:start_date].to_date.next_day # dates seem to be offset by one day
      end_date = params[:end_date].to_date.next_day
      orders = ShopifyAPI::Order.count(created_at_min: start_date.strftime('%FT%T%:z'),
                                       created_at_max: end_date.strftime('%FT%T%:z'),
                                       status: 'any').body['count']
      refunds = ShopifyAPI::Order.count(created_at_min: start_date.strftime('%FT%T%:z'),
                                        created_at_max: end_date.strftime('%FT%T%:z'),
                                        financial_status: 'refunded', status: 'any').body['count']
      refunds += ShopifyAPI::Order.count(created_at_min: start_date.strftime('%FT%T%:z'),
                                         created_at_max: end_date.strftime('%FT%T%:z'),
                                         financial_status: 'partially_refunded', status: 'any').body['count']
      transactions = Shopify::TenderTransactionsCountService.call(@current_shop, start_date, end_date)

      render json: { success: true, orders:, refunds:, transactions: }
    end

    private

    def import_params
      params.permit(:start_date, :end_date, :invoices, :refunds, :transactions)
    end
  end
end
