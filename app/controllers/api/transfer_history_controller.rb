# frozen_string_literal: true

module Api
  class TransferHistoryController < AuthenticatedController
    before_action :find_or_create_shop_from_session

    def transfer_history
      # To compare implementations, comment out the one you don't want to use
      # and uncomment the one you want to test
      result = if params[:order_id].present?
                 transfers = TransferLogFetcherService.new(@current_shop.id,
                   order_id: params[:order_id]).call
                 {
                   transfers: TransfersTransformerService.call(transfers),
                   errors: @current_shop.errors? # Used for ErrorLog Banner
                 }
               else
                 transfers, total_count = TransferLogFetcherService.new(@current_shop.id,
                   search: params[:search],
                   filter: params[:filter],
                   page: params[:page] || 1).call
                 {
                   transfers: TransfersTransformerService.call(transfers),
                   totalPages: (((total_count - 1) / 30) + 1),
                   errors: @current_shop.errors? # Used for ErrorLog Banner
                 }
               end
      render json: result
    rescue StandardError => e
      Rails.error.report(e)
      render json: { error: "An error occurred while fetching transfer history" }, status: :internal_server_error
    end

    # Retries the transfers for the given order IDs and returns the result.
    #
    # This method fetches the transfer errors for the current shop and the specified order IDs,
    # retries each transfer with a delay based on its position in the loop, and returns the
    # success status, job count, and the last retry queue time to the frontend.
    #
    # @return [JSON] A JSON response containing the success status, job count, and last retry queue time.
    def retry_transfers
      transfer_errors = ErrorLog.where(shop_id: @current_shop.id, order_id: params[:order_ids])
      count = transfer_errors.count
      last_retry_time = nil
      delay = ENV.fetch("RETRY_DELAY", 2).to_i.seconds

      transfer_errors.each_with_index do |error, index|
        last_retry_time = Time.zone.now + (index * delay)
        error.retry_job(last_retry_time)
      end
      render json: { success: true, job_count: count, last_retry_time: }
    rescue StandardError => e
      Rails.error.report(e, context: { tags: "Requeue" })
      render json: { success: false }
    end

    def retry_skipped_transfers
      sync_infos = SyncInfo.where(shop_id: @current_shop.id, id: params[:sync_info_ids])
      count = sync_infos.count

      sync_infos.each(&:retry_job)
      render json: { success: true, job_count: count }
    rescue StandardError => e
      Rails.error.report(e, context: { tags: "Requeue" })
      render json: { success: false }
    end
  end
end
