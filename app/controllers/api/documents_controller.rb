# frozen_string_literal: true

module Api
  class DocumentsController < AuthenticatedController
    before_action :find_or_create_shop_from_session

    def redirect_to_invoice
      order_id = params[:id]
      @sync_info = SyncInfo.for_shop(@current_shop.id).find_by(shopify_id: order_id)
      if @sync_info.nil? || @sync_info.target_id.nil? || @current_shop.lexoffice_token.blank?
        render json: { status: 'not found' }
        return
      end

      @invoice_url = "#{ENV.fetch('LEXOFFICE_SITE', nil)}/vouchers#!/VoucherView/Invoice/#{@sync_info.target_id}"
      render json: { url: @invoice_url }
    end

    def invoice_pdf
      order_id = params[:id]
      @sync_info = SyncInfo.for_shop(@current_shop.id).find_by(shopify_id: order_id)
      if @sync_info.nil? || @sync_info.target_id.nil? || @current_shop.lexoffice_token.blank?
        render json: { status: 'not found' }
        return
      end

      @current_shop.refresh_token_if_expired
      invoice_endpoint = Lexoffice::Invoice.new(@current_shop.lexoffice_token)
      respond_to do |format|
        format.html do
          render_pdf(invoice_endpoint)
        end
        format.pdf do
          render_pdf(invoice_endpoint)
        end
      end
    end

    private

    def render_pdf(invoice_endpoint)
      pdf_binary = invoice_endpoint.pdf(@sync_info.target_id)
      filename = invoice_endpoint.voucher_number(@sync_info.target_id).insert(2, '-') || 'Rechnung'
      filename += '.pdf' unless filename.end_with?('.pdf')
      send_data(Base64.decode64(pdf_binary), filename:, type: 'application/pdf')
    end
  end
end
