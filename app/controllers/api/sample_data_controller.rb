# frozen_string_literal: true

module Api
  class SampleDataController < AuthenticatedController
    before_action :find_or_create_shop_from_session

    SAMPLE_ORDER_STRUCTURE = {
      id: 123_456_789,
      name: "#1001",
      email: "<EMAIL>",
      created_at: nil, # will be set dynamically
      processed_at: nil, # will be set dynamically
      currency: "EUR",
      total_price: "99.99",
      subtotal_price: "89.99",
      total_tax: "10.00",
      financial_status: "paid",
      fulfillment_status: "fulfilled",
      customer: {
        id: 987_654_321,
        email: "<EMAIL>",
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        phone: "+49123456789"
      },
      billing_address: {
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        address1: "123 Billing St",
        city: "Berlin",
        zip: "10115",
        province: "Berlin",
        country: "Germany",
        country_code: "DE"
      },
      shipping_address: {
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        address1: "123 Shipping St",
        city: "Berlin",
        zip: "10115",
        province: "Berlin",
        country: "Germany",
        country_code: "DE"
      },
      line_items: [
        {
          id: 111_222_333,
          title: "Sample Product",
          price: "49.99",
          quantity: 2,
          sku: "SAMPLE-001"
        }
      ]
    }.freeze

    def sample_order
      @current_shop.with_shopify_session do
        order = fetch_most_recent_order

        if order.nil?
          handle_no_order_found
        else
          handle_order_found(order)
        end
      end
    rescue StandardError => e
      handle_sample_order_error(e)
    end

    private

    def fetch_most_recent_order
      ShopifyAPI::Order.all(limit: 1).first
    end

    def handle_no_order_found
      sample_data = sample_order_structure
      cache_sample_order(sample_data)
      render json: { order: sample_data, success: true }
    end

    def handle_order_found(order)
      order.order_transactions = order.transactions if order.respond_to?(:transactions)
      cache_sample_order(order)
      render json: { order: order.as_json, success: true }
    end

    def handle_sample_order_error(error)
      Rails.logger.error("Error fetching sample order: #{error.message}")
      sample_data = sample_order_structure
      cache_sample_order(sample_data)
      render json: { order: sample_data, success: false, error: error.message }
    end

    def cache_sample_order(order_data)
      Rails.cache.write("sample_order_#{@current_shop.id}", order_data.to_json, expires_in: 10.minutes)
    end

    def sample_order_structure
      now = Time.now.iso8601
      sample = SAMPLE_ORDER_STRUCTURE.deep_dup
      sample[:created_at] = now
      sample[:processed_at] = now
      sample
    end
  end
end
