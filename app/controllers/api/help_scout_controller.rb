# frozen_string_literal: true

module Api
  class HelpScoutController < AuthenticatedController
    before_action :find_shop_from_session

    def default_articles
      articles = Rails.cache.fetch('help_default_articles',
                                   expires_in: ENV.fetch('HELP_ARTICLES_CACHE_TIME', 240).to_i.minutes) do
        response = JSON.parse(
          RestClient.get(
            "#{ENV.fetch('HELPSCOUT_API_URL')}/categories/#{ENV.fetch('HELPSCOUT_TOP10_CATEGORY_ID')}/articles",
            headers
          )
        )
        response.dig('articles', 'items').map { |article| { id: article['id'], title: article['name'] } }
      end

      render json: { articles: }
    rescue RestClient::MethodNotAllowed, RestClient::NotFound
      render json: {}, status: :not_found
    end

    def search_articles
      search_params = "?collectionId=#{ENV.fetch('HELPSCOUT_COLLECTION_ID')}&status=published&visibility=public"
      response = JSON.parse(
        RestClient.get("#{ENV.fetch('HELPSCOUT_API_URL')}/search/articles#{search_params}&query=#{params['query']}",
                       headers)
      )
      items = response.dig('articles', 'items')

      if items.empty?
        render json: {}, status: :no_content
        return
      end

      articles = items.map { |article| { id: article['id'], title: article['name'] } }
      render json: { articles: articles.first(10) }
    rescue RestClient::MethodNotAllowed, RestClient::NotFound
      render json: {}, status: :not_found
    end

    def fetch_article
      article = Rails.cache.fetch("help_article_raw_#{params['id']}",
                                  expires_in: ENV.fetch('HELP_ARTICLES_CACHE_TIME', 240).to_i.minutes) do
        response = JSON.parse(RestClient.get("#{ENV.fetch('HELPSCOUT_API_URL')}/articles/#{params['id']}", headers))

        {
          title: response['article']['name'],
          rawBody: response['article']['text']
        }
      end

      render json: { article: }
    rescue RestClient::MethodNotAllowed, RestClient::NotFound
      render json: {}, status: :not_found
    end

    private

    def headers
      encoded_api_key = Base64.strict_encode64("#{ENV.fetch('HELPSCOUT_API_KEY')}:X")
      { Authorization: "Basic #{encoded_api_key}", 'Content-Type': 'application/json' }
    end

    def enrich_body_html(body)
      doc = Nokogiri::HTML.fragment(body)

      doc = enrich_headings(doc)
      doc = enrich_text(doc)
      doc = enrich_media(doc)

      doc.to_html
    end

    def enrich_headings(doc)
      doc.css('h1').each do |h1|
        h1['class'] = 'Polaris-Text--root Polaris-Text--headingLg'
        h1['style'] = 'margin: var(--p-space-5) 0;'
      end
      doc.css('h2').each do |h2|
        h2['class'] = 'Polaris-Text--root Polaris-Text--headingMd'
        h2['style'] = 'margin: var(--p-space-4) 0;'
      end
      doc.css('h3').each do |h3|
        h3['class'] = 'Polaris-Text--root Polaris-Text--headingMd'
        h3['style'] = 'margin: var(--p-space-3) 0;'
      end
      doc
    end

    def enrich_text(doc)
      doc.css('p').each do |p|
        p['class'] = 'Polaris-Text--root'
        p['style'] = 'margin-bottom: var(--p-space-3);'
      end
      doc.css('pre').each do |pre|
        code = Nokogiri::XML::Node.new 'code', doc
        code.inner_html = pre.inner_html
        code['style'] =
          'display: block; width: 100%; margin: var(--p-space-4) 0; padding: var(--p-space-3);' \
          'background-color: var(--p-color-bg-app-selected); border-radius: var(--p-border-radius-1);'
        code.css('p').each { |p| p['style'] = 'margin: var(--p-space-2) 0' }
        pre.replace(code)
      end
      doc
    end

    def enrich_media(doc)
      doc.css('img').each { |img| img['width'] = '100%' }
      doc.css('a').each { |a| a['target'] = '_blank' }
      doc
    end
  end
end
