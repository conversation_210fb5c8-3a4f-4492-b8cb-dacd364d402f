# frozen_string_literal: true

module Api
  # A controller for returning the shop status. The status determines the UI status in the frontend for some of the
  # components.
  class ShopStatusController < AuthenticatedController
    before_action :find_or_create_shop_from_session

    def status
      if @current_shop.should_run_invoice_info_job?
        InvoiceDataRetrievalJobTop80.perform_async(@current_shop.id)
        @current_shop.update(invoice_info_job_ran: true)
      end
      render json: { status: EshopGuide::ShopStatusService.call(@current_shop) }
    end

    def disconnect_lexoffice
      if @current_shop.reset_lexoffice_connection
        render json: { success: true }
      else
        render json: { success: false }
      end
    end
  end
end
