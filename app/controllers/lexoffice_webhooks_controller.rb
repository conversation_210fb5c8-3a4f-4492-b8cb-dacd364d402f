# frozen_string_literal: true

class LexofficeWebhooksController < ApplicationController
  include Lexoffice::WebhookVerification

  before_action :find_current_shop, only: %i[invoice_status_changed credit_note_status_changed]

  def lexoffice_token_revoked
    lexoffice_organization_id = params["organizationId"]
    lexoffice_connection_id = params["resourceId"]
    @current_shop = Shop.find_by(lexoffice_organization_id:, lexoffice_connection_id:)

    return head :no_content if @current_shop.nil?

    # wenn die Verbindung länger als 2 minuten bestand
    if params["eventDate"].to_time.utc > (@current_shop.connection_established_at + 2.minutes)
      # tokens des Shops entfernen
      @current_shop.reset_lexoffice_connection
    end
    head :no_content
  end

  def invoice_status_changed
    process_status_change("Invoice", params)
  end

  def credit_note_status_changed
    process_status_change("CreditNote", params)
  end

  private

  def find_current_shop
    @current_shop = Shop.find_by(lexoffice_organization_id: params["organizationId"])
  end

  def process_status_change(doc_type, params)
    doc_id = params["resourceId"]
    return head :ok if extract_resource(params["eventType"]) != doc_type

    return head :not_found if @current_shop.nil? || @current_shop&.lexoffice_token&.nil?

    sync_info = SyncInfo.find_by(target_id: doc_id)
    return head :ok if sync_info.nil?

    DocStatusJob.perform_async(doc_type, doc_id)
  end

  def extract_resource(event_type)
    return if event_type.nil?

    types = event_type.split(".")
    return if types.blank?

    types.first.split("-").map(&:capitalize).join
  end
end
