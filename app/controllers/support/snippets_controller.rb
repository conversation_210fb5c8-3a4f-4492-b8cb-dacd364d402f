# frozen_string_literal: true

module Support
  class SnippetsController < ApplicationController
    before_action :set_snippet, only: %i[show edit update destroy]
    before_action :authenticate_support_user!

    # GET /snippets or /snippets.json
    def index
      @snippets = Snippet.all.paginate(page: 1).order(id: :desc)
    end

    # GET /snippets/1 or /snippets/1.json
    def show; end

    # GET /snippets/new
    def new
      @snippet = Snippet.new
    end

    # GET /snippets/1/edit
    def edit; end

    # POST /snippets or /snippets.json
    def create
      @snippet = Snippet.new(snippet_params)

      respond_to do |format|
        if @snippet.save
          format.html { redirect_to support_snippets_url(@snippet), notice: t("support.snippets.created") }
          format.json { render :show, status: :created, location: @snippet }
        else
          format.html { render :new, status: :unprocessable_entity }
          format.json { render json: @snippet.errors, status: :unprocessable_entity }
        end
      end
    end

    # PATCH/PUT /snippets/1 or /snippets/1.json
    def update
      respond_to do |format|
        if @snippet.update(snippet_params)
          format.html { redirect_to support_snippets_url(@snippet), notice: t("support.snippets.updated") }
          format.json { render :show, status: :ok, location: @snippet }
        else
          format.html { render :edit, status: :unprocessable_entity }
          format.json { render json: @snippet.errors, status: :unprocessable_entity }
        end
      end
    end

    # DELETE /snippets/1 or /snippets/1.json
    def destroy
      @snippet.destroy

      respond_to do |format|
        format.html { redirect_to support_snippets_url, notice: t("support.snippets.destroyed") }
        format.json { head :no_content }
      end
    end

    def preview
      @errors = ""
      @current_shop = Shop.find_by(shopify_domain: params[:shop_domain])
      text = params[:code]
      order_id = params[:order_id]
      current_support_user.track("preview_snippet", {
        snippet: text, order_id: order_id, shop_domain: params[:shop_domain]
      })

      if text.present? && @current_shop.present? && order_id.present?
        @preview, @errors, @liquid_error = LiquidCodeService.call(@current_shop, @errors, text, order_id)
      else
        @errors = t("support.snippets.error")
      end
      render layout: false
    end

    def show_snippets_table
      page = params[:page] || 1
      @snippets = Snippet.all.paginate(page:).order(id: :desc)
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_snippet
      @snippet = Snippet.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def snippet_params
      params[:snippet].permit(:name, :code, :description)
    end
  end
end
