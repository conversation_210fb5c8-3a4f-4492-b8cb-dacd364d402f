# frozen_string_literal: true

module Support
  class CouponCodesController < ApplicationController
    before_action :set_coupon_code, only: :destroy
    before_action :authenticate_support_user!

    # GET /coupon_codes or /coupon_codes.json
    def index
      @coupon_codes = []
    end

    def show_table
      page = params[:page] || 1
      @coupon_codes = ShopifyBilling::CouponCode.all.paginate(page:).order(id: :desc).per_page(10)

      return unless request.xhr?

      respond_to do |format|
        format.js
      end
    end

    # GET /coupon_codes/new
    def new
      @coupon_code = ShopifyBilling::CouponCode.new
    end

    # POST /coupon_codes or /coupon_codes.json
    def create
      if params[:type] == ('campaign')
        CampaignCouponCodeService.new(params[:number_of_coupons],
                                      params[:free_days],
                                      params[:redeem_counter],
                                      params[:validity],
                                      params[:individual_prefix]).call
      else
        OneTimeCouponCodeService.new(params[:number_of_coupons], params[:free_days]).call
      end
      @coupon_codes = ShopifyBilling::CouponCode.all
      redirect_to action: show_table
    end

    # DELETE /coupon_codes/1 or /coupon_codes/1.json
    def destroy
      @coupon_code.destroy
      @coupon_codes = ShopifyBilling::CouponCode.all.paginate(page: 1).order(id: :desc).per_page(10)
      respond_to do |format|
        format.html do
          redirect_to support_coupon_codes_show_table_path, notice: I18n.t('support.coupon_code.destroyed')
        end
        format.js { render 'show_table' }
      end
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_coupon_code
      @coupon_code = ShopifyBilling::CouponCode.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def coupon_code_params
      params.require(:coupon_code).permit(:number_of_coupons, :free_days)
    end

    def authenticate
      unless authenticate_with_http_basic do |u, p|
        (ENV['SUPPORT_USERNAME'] == u && ENV['SUPPORT_PASSWORD'] == p)
      end
        request_http_basic_authentication
      end
    end
  end
end
