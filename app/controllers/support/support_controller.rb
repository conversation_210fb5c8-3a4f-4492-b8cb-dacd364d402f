# frozen_string_literal: true

module Support
  class SupportController < ApplicationController
    before_action :authenticate_support_user!
    before_action :set_language_de
    layout 'application'

    def index; end

    def autocomplete
      input = params[:input]
      # Check the cache for the results
      results = Rails.cache.fetch(input) do
        # If the results are not in the cache, fetch them from the database
        Shop.where('shopify_domain LIKE ?', "#{input}%").pluck(:shopify_domain)
      end
      render json: results
    end

    def view_order
      order_id = params[:order_id]
      shop_domain = params[:shop].sub! ".myshopify.com", ""
      shop = Shop.find_by(shopify_domain: "#{shop_domain}.myshopify.com")
      current_support_user.track("view_order", { order_id:, shop: shop_domain })

      shop.with_shopify_session do
        @order = ShopifyAPI::Order.find(id: order_id)
        render json: { order: @order, settings: shop.shop_setting, shop_info: shop }
      end
    rescue StandardError
      render404
    end

    def view_merchant
      shopify_domain = "#{params[:shop]}.myshopify.com"
      @current_shop = Shop.find_by(shopify_domain:)
      current_support_user.track("view_merchant", { shop: shopify_domain })

      @shop_info = [] << @current_shop
      @shop_settings = @current_shop.shop_setting
      @current_shop.with_shopify_session do
        @shopify_shop_info = ShopifyAPI::Shop.current
        @app_recurring_charge = ShopifyAPI::RecurringApplicationCharge.current
        @app_import_charge = ShopifyAPI::ApplicationCharge.all.first
      end

      # Optimize error logs query with limit and proper ordering
      @shop_errors = ErrorLog.where(shop_id: @current_shop.id)
                             .select(:id, :shopify_name, :order_id, :shopify_type, :error_info_external,
                                     :error_info_internal, :updated_at, :error_type_id, :shopify_id)
                             .includes(:error_type)
                             .order(id: :desc)
                             .limit(10)
                             .paginate(page: 1)

      @shop_sync_infos = []
      @shop_imports_history = []
      @shop_has_errors = @shop_errors.exists?
      @current_plan = @current_shop.billing_plan
      render layout: 'application'
    end

    def show_errors_table
      shopify_domain = "#{params[:shop]}.myshopify.com"
      @current_shop = Shop.find_by(shopify_domain:)
      page = params[:page] || 1
      @shop_errors = ErrorLog.for_shop(@current_shop.id).paginate(page:).order(id: :desc)

      return unless request.xhr?

      respond_to do |format|
        format.js
      end
    end

    def show_sync_infos
      page = params[:page] || 1
      @current_shop = Shop.find_by(shopify_domain: params[:shop])
      sync_from_date = Date.strptime(params['sync_from_date'], '%d.%m.%Y').strftime('%FT%T%:z')
      sync_to_date = Date.strptime(params['sync_to_date'], '%d.%m.%Y').strftime('%FT%T%:z')
      @sync_info_start = params['sync_from_date']
      @sync_info_end = params['sync_to_date']
      @shop_sync_infos = SyncInfo.for_shop(@current_shop.id).paginate(page:)
                                 .where(created_at: sync_from_date..sync_to_date).order(id: :desc).per_page(10)
      return unless request.xhr?

      respond_to do |format|
        format.js
      end
    end

    def show_imports_history
      page = params[:page] || 1
      @current_shop = Shop.find_by(shopify_domain: params[:shop])
      @shop_imports_history = SyncInfo.for_shop(@current_shop.id).paginate(page:)
                                      .where(import: true).order(id: :desc).per_page(10)
      return unless request.xhr?

      respond_to do |format|
        format.js
      end
    end

    def activate_import
      @current_shop = Shop.find(params.require(:shop_id))
      @current_shop.update!(import_manually_unlocked_at: Time.zone.now)

      redirect_to action: 'view_merchant', shop: @current_shop.shopify_domain.split('.').first
    end

    def retry_all
      @current_shop = Shop.find(params[:shop_id])
      ErrorLog.where(shop: @current_shop).each(&:retry_job) unless @current_shop.nil?
      redirect_to action: 'view_merchant', shop: @current_shop.shopify_domain.split('.').first
    end

    def retry_one
      @current_shop = Shop.find(params[:shop_id])
      @current_shop.with_shopify_session do
        @order = ShopifyAPI::Order.find(id: params['shopify_id'])
      end
      unless @current_shop.nil?
        begin
          SyncOrderJob.perform_async('orders/create', @order.id, @current_shop.id)
          @order.refunds.each do |refund|
            RefundJob.perform_async('refunds/create', refund.id, @order.id, @current_shop.id)
          end
        rescue StandardError => e
          Rails.logger.log "Error while retrying Order #{@order.id}: " + e.to_s
        end
      end
      redirect_to action: 'view_merchant', shop: @current_shop.shopify_domain.split('.').first
    end

    def reset
      if params['errors_from_date'].present? && params['errors_to_date'].present?
        errors_from_date = Date.strptime(params['errors_from_date'], '%d.%m.%Y').strftime('%FT%T%:z')
        errors_to_date = Date.strptime(params['errors_to_date'], '%d.%m.%Y').strftime('%FT%T%:z')
        @errors_start = params['errors_from_date']
        @errors_end = params['errors_to_date']
      end
      @current_shop = Shop.find(params[:shop_id])
      errorlogs = if errors_from_date.present? && errors_to_date.present?
                    ErrorLog.where(shop: @current_shop).where(created_at: errors_from_date..errors_to_date)
                  else
                    ErrorLog.where(shop: @current_shop)
                  end
      return if @current_shop.nil?

      errorlogs.delete_all
      redirect_to action: 'view_merchant', shop: @current_shop.shopify_domain.split('.').first
    end

    def entity
      @current_shop = Shop.find(params[:shop_id])
      @entity_id = params['shopify_id']
      @entity_infos = SyncInfo.where({ shop_id: @current_shop.id, shopify_order_id: params[:shopify_id] })
      return unless request.xhr?

      respond_to do |format|
        format.js
      end
    end

    def support_count_orders
      session['orders'] = orders = params['orders'] == 'on'
      session['refunds'] = refunds = params['refunds'] == 'on'
      session['transactions'] = transactions = params['transactions'] == 'on'
      session['import_from_date'] = from_date = Date.strptime(params['from_date'], '%d.%m.%Y').strftime('%FT%T%:z')
      session['import_to_date'] = to_date = Date.strptime(params['to_date'], '%d.%m.%Y').strftime('%FT%T%:z')
      current_shop = Shop.find(params[:shop_id])
      order_count = refund_count = transaction_count = nil
      current_shop.with_shopify_session do
        if orders
          order_count = ShopifyAPI::Order.count(created_at_min: from_date, created_at_max: to_date,
                                                status: 'any').body['count']
        end
        if refunds
          refund_count = ShopifyAPI::Order.count(created_at_min: from_date, created_at_max: to_date,
                                                 financial_status: 'refunded', status: 'any').body['count']
          refund_count += ShopifyAPI::Order.count(created_at_min: from_date, created_at_max: to_date,
                                                  financial_status: 'partially_refunded', status: 'any').body['count']
        end
        if transactions
          transaction_count = Shopify::TenderTransactionsCountService.call(current_shop, from_date, to_date)
        end
      end

      assign_instance_variables(order_count, refund_count, transaction_count, current_shop)

      respond_to do |format|
        format.js
      end
    end

    def support_start_import
      @current_shop = Shop.find(params[:shop_id])
      params['orders']
      params['refunds']
      params['transactions']
      unless @current_shop.billing_plan.id < 1
        if @current_shop.has_running_import?
          flash[:error] = t('import.message.already_running')
          head :conflict
          return
        end
        SyncAllJob.perform_async(@current_shop.id, session['import_from_date'], session['import_to_date'],
                                 { invoices: session['orders'],
                                   refunds: session['refunds'],
                                   transactions: session['transactions'] })
        # head :ok
      end
      redirect_to action: 'view_merchant', shop: @current_shop.shopify_domain.split('.').first
      nil
    end

    def reset_plan
      @current_shop = Shop.find(params[:shop_id])

      BillingServices::ResetBillingPlanService.call(shop: @current_shop)

      redirect_to action: 'view_merchant', shop: @current_shop.shopify_domain.split('.').first
    end

    def requeue_entity
      error_log_entry = ErrorLog.find(params[:id])
      @current_shop = Shop.find_by(shopify_domain: params[:shop])
      error_log_entry.retry_job
      redirect_to action: 'view_merchant', shop: @current_shop.shopify_domain.split('.').first
    end

    def set_import_discount
      @current_shop = Shop.find(params[:shop_id])
      @current_shop.with_shopify_session do
        @current_shop.import_discount_percent = params[:import_rabatt].to_i
        @current_shop.save
        redirect_to action: 'view_merchant', shop: @current_shop.shopify_domain.split('.').first
      end
    end

    def set_plan_discount
      @current_shop = Shop.find(params[:shop_id])
      @current_shop.with_shopify_session do
        @current_shop.discount_percent = params[:plan_rabatt].to_i
        @current_shop.save
        redirect_to action: 'view_merchant', shop: @current_shop.shopify_domain.split('.').first
      end
    end

    def toggle_internal_test_shop
      @current_shop = Shop.find(params[:shop_id])
      @current_shop.update!(internal_test_shop: params[:internal_test_shop])
      redirect_to action: 'view_merchant', shop: @current_shop.shopify_domain.split('.').first
    end

    def delete_data
      @current_shop = Shop.find(params[:shop])
      sync_from_date = Date.strptime(params['sync_from_date'], '%d.%m.%Y').strftime('%FT%T%:z')
      sync_to_date = Date.strptime(params['sync_to_date'], '%d.%m.%Y').strftime('%FT%T%:z')
      @shop_sync_infos = SyncInfo.for_shop(@current_shop.id).where(created_at: sync_from_date..sync_to_date)
      @shop_sync_infos.each(&:destroy)
      redirect_to action: 'view_merchant', shop: @current_shop.shopify_domain.split('.').first
    end

    private

    def set_language_de
      I18n.locale = :de
    end

    def authenticate
      unless authenticate_with_http_basic do |u, p|
        (ENV['SUPPORT_USERNAME'] == u && ENV['SUPPORT_PASSWORD'] == p)
      end
        request_http_basic_authentication
      end
    end

    def render404
      render file: Rails.public_path.join('404.html'), layout: false, status: :not_found
    end

    def calculate_time(count)
      return count if count.nil?

      (((count / 9) + 1) + (count * ENV['IMPORT_JOB_INTERVAL'].to_i)).seconds
    end

    def assign_instance_variables(order_count, refund_count, transaction_count, current_shop)
      @order_count = order_count
      @order_import_time = calculate_time(order_count)
      @refund_count = refund_count
      @refund_import_time = calculate_time(refund_count)
      @transaction_count = transaction_count
      @transaction_import_time = calculate_time(transaction_count)
      @current_shop = current_shop
    end
  end
end
