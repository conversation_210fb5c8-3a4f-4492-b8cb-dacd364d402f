# frozen_string_literal: true

# beacon messages controller
module Support
  class BeaconMessagesController < ApplicationController
    before_action :authenticate_support_user!

    # GET /beacon_messages or /beacon_messages.json
    def index
      @beacon_messages = BeaconMessage.all
      @beacon_areas = [{ id: 'none', name: '<PERSON><PERSON>' },
                       { id: 'general-settings', name: 'Allgemeine Einstellungen' },
                       { id: 'email', name: 'Dokumentversand per Email' },
                       { id: 'invoice', name: '<PERSON><PERSON>nu<PERSON><PERSON>' },
                       { id: 'credit-note', name: 'Gutschriften' },
                       { id: 'payments', name: 'Synchronisation von Zahlungsdaten' },
                       { id: 'errors-log', name: 'Übertragungslog' },
                       { id: 'import', name: 'Datenimport' }]
      render layout: 'application'
    end

    # GET /beacon_messages/1 or /beacon_messages/1.json
    def show; end

    # GET /beacon_messages/new
    def new
      @beacon_message = BeaconMessage.new
    end

    # GET /beacon_messages/1/edit
    def edit; end

    # POST /beacon_messages or /beacon_messages.json
    def create
      @beacon_message = BeaconMessage.new(beacon_message_params)

      if @beacon_message.save
        if params[:new_feature_message].present?
          BeaconMessage.where(new_feature_message: true).where.not(id: @beacon_message.id).each(&:destroy)
        end
        @beacon_messages = BeaconMessage.all
        respond_to do |format|
          format.js { render 'show_beacon_messages' }
        end
      else
        respond_to do |format|
          format.html { render :new, status: :unprocessable_entity }
          format.json { render json: @beacon_message.errors, status: :unprocessable_entity }
        end
      end
    end

    # PATCH/PUT /beacon_messages/1 or /beacon_messages/1.json
    def update
      respond_to do |format|
        if @beacon_message.update(beacon_message_params)
          format.html { redirect_to @beacon_message, notice: I18n.t('support.beacons.updated') }
          format.json { render :show, status: :ok, location: @beacon_message }
        else
          format.html { render :edit, status: :unprocessable_entity }
          format.json { render json: @beacon_message.errors, status: :unprocessable_entity }
        end
      end
    end

    # DELETE /beacon_messages/1 or /beacon_messages/1.json
    def destroy
      set_beacon_message
      @beacon_message.destroy
      @beacon_messages = BeaconMessage.all
      respond_to do |format|
        format.js { render 'show_beacon_messages' }
      end
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_beacon_message
      @beacon_message = BeaconMessage.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def beacon_message_params
      params['beacon_message'].permit(:name, :help_scout_id, :new_feature_message, :domain_name)
    end

    def authenticate
      unless authenticate_with_http_basic do |u, p|
        (ENV['SUPPORT_USERNAME'] == u && ENV['SUPPORT_PASSWORD'] == p)
      end
        request_http_basic_authentication
      end
    end
  end
end
