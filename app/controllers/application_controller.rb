# frozen_string_literal: true

class ApplicationController < ActionController::Base
  include ControllerErrorReporting

  before_action :handle_locale
  # Prevent CSRF attacks by raising an exception.
  # For APIs, you may want to use :null_session instead.
  protect_from_forgery with: :null_session

  private

  def shopify_host
    request.headers['x-host'] || params[:host] || session[:host]
  end

  def handle_locale
    locale = params[:locale] || request.headers[:locale]
    return if locale.blank?

    locale = locale[0..1]
    begin
      I18n.locale = locale
      session[:locale] = locale
    rescue I18n::InvalidLocale
      I18n.locale = I18n.default_locale
      session[:locale] = I18n.default_locale
    end
  end

  def redirect_to_admin(path = nil, status = nil)
    frontend_url = ShopifyAPI::Auth.embedded_app_url(shopify_host)
    frontend_url += "/#{path}" if path.present?
    frontend_url += "?status=#{status}" if status.present?
    redirect_to(frontend_url, allow_other_host: true)
  end

  def after_sign_in_path_for(resource)
    stored_location_for(resource) ||
      if resource.is_a?(SupportUser)
        '/api/support/'
      else
        super
      end
  end
end
