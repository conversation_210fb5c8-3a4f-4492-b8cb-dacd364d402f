# frozen_string_literal: true

require 'base64'
require 'json'
require 'openssl'

module Lexoffice
  module WebhookVerification
    extend ActiveSupport::Concern

    included do
      skip_before_action :verify_authenticity_token, raise: false
      before_action :verify_request
    end

    private

    def verify_request
      data = request.raw_post
      return head :unauthorized unless hmac_valid?(data)
    end

    def hmac_valid?(data)
      secret = ENV["LEXOFFICE_WEBHOOK_SECRET"]

      # verify the data
      digest    = OpenSSL::Digest::SHA512.new
      pub_key   = OpenSSL::PKey::RSA.new(secret)
      signature = Base64.decode64(request.headers["X-Lxo-Signature"])
      verified  = pub_key.verify(digest, signature, data)
      verified
    end
  end
end



