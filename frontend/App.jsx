import { BrowserRouter } from "react-router-dom";
import { NavigationMenu } from "@shopify/app-bridge-react";
import Routes from "./Routes";

import {
  AppBridgeProvider,
  QueryProvider,
  PolarisProvider,
} from "./components";
import EshopGuideProvider from "./shared/providers/EshopGuideProvider";
import { useTranslation } from "react-i18next";
import appService from "./services/appService";
import { Frame } from "@shopify/polaris";
import "./assets/style.scss";
import ErrorBoundary from "../frontend/components/ErrorBoundary.jsx";

export default function App() {
  // Any .tsx or .jsx files in /pages will become a route
  // See documentation for <Routes /> for more info
  const pages = import.meta.globEager("./pages/**/!(*.test.[jt]sx)*.([jt]sx)");
  const { t } = useTranslation();

  return (
    <PolarisProvider>
      <ErrorBoundary>
        <BrowserRouter>
          <AppBridgeProvider>
            <ErrorBoundary>
              <QueryProvider>
                <NavigationMenu
                  navigationLinks={[
                    {
                      label: t('navigation.settings'),
                      destination: "/ShopSettings",
                    },
                    {
                      label: t('navigation.plans_and_coupons'),
                      destination: "/PlansAndCoupons",
                    },
                    {
                      label: t('navigation.transfer_log'),
                      destination: "/TransferLog",
                    },
                    {
                      label: t('navigation.import'),
                      destination: "/Import",
                    },
                    {
                      label: t('navigation.help'),
                      destination: "/Help",
                    },
                  ]}
                  matcher={(link, location) => link.destination === location.pathname}
                />
                <EshopGuideProvider appService={appService}>
                  <Frame>
                    <Routes pages={pages} />
                  </Frame>
                </EshopGuideProvider>
              </QueryProvider>
            </ErrorBoundary>
          </AppBridgeProvider>
        </BrowserRouter>
      </ErrorBoundary>
    </PolarisProvider>
  );
}
