import { Routes as ReactRouterRoutes, Route } from "react-router-dom";

export default function Router({ pages, prefix = "" }) {
  const routes = useRoutes(pages, prefix);

  const routeComponents = routes.map(({ path, component: Component }) => (
    <Route key={path} path={path} element={<Component />} />
  ));

  return <ReactRouterRoutes>{routeComponents}</ReactRouterRoutes>;
}

function useRoutes(pages, prefix = "") {
  return Object.keys(pages)
    .map((key) => {
      let path = key
        .replace("./pages", "")
        .replace(/\.(t|j)sx?$/, "")
        .replace(/\/index$/i, "/")
        .replace(/\b[A-Z]/, (firstLetter) => firstLetter.toLowerCase())
        .replace(/\[(?:[.]{3})?(\w+?)\]/g, (_match, param) => `:${param}`);

      if (path.endsWith("/") && path !== "/") {
        path = path.substring(0, path.length - 1);
      }

      if (!pages[key].default) {
        console.warn(`${key} doesn't export a default React component`);
      }

      if (prefix) {
        path = `${prefix}${path}`;
      }

      return {
        path,
        component: pages[key].default,
      };
    })
    .filter((route) => route.component);
}
