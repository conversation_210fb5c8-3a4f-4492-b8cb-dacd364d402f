import React, { useCallback } from "react";
import {Modal} from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import YouTube from "react-youtube";

export default function IntroVideoModal({ active, setActive, videoId }) {
  const { t } = useTranslation();
  const handleChange = useCallback(() => setActive(!active), [active]);

  const video_opts = {
    height: '390',
    width: '580',
    playerVars: {
      autoplay: 1,
    },
  };

  return (
    <Modal open={active} onClose={handleChange}
           title={ t('help.main_video_title')}>
      <Modal.Section>
        <YouTube videoId={videoId} opts={video_opts} />
      </Modal.Section>
    </Modal>
  );
}
