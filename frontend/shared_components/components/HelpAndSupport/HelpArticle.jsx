import React, { useCallback, useEffect, useMemo, useState } from "react";
import {
  SkeletonBodyText,
  Banner,
  Card,
  Text,
  SkeletonDisplayText,
  BlockStack,
  Box,
} from "@shopify/polaris";
import { useAuthenticatedFetch } from "../../../admin/hooks";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import HelpscoutParser from "../../services/HelpscoutParser";

export const formatTitle = (title) => {
  const parts = title.split(':');

  if (parts.length > 1) {
    return parts.slice(1).join('').trim();
  }

  return title;
}

export default function HelpArticle({ active, setActive }) {
  const authenticatedFetch = useAuthenticatedFetch();
  const { t } = useTranslation();

  const handleChange = useCallback(() => setActive(!active), [active]);
  const [articleTitle, setArticleTitle] = useState("");
  const [articleBody, setArticleBody] = useState("");
  const [errors, setErrors] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { articleId } = useParams();

  useEffect(() => {
    async function fetchData() {
      setIsLoading(true);
      const response = await authenticatedFetch(
        `/api/help/article/${articleId}`,
        {
          method: "GET",
          headers: { "Content-Type": "application/json" },
        }
      );
      const data = await response.json();

      if (response.status === 404) {
        setErrors(true);
      } else {
        setArticleTitle(formatTitle(data.article.title));
        setArticleBody(HelpscoutParser.parseAndReplace(data.article.rawBody));
      }
      setIsLoading(false);
    }
    fetchData();
  }, [articleId]);

  return (
    <>
      <Card>
        {isLoading ? (
          <BlockStack gap={"200"}>
            <SkeletonDisplayText size="small" /> <SkeletonBodyText lines={20} />
          </BlockStack>
        ) : !errors ? (
          <>
            <Box paddingBlockEnd={"400"}>
              <Text variant="headingLg" as="h3">
                {articleTitle}
              </Text>
            </Box>
            {articleBody}
          </>
        ) : (
          <Banner tone="critical">
            <p>{t("help.errors.not_found.body")}</p>
          </Banner>
        )}
      </Card>
    </>
  );
}
