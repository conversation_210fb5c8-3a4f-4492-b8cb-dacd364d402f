import React, {useState} from "react";
import { InlineStack, Link } from "@shopify/polaris";
import {useTranslation} from "react-i18next";
import PreviewModal from "./PreviewModal.jsx";
import PlaceholderPopover from "./PlaceholderPopover.jsx";

export default function TextFieldPlaceholderAndPreview({textFieldId, textFieldValue, handleSettingsChange}) {
  const { t } = useTranslation();

  // Placeholders
  const [placeholderPopoverActive, setPlaceholderPopoverActive] = useState(false);
  const showPlaceholderPopover = () => { setPlaceholderPopoverActive(true) }
  const insertPlaceholder = (placeholder) => {
    const newText = (textFieldValue || '') + placeholder;
    handleSettingsChange(newText, textFieldId);
  };

  // Text Preview
  const [previewModalActive, setPreviewModalActive] = useState(false);
  const [previewModalText, setPreviewModalText] = useState("");
  const showModal = (text) => {
    setPreviewModalActive(true);
    setPreviewModalText(text);
  }

  return <>
    {previewModalActive && <PreviewModal text={previewModalText} active={previewModalActive} setActive={setPreviewModalActive} />}

    <InlineStack wrap={false} gap={"200"}>
      <Link onClick={() => showPlaceholderPopover()}>{t('settings.placeholders.title')}</Link>
      {placeholderPopoverActive && (
        <PlaceholderPopover
          active={placeholderPopoverActive}
          setActive={setPlaceholderPopoverActive}
          onSelectPlaceholder={insertPlaceholder}
        />
      )}
      <Link onClick={() => showModal(textFieldValue)}>{t('settings.liquid.preview')}</Link>
    </InlineStack>
  </>;
}
