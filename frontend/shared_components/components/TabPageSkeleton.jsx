import React from 'react'
import {Box, Card, Layout, SkeletonBodyText, SkeletonDisplayText, SkeletonTabs} from "@shopify/polaris";
import AnnotatedSkeleton from "@/shared_components/components/AnnotatedSkeleton";
export default function TabPageSkeleton({tabCount}) {
  const skeletonTabs = Array.from({ length: tabCount }, (_, i) => ({
    id: `tab${i + 1}`,
    content: <SkeletonBodyText lines={1} />,
    accessibilityLabel: `Tab ${i + 1}`,
    panelID: `tab${i + 1}-content`,
  }));

  return (
    <><SkeletonTabs fitted={true} count={tabCount} selected={0}/>
      <Box paddingBlockStart="100">
        <AnnotatedSkeleton cardCount={3}/>
      </Box></>
  )
}