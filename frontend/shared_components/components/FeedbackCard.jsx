import {<PERSON>, <PERSON>ton, ButtonGroup, Card, InlineStack, Layout, Text, BlockStack} from "@shopify/polaris";
import React, {useEffect, useState} from "react";
import {useTranslation} from "react-i18next";

export default function FeedbackCard ({reviewUrl, feedbackUrl}) {
  const { t } = useTranslation();
  const [showFeedbackCard, setShowFeedbackCard] = useState(false);

  useEffect(() => {
    try {
      if (sessionStorage.getItem('FeedbackCardDismissed') !== 'true') {
        setShowFeedbackCard(true);
      }
    } catch {
      setShowFeedbackCard(true);
    }
  }, []);

  const dismissFeedbackCard = () => {
    setShowFeedbackCard(false);
    try {
      sessionStorage.setItem('FeedbackCardDismissed', 'true');
    } catch {}
  };

  return showFeedbackCard &&
  <Banner onDismiss={dismissFeedbackCard} hideIcon>
    <InlineStack wrap="false" gap="200" align="center">
      <BlockStack align="center">
        <Text as="h3" variant="headingMd">
          {t("overview.feedback.heading")}
        </Text>
      </BlockStack>
      <ButtonGroup variant="segmented">
        <Button size="large" onClick={() => window.open(reviewUrl, "_blank").focus()}>
          😀
        </Button>
        <Button size="large" onClick={() => window.open(feedbackUrl, "_blank").focus()}>
          😐
        </Button>
        <Button size="large" onClick={() => window.open(feedbackUrl, "_blank").focus()}>
          😕
        </Button>
      </ButtonGroup>
    </InlineStack>
  </Banner>;
}