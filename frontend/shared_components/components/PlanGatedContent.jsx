import { useTranslation } from "react-i18next";
import {useEffect, useState} from "react";
import {Banner, SkeletonBodyText} from "@shopify/polaris";
import { useNavigate } from "react-router-dom";

export default function PlanGatedContent({ children, unlockIf, messageKey, buttonURL}) {

    const { t } = useTranslation();
    const navigate = useNavigate();

    const [loading, setLoading] = useState(true);
    const [blocked, setBlocked] = useState(true);

    useEffect(() => {
        setLoading(false);
        setBlocked(!unlockIf)
    },[unlockIf]);

    const containerStyle = {
        width: "100%",
        position: "relative",
        marginBottom: "1rem",
    };

    const overlayStyle = {
        display: "flex",
        zIndex: 100,
        alignItems: "center",
        position: "absolute",
        inset: 0,
        background: "rgba(255, 255, 255, 0.667)",
        borderRadius: "var(--p-border-radius-2)",
    };

    const contentStyle = {
        width: "80%",
        marginLeft: "10%",
        boxShadow: "rgba(0, 0, 0, 0.086) 0px 4px 5px",
        borderRadius: "calc(var(--p-border-radius-2) + 0.0625rem)",
    };

    /*TODO: korrekte PlanURL einfügen*/
    const activateAction = {
        content: t(`${messageKey}.needs_plan.action`),
        onAction: () => navigate(buttonURL),
        external: true
    }

    const gatedLayout = (<div style={containerStyle}>
        <div style={overlayStyle}>
            <div style={contentStyle}>
                <Banner title={t(`${messageKey}.needs_plan.title`)} tone={"warning"} action={activateAction}>
                    <p>{t(`${messageKey}.needs_plan.content`)}</p>
                </Banner>
            </div>
        </div>
        {children}
    </div>)

    return loading ? (
        <SkeletonBodyText></SkeletonBodyText>
    ) : blocked ? (
        gatedLayout
    ) : (
        children
    );
}
