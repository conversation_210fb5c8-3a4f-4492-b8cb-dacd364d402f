import React, {useEffect} from 'react';
import { useContextualSaveBar } from '@shopify/app-bridge-react';
import { useTranslation } from 'react-i18next';

export default function SaveBar({ visible, onSave, onDiscard }) {
  const { t } = useTranslation();
  const {show, hide, saveAction, discardAction} = useContextualSaveBar();

  useEffect(() => {
    if (visible) {
      show();
      discardAction.setOptions({onAction: () => onDiscard()});
      saveAction.setOptions({onAction: () => onSave()});
    }
    else {
      hide();
    }
  }, [visible, onSave, onDiscard]);

  return null
}
