import React, { useCallback, useEffect, useState } from "react";
import { Banner, Modal, SkeletonBodyText } from "@shopify/polaris";
import { useAuthenticatedFetch } from "../../admin/hooks";
import { useTranslation } from "react-i18next";

export default function PreviewModal({ text, active, setActive }) {
  const { t } = useTranslation();
  const handleChange = useCallback(() => setActive(!active), [active]);
  const [previewText, setPreviewText] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [errors, setErrors] = useState("");
  const [liquidErrors, setLiquidErrors] = useState(false);
  const authenticatedFetch = useAuthenticatedFetch();

  useEffect(() => {
    async function fetchData() {
      const response = await authenticatedFetch("/api/liquid_preview", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({text: text}),
      });
      const data = await response.json();
      setIsLoading(false);
      setLiquidErrors(data.liquid_error);
      setPreviewText(data.preview);
      setErrors(data.errors);
    }
    fetchData();
  });
  return (
    <Modal open={active} onClose={handleChange} title={t("settings.liquid.preview")}>
      <Modal.Section>
        {isLoading ? (
          <SkeletonBodyText />
        ) : (
          !liquidErrors && (
            <Banner tone="success">
              <p>{previewText}</p>
            </Banner>
          )
        )}
        {!isLoading && liquidErrors && (
          <Banner title={t("settings.liquid.error")} tone="critical">
            <p>{liquidErrors}</p>
          </Banner>
        )}
        {!isLoading && errors && (
          <Banner title={t("settings.liquid.error")} tone="critical">
            <p>{errors}</p>
          </Banner>
        )}
      </Modal.Section>
    </Modal>
  );
}
