import React from 'react';
import { <PERSON>, Banner} from "@shopify/polaris";
import {useTranslation} from "react-i18next";

export default function PlanActivationWarnings({shopStatus}) {
  const { t } = useTranslation();
  const {billing, service} = shopStatus;

  return (
    <Box paddingBlockStart="400" paddingBlockEnd="400">
      {!service.connected_to_service &&
        <Banner title={t('settings.setup_warning.service.title')}
                action={{content: t('settings.setup_warning.service.action'), url: '/'}}
                tone="warning">
          <p>{t('settings.setup_warning.service.description')}</p>
        </Banner>
      }

      {!billing.plan_active &&
        <Banner title={t('settings.setup_warning.plan.title')}
                action={{content: t('settings.setup_warning.plan.action'), url: '/PlansAndCoupons'}}
                tone="warning">
          <p>{t('settings.setup_warning.plan.description')}</p>
        </Banner>
      }
    </Box>
  );
}
