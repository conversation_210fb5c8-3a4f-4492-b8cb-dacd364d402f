import React, { useCallback} from "react";
import {Modal, TextContainer} from "@shopify/polaris";
import { useAuthenticatedFetch } from "../../admin/hooks/index.js";
import { useTranslation } from "react-i18next";
import { useToast } from '@shopify/app-bridge-react';

export default function DisconnectConfirmationModal({ disconnectPath, active, setActive, refetchShopInfo }) {
  const handleChange = useCallback(() => setActive(!active), [active]);
  const authenticatedFetch = useAuthenticatedFetch();
  const { t } = useTranslation();
  const { show } = useToast();

  const handleDisconnect = async () => {
    const response = await authenticatedFetch(disconnectPath, {
      method: "POST",
      headers: {"Content-Type": "application/json"}
    });
    const data = await response.json()

    handleChange()
    refetchShopInfo()
    data.success ? show(t('overview.service.disconnect.confirm.success')) : show(t('overview.service.disconnect.confirm.error'), { isError: true })
  }

  return (
    <Modal open={active}
           onClose={handleChange}
           title={t("overview.service.disconnect.confirm.title")}
           primaryAction={{
             content: t("overview.service.disconnect.confirm.action"), destructive: true, onAction: handleDisconnect
           }}
           secondaryActions={[
             {content: t("overview.service.disconnect.confirm.cancel"), onAction: handleChange}
           ]}
    >
      <Modal.Section>
        <TextContainer>
          <p>{t("overview.service.disconnect.confirm.text")}</p>
        </TextContainer>
      </Modal.Section>
    </Modal>
  );
}
