import { useCallback, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useAppQuery } from "./useAppQuery";

export function useOrderTags({ onSettingsChange, settings, type }) {
  const { t } = useTranslation();
  const [inputValue, setInputValue] = useState("");
  const { data: orderTags, isLoading: orderTagsLoading } = useAppQuery({
    url: "/api/order_tags",
  });
  const typeKey = type === "mail" ? "mail_exclusion_tags" : "order_exclusion_tags";

  const selectedOptions = useMemo(() => {
    return settings[typeKey]?.length > 0
      ? settings[typeKey].split(",")
      : [];
  }, [settings]);

  const autocompleteOptions = useMemo(() => {
    const availableTags = [
      ...new Set([
        ...(orderTags?.order_tags || []),
        ...(selectedOptions || []),
      ]),
    ];

    return availableTags.map((tag) => ({
      label: tag,
      value: tag,
    }));
  }, [selectedOptions, orderTags]);

  const onSelect = useCallback(
    (selectedOptions) => {
      onSettingsChange(selectedOptions.join(","), typeKey);
    },
    [onSettingsChange]
  );

  const addTag = useCallback(
    (value) => {
      const newTags = [...selectedOptions, value];
      onSettingsChange(newTags.join(","), typeKey);
      setInputValue("");
    },
    [selectedOptions, onSettingsChange]
  );

  const removeTag = useCallback(
    (option) => {
      const newTags = selectedOptions.filter((tag) => tag !== option);
      onSettingsChange(newTags.join(","), typeKey);
    },
    [selectedOptions, onSettingsChange]
  );

  return {
    inputValue,
    setInputValue,
    orderTagsLoading,
    selectedOptions,
    autocompleteOptions,
    onSelect,
    addTag,
    removeTag,
  };
}
