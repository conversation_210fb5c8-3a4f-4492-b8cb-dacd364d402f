import React, { useState, useEffect } from 'react';
import {
    Card, Layout,
    ProgressBar, Text
} from "@shopify/polaris";
import cable from '../../services/cable';
import {Fade, Slide} from "react-awesome-reveal";
import {useTranslation} from "react-i18next";

const ImportProgress = ({ jobId, importComplete }) => {
    const {t} = useTranslation();

    const [progress, setProgress] = useState(0);
    const [message, setMessage] = useState(t("import.running"));

    useEffect(() => {
        const subscription = cable.subscriptions.create(
            { channel: 'JobStatusChannel', job_id: jobId },
            {
                received: (data) => {
                    setProgress(data.progress);
                    setMessage(data.status);
                    if (data.progress >= 100) {
                        importComplete(data);
                    }
                },
            }
        );

        return () => {
            cable.subscriptions.remove(subscription);
        };
    }, [jobId]);

    return (
        <Fade duration={500}><Slide direction={"left"} duration={500}> <Card>
            <Text as={"h2"} variant={"headingMd"}>{message}</Text>
            <ProgressBar animated={true} tone="primary"  progress={progress} size={"large"}></ProgressBar>
        </Card><Layout.Section/></Slide></Fade>
    );
};

export default ImportProgress;