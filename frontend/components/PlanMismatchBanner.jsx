import { useNavigate } from "react-router-dom";
import { useContext } from "react";
import { useTranslation } from "react-i18next";
import { Banner } from "@shopify/polaris";
import { EshopGuideContext } from "../shared/providers/EshopGuideProvider";

export default function PlanMismatchBanner({ showAction = false }) {
  const { shopInfo } = useContext(EshopGuideContext);
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { plan_mismatch_since, billing_plan_name } = shopInfo?.billing || {};

  if (!plan_mismatch_since) {
    return null;
  }

  return (
    <Banner
      title={t("billing.planMismatchBanner.title")}
      tone="warning"
      action={
        showAction && {
          content: t("overview.plan.go_to_plan"),
          onAction: () => {
            navigate("/PlansAndCoupons");
          },
        }
      }
    >
      {t("billing.planMismatchBanner.text", {
        currentBillingPlanName: billing_plan_name,
      })}
    </Banner>
  );
}
