import { createContext, useContext } from "react";
import useSessionStorage from "../../hooks/useSessionStorage";

export const ViewModeContext = createContext();

export function ViewModeProvider({ children }) {
  const [isDetailView, setIsDetailView] = useSessionStorage(
    "isDetailView",
    false
  );

  return (
    <ViewModeContext.Provider value={{ isDetailView, setIsDetailView }}>
      {children}
    </ViewModeContext.Provider>
  );
}

export function useViewMode() {
  const { isDetailView, setIsDetailView } = useContext(ViewModeContext);
  return { isDetailView, setIsDetailView };
}
