import { Banner, BlockStack } from "@shopify/polaris";
import useSessionStorage from "../hooks/useSessionStorage";

export const DismissibleBanner = ({ storageKey, title, description, children, ...props }) => {
  const [dismissed, setDismissed] = useSessionStorage(storageKey, false);

  if (!dismissed) {
    return (
      <Banner onDismiss={() => setDismissed(true)} {...props} title={title}>
        <BlockStack gap="200" inlineAlign="start">
          <div dangerouslySetInnerHTML={{ __html: description }} />
          {children}
        </BlockStack>
      </Banner>
    );
  }

  return [];
};