import React, { useContext, useState } from "react";
import {
  Banner,
  Button,
  Image,
  Text,
  InlineGrid,
  BlockStack,
  Collapsible,
  TextField,
  InlineStack,
  Card,
} from "@shopify/polaris";
import { plansAndCoupons } from "~/assets";
import { useTranslation } from "react-i18next";
import { useAppQuery } from "@/hooks";
import BeaconMessages from "@/shared/components/BeaconMessages";
import { useToast } from "@shopify/app-bridge-react";
import { EshopGuideContext } from "~/shared/providers/EshopGuideProvider";
import { useAppMutation } from "../hooks/useAppMutation";
import { PlansAndCouponsContext } from "../pages/PlansAndCoupons";

export default function CouponsCard() {
  const { activeCouponCode, setActiveCouponCode } = useContext(
    PlansAndCouponsContext
  );
  const { shopInfo } = useContext(EshopGuideContext);
  const { billing } = shopInfo || {};
  const { t } = useTranslation();
  const [couponCode, setCouponCode] = useState("");
  const [couponCodeAreaOpen, setCouponCodeAreaOpen] = useState(false);
  const { data: beaconMessagesData } = useAppQuery({
    url: "/api/beacon_messages?domain=plans",
  });

  const { mutate: checkCoupon, isLoading } = useAppMutation({
    url: "/shopify_billing/billing/check_coupon",
  });
  const { show } = useToast();

  const onSubmit = () => {
    checkCoupon(
      {
        coupon_code: couponCode,
      },
      {
        onSuccess: () => {
          setActiveCouponCode(couponCode);
          show(t("billing.coupon.success"));
          setCouponCode("");
        },
        onError: () => {
          show(t("billing.coupon.error"), {
            isError: true,
          });
          setCouponCode("");
        },
      }
    );
  };

  return (
    <Card>
      <BlockStack gap="400">
        <Text variant="headingMd">{t("billing.coupon.title")}</Text>
        <Text>{t("billing.coupon.description")}</Text>
        <InlineStack gap="200">
          <TextField
            placeholder={t("billing.coupon.placeholder")}
            monospaced={true}
            label=""
            value={couponCode}
            onChange={setCouponCode}
            autoComplete="off"
          />
          <Button onClick={onSubmit} disabled={!couponCode}>
            {t("billing.coupon.button")}
          </Button>
        </InlineStack>
      </BlockStack>
    </Card>
  );
}
