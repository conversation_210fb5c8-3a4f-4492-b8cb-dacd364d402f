import React from "react";
import { Autocomplete, BlockStack, TextField } from "@shopify/polaris";
import { CirclePlusMinor } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";
import TagList from "./TagList";
import { useOrderTags } from "../hooks/useOrderTags";

export default function OrderTagsInput({ onSettingsChange, settings, type }) {
  const { t } = useTranslation();
  const {
    inputValue,
    setInputValue,
    orderTagsLoading,
    selectedOptions,
    autocompleteOptions,
    onSelect,
    addTag,
    removeTag,
  } = useOrderTags({ onSettingsChange, settings, type });
  const transalationKey = type === "mail" ? "mailing.mail_exclusion" : "invoices.invoices_exclusion";

  if (orderTagsLoading) {
    return (
      <TextField disabled label={t(`settings.${transalationKey}.label`)} />
    );
  }

  return (
    <BlockStack gap="200">
      <Autocomplete
        actionBefore={
          inputValue.length > 0 && {
            accessibilityLabel: "Add tag",
            content: `${t(
              `settings.${transalationKey}.add`
            )} ${inputValue}`,
            icon: CirclePlusMinor,
            onAction: () => addTag(inputValue),
          }
        }
        allowMultiple
        options={autocompleteOptions}
        selected={selectedOptions}
        textField={
          <Autocomplete.TextField
            onChange={(input) => setInputValue(input)}
            label={t(`settings.${transalationKey}.label`)}
            value={inputValue}
            autoComplete="off"
          />
        }
        onSelect={onSelect}
        listTitle={
          autocompleteOptions.length > 0 &&
          t(`settings.${transalationKey}.suggestions`)
        }
      />
      <TagList selected={selectedOptions} removeTag={removeTag} />
    </BlockStack>
  );
}
