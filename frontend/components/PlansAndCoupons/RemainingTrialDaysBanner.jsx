import { useTranslation } from "react-i18next";
import { useContext } from "react";

import { DismissibleBanner } from "../DismissibleBanner";
import { EshopGuideContext } from "~/shared/providers/EshopGuideProvider";

export function RemainingTrialDaysBanner() {
  const { t } = useTranslation();
  const { shopInfo } = useContext(EshopGuideContext);

  if (shopInfo?.billing?.remaining_trial_days > 0) {
    const storageKey = `remainingTrialDays_${shopInfo.shop_domain}_${shopInfo.billing.trial_ends_on}`;

    return (
      <DismissibleBanner
        storageKey={storageKey}
        description={t("billing.remainingTrialDays", {
          days: shopInfo?.billing?.remaining_trial_days,
        })}
      />
    );
  }
}
