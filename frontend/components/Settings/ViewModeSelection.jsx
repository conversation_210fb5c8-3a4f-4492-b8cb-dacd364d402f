import React from "react";
import { Button, ButtonGroup } from "@shopify/polaris";
import { useViewMode } from "../providers/ViewModeProvider";
import { useTranslation } from "react-i18next";

export default function ViewModeSelection() {
  const { isDetailView, setIsDetailView } = useViewMode();
  const { t } = useTranslation();

  return (
    <>
      <ButtonGroup variant="segmented">
        <Button pressed={isDetailView} onClick={() => setIsDetailView(true)}>
          {t("viewMode.detail")}
        </Button>
        <Button pressed={!isDetailView} onClick={() => setIsDetailView(false)}>
          {t("viewMode.simple")}
        </Button>
      </ButtonGroup>
    </>
  );
}
