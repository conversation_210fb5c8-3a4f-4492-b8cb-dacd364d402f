import React from "react";
import {Card, Layout, FormLayout, Checkbox, ChoiceList, Text, Link, Button} from "@shopify/polaris";
import {useTranslation} from "react-i18next";
import ReactHtmlParser from 'react-html-parser';
import BeaconMessages from "~/shared/components/BeaconMessages";
import { useViewMode } from "../providers/ViewModeProvider";
import {DismissibleBanner} from "../DismissibleBanner";

/*
  General settings Tab, including settings for POS, shipping tax, create customer and invoice language choice
 */
export default function GeneralSettings({shopSettings, onSettingsChange, beaconMessages}) {
  const { t } = useTranslation();
  const { isDetailView } = useViewMode();

  return <>
    <Layout>
      <Layout.AnnotatedSection title={<BeaconMessages beacons={beaconMessages} title={t('settings.general.title')}/>}
                               description={t('settings.general.description')}>
        <Card>
          <FormLayout>
            <Text variant="headingMd" as="h2">{t('settings.general.tax_settings.title')}</Text>
            <Checkbox id={'confirm_tax_settings'}
                      label={t('settings.general.tax_settings.label')}
                      checked={shopSettings.confirm_tax_settings}
                      onChange={onSettingsChange}
                      helpText={
                        <>
                          {ReactHtmlParser(t('settings.general.tax_settings.help'))}
                          <Link monochrome={false} removeUnderline={true} onClick={() => window.Beacon('article', '359')}>
                            {t('settings.general.tax_settings.click')}
                          </Link>
                        </>
                      }
            />
            <DismissibleBanner storageKey="taxSettingsBanner"
                               description={t("overview.tax_banner.description")}
                               status={"info"}
                               children={
                                 <Button onClick={() => {window.Beacon ? window.Beacon('article', '401') : window.Beacon('navigate', '/ask/message')}}>
                                   {t("overview.tax_banner.action")}
                                 </Button>
                               }
            />
          </FormLayout>
        </Card>

        <Layout.Section/>{/* Empty section to add some space */}

        <Card>
          <FormLayout>
            <Text variant="headingMd" as="h2">{t('settings.general.pos.title')}</Text>
            <Checkbox id={'excludePOS'}
                      label={t('settings.general.pos.label')}
                      checked={shopSettings.excludePOS}
                      onChange={onSettingsChange}
                      helpText={ReactHtmlParser(t('settings.general.pos.help'))}
            />
          </FormLayout>
        </Card>

        <Layout.Section variant="oneThird"></Layout.Section>

        <Layout.Section/>{/* Empty section to add some space */}

        {isDetailView && (
          <Card>
            <FormLayout>
              <Text variant="headingMd" as="h2">{t('settings.general.shipping_tax.title')}</Text>
              <Checkbox id={'calculate_shipping_tax'}
                        label={t('settings.general.shipping_tax.label')}
                        checked={shopSettings.calculate_shipping_tax}
                        onChange={onSettingsChange}
                        helpText={ReactHtmlParser(t('settings.general.shipping_tax.help'))}
              />
            </FormLayout>
          </Card>
        )}

        <Layout.Section/>{/* Empty section to add some space */}

        {isDetailView && (
          <Card>
            <FormLayout>
              <Text variant="headingMd" as="h2">{t('settings.general.lexoffice_customer.title')}</Text>
              <Checkbox id={"create_customer"}
                        label={t('settings.general.lexoffice_customer.label')}
                        checked={shopSettings.create_customer}
                        onChange={onSettingsChange}
                        helpText={t('settings.general.lexoffice_customer.help')}
              />
            </FormLayout>
          </Card>
        )}

        <Layout.Section/>{/* Empty section to add some space */}

        <Card>
          <FormLayout>
            <Text variant="headingMd" as="h2">{t('settings.general.language.title')}</Text>
            <ChoiceList choices={[
                          {label: t('settings.general.language.choices.de'), value: 'de'},
                          {label: t('settings.general.language.choices.en'), value: 'en'}
                        ]}
                        selected={[shopSettings.invoice_language]}
                        onChange={(value) => onSettingsChange(value[0], "invoice_language")}
            />
          </FormLayout>
        </Card>
      </Layout.AnnotatedSection>
    </Layout>
  </>;
}
