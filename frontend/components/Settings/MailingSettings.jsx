import React from "react";
import {Card, Checkbox, FormLayout, Layout} from "@shopify/polaris";
import CustomTextField from "../CustomTextField";
import TextFieldPlaceholderAndPreview from "../../shared/components/TextFieldPlaceholderAndPreview";
import OrderTagsInput from "../OrderTagsInput";
import {useTranslation} from "react-i18next";
import Paywall from "../Paywall";

export default function MailingSettings({shopSettings, onSettingsChange}) {
    const { t } = useTranslation();
    return (
        <>
        <Layout>


            <Layout.AnnotatedSection title={t('settings.mailing.invoice_mail_text.title')}
                                     description={t('settings.mailing.invoice_mail_text.description')}>
                <Card>
                    <FormLayout>
                        <Checkbox id={'send_invoice_mail'}
                                  label={t("settings.mailing.invoice_mail_text.enabled_label")}
                                  checked={shopSettings.send_invoice_mail}
                                  onChange={onSettingsChange}
                                  helpText={t('settings.mailing.invoice_mail_text.enabled_help')}
                        />
                        <CustomTextField id={'invoice_mail_offset_days'}
                                         label={t("settings.mailing.invoice_mail_text.offset_days_label")}
                                         type="number"
                                         value={shopSettings.invoice_mail_offset_days || 0}
                                         onChange={onSettingsChange}
                                         prefix={t("settings.mailing.invoice_mail_text.offset_days_prepend")}
                                         suffix={t("settings.mailing.invoice_mail_text.offset_days_append")}
                                         autoComplete="off"
                                         min={0}
                                         max={90}
                        />
                    </FormLayout>
                </Card>
            </Layout.AnnotatedSection>

            <Layout.AnnotatedSection title={t('settings.mailing.invoice_mail_card.title')}
                                     description={t('settings.mailing.invoice_mail_card.description')}>
                <Card>
                    <FormLayout>

                        <CustomTextField id={'invoice_mail_subject'}
                                         label={t("settings.mailing.invoice_mail_text.subject_label")}
                                         labelAction={{
                                             content: (<TextFieldPlaceholderAndPreview textFieldId="invoice_mail_subject"
                                                                                       textFieldValue={shopSettings.invoice_mail_subject}
                                                                                       handleSettingsChange={onSettingsChange}/>)
                                         }}
                                         value={shopSettings.invoice_mail_subject}
                                         onChange={onSettingsChange}
                                         autoComplete="off"
                                         helpText={t('settings.mailing.invoice_mail_text.subject_help')}
                        />
                        <CustomTextField id={'invoice_mail_body'}
                                         label={t("settings.mailing.invoice_mail_text.body_label")}
                                         labelAction={{
                                             content: (<TextFieldPlaceholderAndPreview textFieldId="invoice_mail_body"
                                                                                       textFieldValue={shopSettings.invoice_mail_body}
                                                                                       handleSettingsChange={onSettingsChange}/>)
                                         }}
                                         value={shopSettings.invoice_mail_body}
                                         onChange={onSettingsChange}
                                         maxLength={1800}
                                         multiline={4}
                                         autoComplete="off"
                                         showCharacterCount
                                         helpText={t('settings.mailing.invoice_mail_text.body_help')}
                        />
                    </FormLayout>
                </Card>
            </Layout.AnnotatedSection>

            <Layout.AnnotatedSection title={t('settings.mailing.refund_mail_card.title')}
                                     description={t('settings.mailing.refund_mail_card.description')}>
                <Card>
                    <FormLayout>
                        <CustomTextField id={'credit_note_mail_subject'}
                                         value={shopSettings.credit_note_mail_subject || ''}
                                         label={t('settings.mailing.refund_mail_card.subject_label')}
                                         labelAction={{
                                             content: (<TextFieldPlaceholderAndPreview textFieldId="credit_note_mail_subject"
                                                                                       textFieldValue={shopSettings.credit_note_mail_subject}
                                                                                       handleSettingsChange={onSettingsChange}/>)
                                         }}
                                         onChange={onSettingsChange}
                                         helpText={t('settings.mailing.refund_mail_card.subject_help')}
                        />
                        <CustomTextField id={'credit_note_mail_body'}
                                         value={shopSettings.credit_note_mail_body || ''}
                                         label={t('settings.mailing.refund_mail_card.body_label')}
                                         labelAction={{
                                             content: (<TextFieldPlaceholderAndPreview textFieldId="credit_note_mail_body"
                                                                                       textFieldValue={shopSettings.credit_note_mail_body}
                                                                                       handleSettingsChange={onSettingsChange}/>)
                                         }}
                                         multiline={4}
                                         maxLength={1800}
                                         showCharacterCount
                                         onChange={onSettingsChange}
                                         helpText={t('settings.mailing.refund_mail_card.body_help')}
                        />
                    </FormLayout>
                </Card>
            </Layout.AnnotatedSection>

            <Layout.AnnotatedSection title={t('settings.mailing.mail_exclusion.title')}
                                     description={t('settings.mailing.mail_exclusion.description')}
            >
                <Paywall feature="exclude_emails_by_tag">
                    <Card>
                        <FormLayout>
                            <OrderTagsInput onSettingsChange={onSettingsChange} settings={shopSettings} type={'mail'}/>
                        </FormLayout>
                    </Card>
                </Paywall>
            </Layout.AnnotatedSection>
        </Layout>
        </>
    );
}