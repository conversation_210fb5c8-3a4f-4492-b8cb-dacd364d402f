import React from "react";
import {Layout, Card, FormLayout, Text, Checkbox, OptionList, InlineStack} from "@shopify/polaris";
import {useTranslation} from "react-i18next";
import ReactHtmlParser from 'react-html-parser';
import BeaconMessages from "~/shared/components/BeaconMessages";

/*
  Payment data settings tab
 */
export default function PaymentDataSettings({shopSettings, transactionSettings, onSettingsChange, beaconMessages}) {
  const { t } = useTranslation();
  const isTransactionsEnabled = shopSettings.enable_tender_transactions;
  const extraAccounts = Object.keys(transactionSettings.extra_accounts_info).filter(function (str) { return str.includes('enable_'); });
  const handleTenderTransactionsChange = (value) => {
    onSettingsChange(value, "enable_tender_transactions");

    if (!value) {
      onSettingsChange(false, "enable_tah_creation");

      const keys = Object.keys({...transactionSettings, ...transactionSettings.extra_accounts_info}).filter(function (str) { return str.includes('enable_'); });

      for (const key of keys) {
        onSettingsChange(false, key, true);
      }
    }
  };

  return <>
    <Layout>
      <Layout.AnnotatedSection title={<BeaconMessages beacons={beaconMessages}
                                                      title={t('settings.payment_data.title')}/>}
                               description={t('settings.payment_data.description')}>
        <Card>
          <FormLayout>
            <Text variant="headingSm" as="h2"> {t('settings.payment_data.sync_card.title')} </Text>
            <Checkbox id={'enable_tender_transactions'}
                      label={t('settings.payment_data.sync_card.label')}
                      checked={isTransactionsEnabled}
                      onChange={handleTenderTransactionsChange}
                      helpText={t('settings.payment_data.sync_card.help')}
            />
          </FormLayout>
        </Card>

        <Layout.Section/>{/* Empty section to add some space */}

        <Card>
          <FormLayout>
            <Text variant="headingSm" as="h2"> {t('settings.payment_data.tah_card.title')} </Text>
            <Checkbox id={'enable_tah_creation'}
                      label={t('settings.payment_data.tah_card.label')}
                      checked={isTransactionsEnabled && shopSettings.enable_tah_creation}
                      disabled={!isTransactionsEnabled}
                      onChange={onSettingsChange}
                      helpText={ReactHtmlParser(t('settings.payment_data.tah_card.help'))}
            />
          </FormLayout>
        </Card>

          <Layout.Section/>{/* Empty section to add some space */}
          <Card>
            <FormLayout>
              <Text variant="headingSm" as="h2"> {t('settings.payment_data.account_card.title')} </Text>
              <Checkbox id={'enable_amazon'}
                        label={t('settings.payment_data.account_card.amazon')}
                        checked={isTransactionsEnabled && transactionSettings.enable_amazon}
                        disabled={!isTransactionsEnabled}
                        onChange={(value) => {onSettingsChange(value, "enable_amazon", true)}}
              />
              <Checkbox id={'enable_apple_pay'}
                        label={t('settings.payment_data.account_card.apple_pay')}
                        checked={isTransactionsEnabled && transactionSettings.enable_apple_pay}
                        disabled={!isTransactionsEnabled}
                        onChange={(value) => onSettingsChange(value, "enable_apple_pay", true)}
              />
              <Checkbox id={'enable_credit_card'}
                        label={t('settings.payment_data.account_card.credit_card')}
                        checked={isTransactionsEnabled && transactionSettings.enable_credit_card}
                        disabled={!isTransactionsEnabled}
                        onChange={(value) => onSettingsChange(value, "enable_credit_card", true)}
              />
              <Checkbox id={'enable_google_pay'}
                        label={t('settings.payment_data.account_card.google_pay')}
                        checked={isTransactionsEnabled && transactionSettings.enable_google_pay}
                        disabled={!isTransactionsEnabled}
                        onChange={(value) => onSettingsChange(value, "enable_google_pay", true)}
              />
              <Checkbox id={'enable_klarna'}
                        label={t('settings.payment_data.account_card.klarna')}
                        checked={isTransactionsEnabled && transactionSettings.enable_klarna}
                        disabled={!isTransactionsEnabled}
                        onChange={(value) => onSettingsChange(value, "enable_klarna", true)}
              />
              <Checkbox id={'enable_klarna_pay_later'}
                        label={t('settings.payment_data.account_card.klarna_pay_later')}
                        checked={isTransactionsEnabled && transactionSettings.enable_klarna_pay_later}
                        disabled={!isTransactionsEnabled}
                        onChange={(value) => onSettingsChange(value, "enable_klarna_pay_later", true)}
              />
              <Checkbox id={'enable_paypal'}
                        label={t('settings.payment_data.account_card.paypal') + ' ¹'}
                        checked={false}
                        disabled={true}
                        onChange={() => {}}
              />
              <Checkbox id={'enable_samsung_pay'}
                        label={t('settings.payment_data.account_card.samsung_pay')}
                        checked={isTransactionsEnabled && transactionSettings.enable_samsung_pay}
                        disabled={!isTransactionsEnabled}
                        onChange={(value) => onSettingsChange(value, "enable_samsung_pay", true)}
              />
              <Checkbox id={'enable_shopify_pay'}
                        label={t('settings.payment_data.account_card.shopify_pay')}
                        checked={isTransactionsEnabled && transactionSettings.enable_shopify_pay}
                        disabled={!isTransactionsEnabled}
                        onChange={(value) => onSettingsChange(value, "enable_shopify_pay", true)}
              />
              <Checkbox id={'enable_sofort'}
                        label={t('settings.payment_data.account_card.sofort')}
                        checked={isTransactionsEnabled && transactionSettings.enable_sofort}
                        disabled={!isTransactionsEnabled}
                        onChange={(value) => onSettingsChange(value, "enable_sofort", true)}
              />

              
              <InlineStack wrap={false} gap={'100'}>
                <Text as={'p'} tone={'subdued'}>{'¹'}</Text>
                <Text as={'p'} tone={'subdued'}>{t('settings.payment_data.account_card.paypal_tooltip')}</Text>
              </InlineStack>
            </FormLayout>
          </Card>

          <Layout.Section/>{/* Empty section to add some space */}

          <Card>
            <FormLayout>
              <Text variant="headingSm" as="h2"> {t('settings.payment_data.extra_payments_card.title')} </Text>

              <Checkbox id={'enable_other'}
                        label={t('settings.payment_data.extra_payments_card.label')}
                        checked={isTransactionsEnabled && transactionSettings.enable_other}
                        disabled={!isTransactionsEnabled}
                        onChange={(value) => onSettingsChange(value, "enable_other", true, false)}
                        helpText={t('settings.payment_data.extra_payments_card.help')}
              />

              {extraAccounts.map((account) => {
                return (
                    <Checkbox id={account}
                              label={ account.split('enable_')[1] }
                              checked={isTransactionsEnabled && transactionSettings.extra_accounts_info[account] &&
                                  transactionSettings.enable_other}
                              disabled={!isTransactionsEnabled || !transactionSettings.enable_other}
                              onChange={(value) => onSettingsChange(value, account, true, true)}
                    />
                );
              })}
            </FormLayout>
          </Card>
        </Layout.AnnotatedSection>
      </Layout>
    </>
}
