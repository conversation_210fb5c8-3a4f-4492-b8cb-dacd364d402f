import React, { useCallback, useState, useEffect, useMemo } from "react";
import { Card, FormLayout, Link, Select } from "@shopify/polaris";
import { Trans } from "react-i18next";
import { useTranslation } from "react-i18next";
import useShopSettings from "../../hooks/useShopSettings";
import Paywall from '../Paywall';

export default function PrintLayoutsSettings({ onSettingsChange, type }) {
  const { t } = useTranslation();
  const { data: shopSettingsData } = useShopSettings();
  const [selected, setSelected] = useState();
  const layoutIdKey =
    type === "refunds" ? "refund_layout_id" : "invoice_layout_id";

  const options = useMemo(() => {
    const printLayouts = shopSettingsData?.layouts || [];

    return printLayouts.map((layout) => {
      return { label: layout.name, value: layout.id };
    });
  }, [shopSettingsData]);

  useEffect(() => {
    if (shopSettingsData?.shop_setting) {
      setSelected(shopSettingsData?.shop_setting[layoutIdKey]);
    }
  }, [shopSettingsData]);

  const handleLayoutSelectChange = useCallback((value) => {
    setSelected(value);
    onSettingsChange(value, layoutIdKey);
  }, []);

  return (
    <Paywall feature="custom_layouts">
      <Card>
        <FormLayout>
          <Select
            label={t(`settings.${type}.layouts.select_label`)}
            options={options}
            onChange={handleLayoutSelectChange}
            value={selected}
            disabled={options.length === 0}
            helpText={
              <Trans
                i18nKey={`settings.${type}.layouts.select_help`}
                components={{
                  a: (
                    <Link
                      target="_blank"
                      url="https://support.lexoffice.de/de-form/articles/6205938-mehrere-layouts-erstellen-und-verwenden"
                    />
                  ),
                }}
              />
            }
          />
        </FormLayout>
      </Card>
    </Paywall>
  );
}
