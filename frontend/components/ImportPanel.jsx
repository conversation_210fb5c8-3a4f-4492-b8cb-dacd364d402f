import {
  Layout, <PERSON>ton<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, InlineStack, Spinner, Loading, Checkbox,
  FormLayout, Text, Link, Banner, Card, Box
} from "@shopify/polaris";
import React, {useCallback, useContext, useState, useEffect} from "react";

import {useTranslation} from "react-i18next";
import {EshopGuideContext} from "../shared/providers/EshopGuideProvider.jsx";
import { useAppQuery, useAuthenticatedFetch } from "../hooks";
import {useNavigate, useToast} from "@shopify/app-bridge-react";
import DateRangePicker from "../components/Import/DateRangePicker.jsx";
import ImportProgress from "./Import/ImportProgress.jsx";
import CompletedImport from "./Import/CompletedImport.jsx";
import PlanGatedContent from "../shared/components/PlanGatedContent.jsx";
import ConnectionGatedContent from '../components/ConnectionGatedContent';
import i18next from "i18next";
import ReactHtmlParser from "react-html-parser";
import BeaconMessages from "@/shared/components/BeaconMessages";

export function ImportPanel() {
  const { t } = useTranslation();
  const {shopInfo, appService} = useContext(EshopGuideContext);
  const [importPeriod, setImportPeriod] = useState({period: {since: null, until: null}});

  const [invoices, setInvoices] = useState(false);
  const [refunds, setRefunds] = useState(false);
  const [transactions, setTransactions] = useState(false);

  const [docCount, setDocCount] = useState({orders: null, refunds: null, transactions: null});
  const [countLoading, setCountLoading] = useState(false);
  const [jobId, setJobId] = useState(null);

  const [importRunning, setImportRunning] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    setImportRunning(shopInfo?.import?.import_running);
  },[shopInfo]);

  const [completedImports, setCompletedImports] = useState([]);

  const authenticatedFetch = useAuthenticatedFetch();
  const {show} = useToast();

  const handleDateChange = useCallback((newValue) => {
    setImportPeriod(newValue)
  }, []);

  const [showFairUsageNote, setShowFairUsageNote] = useState(false);
  useEffect(() => {
    try {
      if (sessionStorage.getItem('FairUsageNoteDismissed') !== 'true') {
        setShowFairUsageNote(true);
      }
    } catch {
      setShowFairUsageNote(true);
    }
  }, []);

  const dismissFairUsageNote = () => {
    setShowFairUsageNote(false);
    try {
      sessionStorage.setItem('FairUsageNoteDismissed', 'true');
    } catch {}
  };

  const importButtonEnabled = () => {
    const anyDocsToTransfer = invoices && docCount.orders > 0 || refunds && docCount.refunds > 0 || transactions && docCount.transactions > 0;
    return anyDocsToTransfer && !importRunning && !countLoading;
  }

  const {
    data: beaconMessagesData
  } = useAppQuery({ url: '/api/beacon_messages?domain=import' });

  // Count the number of selected documents
  useEffect(() => {
    (async () => {
      if (!importPeriod.period.since) return;
      setCountLoading(true);
      // recount the number of selected documents
      const response = await authenticatedFetch('/api/import/count', {
        method:'POST',
        body: JSON.stringify({
          start_date: importPeriod.period.since,
          end_date: importPeriod.period.until,
          invoices: invoices,
          refunds: refunds,
          transactions: transactions
        })
      });
      const data = await response.json();
      setCountLoading(false);
      if (data.success) {
        setDocCount({orders: data.orders, refunds: data.refunds, transactions: data.transactions});
      } else {
        show(t("notifications.queued_error"),{
          duration: 3000,
          isError: true
        });
      }
    })();
  }, [importPeriod]);

  const startImport = useCallback(async () => {
    const response = await authenticatedFetch('/api/import/start', {
      method:'POST',
      headers: {
        'locale': i18next.language || 'en'
      },
      body: JSON.stringify({
        start_date: importPeriod.period.since,
        end_date: importPeriod.period.until,
        invoices: invoices,
        refunds: refunds,
        transactions: transactions
      })
    });
    const data = await response.json();
    if (data.success) {
      setImportRunning(true);
      setJobId(data.job_id);
      show(t("import.starting_import"),{
        duration: 3000,
        isError: false
      });
    } else {
      show(t("notifications.queued_error"),{
        duration: 3000,
        isError: true
      });
    }
  }, [importPeriod, invoices, refunds, transactions]);

  const importComplete = useCallback((data) => {
    setImportRunning(false);
    setJobId(null);
    if (data.success) {
      show(t('import.success'), { duration: 5000, isError: false });
    } else {
      show(t('import.error'), { duration: 5000, isError: true });
    }
    setCompletedImports([...completedImports, data]);
  }, [jobId]);

  return (
    <div>
      {shopInfo && beaconMessagesData ? (
        <Layout>
          <Layout.AnnotatedSection
            title={<BeaconMessages beacons={beaconMessagesData.beacon_messages} domain="import" title={t('import.help_caption')} />}
            description={ReactHtmlParser(t('import.help_text'))}>
            <ConnectionGatedContent 
              isConnected={shopInfo.service.connected_to_service}
              messageKey="import.needs_connection"
            >
              <PlanGatedContent messageKey={"import"} unlockIf={shopInfo.billing.import_unlocked} buttonURL={"/PlansAndCoupons"} >
                <Card>
                  <Box>
                    <FormLayout>
                      <FormLayout.Group>
                        <DateRangePicker onChange={handleDateChange}></DateRangePicker>
                      </FormLayout.Group>
                      <Divider/>
                      <InlineStack gap={"100"} wrap={false}>
                        <Text as="p" variant="headingSm" alignment="start"> {t("import.doc_types")} </Text>
                      </InlineStack>
                      <InlineStack gap={"100"} wrap={false}>
                        <Checkbox
                          label={t("import.invoices")}
                          checked={invoices}
                          onChange={setInvoices}
                        />{docCount.orders != null && <Badge>{docCount.orders}</Badge>}
                      </InlineStack>
                      <InlineStack gap={"100"} wrap={false}>
                        <Checkbox
                          label={t("import.refunds")}
                          checked={refunds}
                          onChange={setRefunds}
                        />{docCount.refunds != null && <Badge>{docCount.refunds}</Badge>}
                      </InlineStack>
                      <InlineStack gap={"100"} wrap={false}>
                        <Checkbox
                          label={t("import.transactions")}
                          checked={transactions}
                          onChange={setTransactions}
                          disabled={docCount.transactions == 0}
                          helpText={docCount.transactions == 0 ?
                              <Banner tone="warning">{t("import.no_transactions")} <Link onClick={() => {
                                navigate(`/shopSettings`)
                              }}>
                                {t("settings.page_title")}
                              </Link> {(t("import.do"))}</Banner>
                              : null}
                        />{docCount.transactions != null && docCount.transactions > 0 &&
                            <Badge>{docCount.transactions > 250 ? '250+' : docCount.transactions}</Badge>}
                      </InlineStack>
                      <Divider/>
                      <ButtonGroup>
                        <Button

                          disabled={!importButtonEnabled()}
                          onClick={startImport}
                          variant="primary"
                          loading={countLoading}
                          >
                          {countLoading ? t("import.counting") : t("import.start")}
                        </Button>
                      </ButtonGroup>
                    </FormLayout>
                  </Box>

                  {showFairUsageNote && (
                    <Box paddingBlockStart={"200"}>
                      <Banner tone="info" onDismiss={dismissFairUsageNote}>
                        {ReactHtmlParser(t("import.fair_use"))}
                      </Banner>
                    </Box>
                  )}

                  {(<Box>
                    <Text as="h2" variant="bodySm">
                      
                    </Text>
                  </Box>)}
                </Card>
              </PlanGatedContent>
            </ConnectionGatedContent>

            <Layout.Section/>

            {importRunning && (<ImportProgress jobId={jobId} importComplete={importComplete}/>)}
            {completedImports.map((importData) => (<CompletedImport importData={importData}/>))}
          </Layout.AnnotatedSection>
        </Layout>) : /*could add Skeleton*/<Loading />}
    </div>
  );
}