import React, { useMemo } from "react";
import {
  SkeletonBodyText,
  Banner,
  Card,
  Text,
  SkeletonDisplayText,
  BlockStack,
  Box,
} from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import HelpscoutParser from "../shared/services/HelpscoutParser";
import { useAppQuery } from "../hooks";

export const formatTitle = (title) => {
  const parts = title.split(":");

  if (parts.length > 1) {
    return parts.slice(1).join("").trim();
  }

  return title;
};

export default function HelpArticle({ id }) {
  const { t } = useTranslation();

  const { data, isLoading, isError } = useAppQuery({
    url: `/api/help/article/${id}`,
  });

  const articleTitle = useMemo(() => {
    if (!data) return;
    return formatTitle(data.article?.title);
  }, [data]);

  const articleBody = useMemo(() => {
    if (!data) return;
    return HelpscoutParser.parseAndReplace(data.article?.rawBody);
  }, [data]);

  if (isLoading) {
    return (
      <Card>
        <BlockStack gap={"200"}>
          <SkeletonDisplayText size="small" /> <SkeletonBodyText lines={20} />
        </BlockStack>
      </Card>
    );
  }

  if (isError) {
    return (
      <Card>
        <Banner tone="critical">
          <p>{t("help.errors.not_found.body")}</p>
        </Banner>
      </Card>
    );
  }

  return (
    <Card>
      <Box paddingBlockEnd={"400"}>
        <Text variant="headingLg" as="h3">
          {articleTitle}
        </Text>
      </Box>
      {articleBody}
    </Card>
  );
}
