import React, {useEffect} from "react";
import {Layout, Loading, Page, Spinner} from "@shopify/polaris";
import {useTranslation} from "react-i18next";
import {useAuthenticatedFetch} from '../hooks';
import {useLocation} from "react-router-dom";
import {useNavigate} from "@shopify/app-bridge-react";

export default function PdfDownload() {
  const { t } = useTranslation();
  const authenticatedFetch = useAuthenticatedFetch();
  const { search } = useLocation();
  const navigate = useNavigate();

  useEffect(async() => {
    const params = new URLSearchParams(search);
    const orderId = params.get("id");

    const response = await authenticatedFetch(`/api/get_invoice_pdf?id=${orderId}`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/pdf' },
      responseType: 'blob'
    });

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    const contentDisposition = response.headers.get('content-disposition');
    let fileName = 'download.pdf';
    if (contentDisposition) {
      const fileNameMatch = contentDisposition.match(/filename="(.+)"/);
      if (fileNameMatch.length === 2) {
        fileName = fileNameMatch[1];
      }
    }
    link.download = fileName;
    link.click();
    window.URL.revokeObjectURL(url);
    navigate({
      name: 'Order',
      resource: {
        id: orderId,
      }
    })
  }, []);

  return (
    <Page fullWidth>
      <Layout>
        <Layout.Section>
          <p><Spinner size={"small"}></Spinner> {t("vouchers.downloading_pdf")}</p>
        </Layout.Section>
      </Layout>
    <Loading />
    </Page>
  )
}