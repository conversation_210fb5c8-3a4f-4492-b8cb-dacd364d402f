import {TransferLogTable} from "../components/TransferLogTable";
import {Banner, Box, Page} from "@shopify/polaris";
import i18next from "i18next";
import {useAppQuery} from "../hooks";
import BeaconMessages from "../../shared_components/components/BeaconMessages";
import HelpFooter from "../../shared_components/components/HelpFooter";
import React, { useState, useEffect } from "react";
import {useTranslation} from "react-i18next";
import { useNavigate } from "react-router-dom";

export default function TransferLogPage() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const {
    data: beaconMessagesData
  } = useAppQuery({ url: '/api/beacon_messages?domain=errors-log' });


  return (
    <>
      { beaconMessagesData && <Page fullWidth
                                    title={<BeaconMessages beacons={beaconMessagesData.beacon_messages}
                                                           title={i18next.t("transfer_log.page_title")}/>}
                                    subtitle={i18next.t("transfer_log.page_subtitle")}
                                    backAction={{content: 'Back', onAction: () => navigate(-1)}}>
        <TransferLogTable></TransferLogTable>
        <HelpFooter/>
      </Page>
      }
    </>
  );
}
