import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import { NavigationMenu } from "@shopify/app-bridge-react";

import {
  AppBridgeProvider,
  QueryProvider,
  PolarisProvider,
} from "./components";
import EshopGuideProvider from "../shared_components/providers/EshopGuideProvider";
import { useTranslation } from "react-i18next";
import appService from "./services/appService";
import { Frame } from "@shopify/polaris";
import "./assets/style.scss";
import ErrorBoundary from "../../frontend/shared_components/components/ErrorBoundary.jsx";
import Router from "../shared_components/Router.jsx";

export default function App() {
  const adminPages = import.meta.globEager("./pages/**/!(*.test.[jt]sx)*.([jt]sx)");
  
  const { t } = useTranslation();

  return (
    <PolarisProvider>
      <ErrorBoundary>
        <BrowserRouter>
          <AppBridgeProvider>
            <ErrorBoundary>
              <QueryProvider>
                <NavigationMenu
                  navigationLinks={[
                    {
                      label: t('navigation.settings'),
                      destination: "/ShopSettings",
                    },
                    {
                      label: t('navigation.plans_and_coupons'),
                      destination: "/PlansAndCoupons",
                    },
                    {
                      label: t('navigation.transfer_log'),
                      destination: "/TransferLog",
                    },
                    {
                      label: t('navigation.import'),
                      destination: "/Import",
                    },
                    {
                      label: t('navigation.help'),
                      destination: "/Help",
                    },
                  ]}
                  matcher={(link, location) => link.destination === location.pathname}
                />
                <EshopGuideProvider appService={appService}>
                  <Frame>
                    <Router pages={adminPages} />
                  </Frame>
                </EshopGuideProvider>
              </QueryProvider>
            </ErrorBoundary>
          </AppBridgeProvider>
        </BrowserRouter>
      </ErrorBoundary>
    </PolarisProvider>
  );
}
