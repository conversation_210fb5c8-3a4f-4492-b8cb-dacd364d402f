import { Routes as ReactRouterRout<PERSON>, Route } from "react-router-dom";
import Help from "./pages/Help";
import HelpArticle from "../shared_components/components/HelpAndSupport/HelpArticle";

export default function AdminRoutes({ pages }) {
  const routes = useRoutes(pages);

  const routeComponents = routes.map(({ path, component: Component }) => (
    <Route key={path} path={path} element={<Component />} />
  ));

  return (
    <ReactRouterRoutes>
      {routeComponents}
      <Route path={"/Help"} element={<Help />}>
        <Route path={"/Help/:articleId"} element={<HelpArticle />}/>
      </Route>
    </ReactRouterRoutes>
  );
}

function useRoutes(pages) {
  return Object.keys(pages)
    .map((key) => {
      let path = key
        .replace("./pages", "")
        .replace(/\.(t|j)sx?$/, "")
        .replace(/\/index$/i, "/")
        .replace(/\b[A-Z]/, (firstLetter) => firstLetter.toLowerCase())
        .replace(/\[(?:[.]{3})?(\w+?)\]/g, (_match, param) => `:${param}`);

      if (path.endsWith("/") && path !== "/") {
        path = path.substring(0, path.length - 1);
      }

      if (!pages[key].default) {
        console.warn(`${key} doesn't export a default React component`);
      }

      return {
        path,
        component: pages[key].default,
      };
    })
    .filter((route) => route.component);
} 