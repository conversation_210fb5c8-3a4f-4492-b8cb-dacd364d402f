import React from "react";
import {InlineStack, Tag} from "@shopify/polaris";

const TagList = ({selected, removeTag}) => {
    if (selected.length === 0) return null;

    return (
        <InlineStack spacing="extraTight" alignment="center">
            {selected.map((option) => {
                let tagLabel = option.replace('_', ' ');
                return (
                    <div style={{padding: '2px'}} key={`option${option}`}>
                        <Tag onRemove={() => removeTag(option)}>
                            {tagLabel}
                        </Tag>
                    </div>
                );
            })}
        </InlineStack>
    );
};

export default TagList;