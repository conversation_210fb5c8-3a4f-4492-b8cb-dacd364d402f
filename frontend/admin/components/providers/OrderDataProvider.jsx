import React, { createContext, useContext, useState, useEffect } from "react";
import { useAppQuery } from "../../hooks";

// Create the context
const OrderDataContext = createContext(null);

/**
 * Provider component that fetches and provides sample order data
 * for liquid template resolution and validation
 */
export function OrderDataProvider({ children }) {
  const [sampleOrder, setSampleOrder] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch sample order data from the backend
  const { data, isLoading: queryLoading, isError } = useAppQuery({
    url: "/api/sample_order",
    reactQueryOptions: {
      refetchOnReconnect: false,
    },
  });

  useEffect(() => {
    if (!queryLoading) {
      if (data && data.order) {
        setSampleOrder(data.order);
      }
      setIsLoading(false);
    }
  }, [data, queryLoading]);

  // Utility function to resolve liquid placeholders in a string
  const resolvePlaceholders = (template) => {
    if (!template || !sampleOrder) return template;
    
    try {
      // Simple placeholder replacement for common patterns
      return template.replace(/{{([^{}]+)}}/g, (match, placeholder) => {
        const path = placeholder.trim().split('.');
        let value = sampleOrder;
        
        // Navigate the object path
        for (const key of path) {
          if (value && value[key] !== undefined) {
            value = value[key];
          } else {
            // If path doesn't exist, return the original placeholder
            return match;
          }
        }
        
        return value !== null && value !== undefined ? String(value) : match;
      });
    } catch (error) {
      console.error("Error resolving placeholders:", error);
      return template;
    }
  };

  // Calculate the effective length after sanitization and placeholder resolution
  const calculateEffectiveLength = (value, disallowedCharsRegex) => {
    if (!value) return 0;
    
    // First sanitize the string (remove disallowed chars)
    const sanitized = value.replace(disallowedCharsRegex, "");
    
    // Then resolve placeholders
    const resolved = resolvePlaceholders(sanitized);
    
    return resolved.length;
  };

  return (
    <OrderDataContext.Provider 
      value={{ 
        sampleOrder, 
        isLoading, 
        isError, 
        resolvePlaceholders,
        calculateEffectiveLength
      }}
    >
      {children}
    </OrderDataContext.Provider>
  );
}

// Custom hook to use the order data context
export function useOrderData() {
  const context = useContext(OrderDataContext);
  if (context === null) {
    throw new Error("useOrderData must be used within an OrderDataProvider");
  }
  return context;
}
