import React, { useState, useEffect } from 'react';
import {
    Banner, Box, Badge, BlockStack, Divider, Link, InlineStack, Text
} from "@shopify/polaris";
import { Slide, Fade } from "react-awesome-reveal";
import i18next from "i18next";
import {useTranslation} from "react-i18next";
import * as PropTypes from "prop-types";
const CompletedImport = ({ importData, redirectToLog = true }) => {
    const {t} = useTranslation();
    const [show, setShow] = useState(true);

    const [failedDocCount, setFailedDocCount] = useState({
        invoices_failed: 0,
        refunds_failed: 0,
        transactions_failed: 0,
    });

    useEffect(() => {
        setFailedDocCount({
            invoices_failed:
                (importData.invoices_queued || 0) - (importData.invoices_processed || 0),
            refunds_failed:
                (importData.refunds_queued || 0) - (importData.refunds_processed || 0),
            transactions_failed:
                (importData.transactions_queued || 0) - (importData.transactions_processed || 0),
        });
    }, []);

    useEffect(() => {
        importData.invoices_failed = ((importData.invoices_queued || 0) - (importData.invoices_processed || 0))
        importData.refunds_failed = ((importData.refunds_queued || 0) - (importData.refunds_processed || 0))
        importData.transactions_failed = ((importData.transactions_queued || 0) - (importData.transactions_processed || 0))
        },[]);

    return show && (<Box paddingBlockEnd={"200"}>
        <Fade duration={500}><Slide duration={500} direction={"left"}>
            <Banner
                tone={importData.success ? "success" : "warning"}
                title={importData.success ? t("import.message.completed_success_title") : t("import.message.completed_error_title")}
                onDismiss={() => { setShow(false)}}>

                <Box paddingBlockEnd={"200"}>
                    <p>{importData.success ? <span>{t("import.message.completed_success_text", {job_count: importData.current_job_count})} {
                            redirectToLog && <Link url="/TransferLog">{t("transfer_log.page_title")}</Link>
                    }</span>:
                                             <span>{t("import.message.completed_error_text_html", {job_count: importData.current_job_count, error_count: importData.failed_doc_count})} {
                                                 redirectToLog && <Link url="/TransferLog">{t("transfer_log.page_title")}</Link>
                                             }</span> }
                    </p>
                </Box>

                <BlockStack gap="100">
                    {(importData.invoices_queued > 0 || importData.invoices_skipped > 0)&&
                        <BlockStack gap="050">
                            <Text variant={"headingSm"} as={"span"}>{t("import.message.completed_invoices_vs_queued")}</Text>

                            {importData.invoices_processed > 0 && <Badge tone={"success"}> {t("import.message.completed",{count:(importData.invoices_processed)})}</Badge>}
                            {failedDocCount.invoices_failed> 0 && <Badge tone={"warning"}> {t("import.message.failed",{count:failedDocCount.invoices_failed})}</Badge>}
                            {importData.invoices_skipped > 0 && <Badge tone={"attention"}> {t("import.message.skipped",{count:(importData.invoices_skipped)})}</Badge>}
                        </BlockStack>}
                    {(importData.refunds_queued > 0 || importData.refunds_skipped > 0)&&
                        <BlockStack gap="050">
                            <Text variant={"headingSm"} as={"span"}>{t("import.message.completed_refunds_vs_queued")}</Text>
                            {importData.refunds_processed > 0 && <Badge tone={"success"}> {t("import.message.completed",{count:(importData.refunds_processed)})}</Badge>}
                            {failedDocCount.refunds_failed> 0 && <Badge tone={"warning"}> {t("import.message.failed",{count:failedDocCount.refunds_failed})}</Badge>}
                            {importData.refunds_skipped > 0 && <Badge tone={"attention"}> {t("import.message.skipped",{count:(importData.refunds_skipped)})}</Badge>}
                        </BlockStack>}
                    {(importData.transactions_queued > 0 || importData.transactions_skipped > 0)&&
                        <BlockStack gap="050">
                            <Text variant={"headingSm"} as={"span"}>{t("import.message.completed_transactions_vs_queued")}</Text>
                            {importData.transactions_processed > 0 && <Badge tone={"success"}> {t("import.message.completed",{count:(importData.transactions_processed)})}</Badge>}
                            {failedDocCount.transactions_failed> 0 && <Badge tone={"warning"}> {t("import.message.failed",{count:failedDocCount.transactions_failed})}</Badge>}
                            {importData.transactions_skipped > 0 && <Badge tone={"attention"}> {t("import.message.skipped",{count:(importData.transactions_skipped)})}</Badge>}
                        </BlockStack>}
                </BlockStack>
            </Banner>
        </Slide></Fade>
    </Box>);
}
export default CompletedImport;