import React, {Component, useCallback, useContext, useMemo, useState, useEffect} from 'react';
import PropTypes from 'prop-types';
import {EshopGuideContext} from "../../shared_components/providers/EshopGuideProvider";
import {
  BlockStack, 
  Badge, Divider,
  EmptyState,
  Frame,
  IndexTable, Layout, Link,
  Loading, Page, Select,
  Text,
  TextField,
  Pagination,
  SkeletonBodyText,
  useIndexResourceState, InlineStack, Button, Box, Card, Tabs, Filters
} from "@shopify/polaris";
import {
  RefreshMajor
} from '@shopify/polaris-icons'
import {useNavigate, useToast, Context} from "@shopify/app-bridge-react";
import {Redirect} from '@shopify/app-bridge/actions';
import {useAppQuery, useAuthenticatedFetch} from "../hooks";
import i18next from "i18next";
import debounce from 'lodash.debounce';
import ErrorBanner from "../components/ErrorBanner";
import TransferLogTableRow from "../components/TransferLogTableRow";
import VoucherStatus from "./VoucherStatus";

import { userFriendlyDateTime } from '../utils/dateUtils';
import cable from '../services/cable';
import TransferDocumentsModal from "../components/TransferDocumentsModal";

export function TransferLogTable() {
  const {shopInfo, appService} = useContext(EshopGuideContext);
  const [currentPage, setCurrentPage] = useState(1);
  const [queryParams, setQueryParams] = useState({sort: '', filter: '', search: ''});

  const [transferHistory, setTransferHistory] = useState([]);

  const navigate = useNavigate();
  const authenticatedFetch = useAuthenticatedFetch();

  const app = useContext(Context);
  const redirect = Redirect.create(app);

  const {
    data: transferHistoryData,
    refetch: refetchTransfers,
    isLoading: isLoadingTransfers,
    isRefetching: isRefetchingTranfers,
  } = useAppQuery({
    url: `/api/transfer_history?page=${currentPage}&sort=${queryParams.sort}&filter=${queryParams.filter}&search=${queryParams.search}`
  });

  useEffect(() => {
    if (transferHistoryData) {
      setTransferHistory(transferHistoryData?.transfers || []);
    }
  }, [transferHistoryData]);

  useEffect(() => {
    if (shopInfo === undefined) {
      return;
    }

    const subscription = cable.subscriptions.create(
      { channel: 'TransferLogChannel', shop_id: shopInfo.id },
      {
        received: (data) => {
          if (data.action === 'create') {
            refetchTransfers();

          } else if (data.action === 'update') {
                // trigger js event for Update with shopify_order_id
                const event = new CustomEvent('updateTransferLog', {detail: {order_id: data.shopify_order_id}});
                document.dispatchEvent(event);
            }
         }
      }
    );

    return () => {
      cable.subscriptions.remove(subscription);
    };
  }, [shopInfo]);

  const emptyRowMarkupLoading = (<div><SkeletonBodyText rows={6}/><SkeletonBodyText rows={6}/></div>);
  const emptyStateMarkup = (
    <EmptyState
      heading={i18next.t("transfer_log.empty_state.heading")}
      action={{
        content: i18next.t("transfer_log.empty_state.activate_transfer"),
        url: "/ShopSettings?tab=1"
      }}
      secondaryAction={{
        content: i18next.t("transfer_log.empty_state.activate_billing"),
        url: "/PlansAndCoupons"
      }}
      image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png">
      <p>{i18next.t("transfer_log.empty_state.content")}</p>
    </EmptyState>
  );
  const tableEmptyMarkup = isLoadingTransfers ? emptyRowMarkupLoading : emptyStateMarkup;

  const resourceName = {
    singular: i18next.t("general.order"),
    plural: i18next.t("general.order", {count: 2})
  };

  const {
    selectedResources,
    allResourcesSelected,
    handleSelectionChange,
    removeSelectedResources
  } = useIndexResourceState(transferHistory);
  const [taggedWith, setTaggedWith] = useState();
  const [queryValue, setQueryValue] = useState(null);
  const [sortValue, setSortValue] = useState("today");

  const handleTaggedWithChange = useCallback(
    (value) => setTaggedWith(value),
    []
  );
  const handleTaggedWithRemove = useCallback(() => setTaggedWith(null), []);
  const handleQueryValueRemove = useCallback(() => setQueryValue(null), []);
  const handleClearAll = useCallback(() => {
    handleTaggedWithRemove();
    handleQueryValueRemove();
  }, [handleQueryValueRemove, handleTaggedWithRemove]);
  const handleSortChange = useCallback((value) => setSortValue(value), []);

  const {show} = useToast();

  const [excludedTransfers,setExcludedTransfers] = useState({invoices:[], creditNotes:[], transactions:[]});
  const [transferModalActive, setTransferModalActive] = useState(false);
  const [transferModalOrderName, setTransferModalOrderName] = useState('');

  const handleRetries = async () => {
    const response = await authenticatedFetch('/api/retry_transfers', {
      method: 'POST',
      body: JSON.stringify({order_ids: selectedResources})
    });
    const data = await response.json();
    if (data.success) {
      removeSelectedResources(selectedResources);
      refetchTransfers();

      show(i18next.t("notifications.queued_retries", {count: data.job_count, finish_time: userFriendlyDateTime(data.last_retry_time)}), {
        duration: 10000,
        isError: false
      });
    } else {
      show(i18next.t("notifications.queued_error"), {
        duration: 3000,
        isError: true
      });
    }
  }

  const promotedBulkActions = [
    {
      content: i18next.t("transfer_log.bulk_actions.retry"),
      onAction: handleRetries
    }
  ];

  const filters = [];

  // TODO: remove taggedWith from filters
  const appliedFilters = !isEmpty(taggedWith)
    ? [
      {
        key: "taggedWith",
        label: disambiguateLabel("taggedWith", taggedWith),
        onRemove: handleTaggedWithRemove
      }
    ]
    : [];

  const sortOptions = [
    {label: "Date", value: "date"},
  ];

  // Filter-Tabs
  const [selectedTab, setSelectedTab] = useState(0);

  const handleTabChange = useCallback((selectedTabIndex) => {
    setSelectedTab(selectedTabIndex);
    setCurrentPage(1);
    setQueryParams((prevParams) => ({...prevParams, filter: tabs[selectedTabIndex].filter}));
  }, []);


  const handleSearchChange = (searchValue) => {
    setCurrentPage(1);
    setQueryParams((prevParams) => ({...prevParams, search: searchValue}));
  }
  const resetSearch = useCallback(() => {
    setQueryParams((prevParams) => ({...prevParams, search: ''}));
  }, []);

  const handleSearchChangeDebounced = useCallback(debounce(handleSearchChange, 500), []);

  const tabs = [
    {
      id: 'tab-all',
      content: i18next.t("transfer_log.tabs.all"),
      filter: ''
    },
    {
      id: 'tab-errors',
      filter: 'Error',
      content: i18next.t("transfer_log.tabs.errors")
    },
    {
      id: 'tab-invoice',
      filter: 'Invoice',
      content: i18next.t("transfer_log.tabs.invoice")
    },
    {
      id: 'tab-credit-note',
      filter: 'Refund',
      content: i18next.t("transfer_log.tabs.credit_note")
    },
    {
      id: 'tab-transaction',
      filter: 'Transaction',
      content: i18next.t("transfer_log.tabs.transaction")
    },
    {
      id: 'tab-skipped',
      filter: 'Skipped',
      content: i18next.t("transfer_log.tabs.skipped")
    },
  ];
const openTransferModal = (title, active, invoices, creditNotes, transactions) => {
  setTransferModalOrderName(title);
  setExcludedTransfers({invoices:invoices, creditNotes:creditNotes, transactions:transactions});
  setTransferModalActive(active);
}

  const rowMarkup = transferHistory.map(
    (
      {id, url, order_id, date, invoices, credit_notes, transactions, amountSpent, errors},
      index
    ) => (
      <TransferLogTableRow
        index={index}
        key={id}
        id={id}
        order_name={order_id}
        date={date}
        invoices={invoices}
        credit_notes={credit_notes}
        transactions={transactions}
        errors={errors}
        selectedResources={selectedResources}
        redirect={redirect}
        setTransferModalActive={openTransferModal}
      />
    )
  );

  return (
    <div>
      {!shopInfo && <Loading/>}
      <ErrorBanner
          transferHistoryData={transferHistoryData}
          onPrimaryActionClick={() => handleTabChange(4)}
      />
      <Divider/>
      <Layout>
        <Layout.Section>
          <Card padding="200">
            <Tabs tabs={tabs} selected={selectedTab} onSelect={handleTabChange} />
            <Filters
              queryValue={queryValue}
              queryPlaceholder={i18next.t("transfer_log.search_placeholder")}
              filters={filters}
              appliedFilters={appliedFilters}
              onQueryChange={handleSearchChangeDebounced}
              onQueryClear={resetSearch}
              onClearAll={handleClearAll}>
                <Box padding="300"><Button onClick={refetchTransfers} icon={RefreshMajor}></Button></Box>
            </Filters>
            <IndexTable
              resourceName={resourceName}
              emptyState={tableEmptyMarkup}
              itemCount={transferHistory.length}
              selectedItemsCount={
                allResourcesSelected ? "All" : selectedResources.length
              }
              onSelectionChange={handleSelectionChange}
              hasMoreItems
              promotedBulkActions={promotedBulkActions}
              lastColumnSticky
              loading={isLoadingTransfers || isRefetchingTranfers}
              headings={[
                {title: i18next.t("transfer_log.table_headers.order")},
                {title: i18next.t("transfer_log.table_headers.date")},
                '',
                {title: i18next.t("transfer_log.invoices")},
                {title: i18next.t("transfer_log.credit_notes")},
                {title: i18next.t("transfer_log.transactions")},
                {title: i18next.t("transfer_log.table_headers.errors")}
              ]}
            >
              {rowMarkup}

            </IndexTable>
            
            {transferHistoryData?.totalPages > 1 &&
              <div style={{padding: "1.5rem"}}>
                <BlockStack inlineAlign="center">
                  <Pagination
                    label={i18next.t("will_paginate.pagination_label", {
                      page: currentPage,
                      total: transferHistoryData?.totalPages
                    })}
                    hasPrevious={currentPage > 1}
                    previousKeys={[74]}
                    previousTooltip={i18next.t("will_paginate.previous_label")}
                    onPrevious={() => setCurrentPage((prev) => prev - 1)}
                    hasNext={currentPage < transferHistoryData?.totalPages}
                    nextKeys={[75]}
                    nextTooltip={i18next.t("will_paginate.next_label")}
                    onNext={() => setCurrentPage((prev) => prev + 1)}
                  />
                </BlockStack>
              </div>
            }
          </Card>
        </Layout.Section>
      </Layout>
      <TransferDocumentsModal title={i18next.t("transfer_log.modal.title", {order_name: transferModalOrderName })} active={transferModalActive} setActive={setTransferModalActive} creditNotes={excludedTransfers.creditNotes} invoices={excludedTransfers.invoices} transactions={excludedTransfers.transactions}></TransferDocumentsModal>
    </div>
  );

  function disambiguateLabel(key, value) {
    switch (key) {
      case "taggedWith":
        return `Tagged with ${value}`;
      default:
        return value;
    }
  }

  function isEmpty(value) {
    if (Array.isArray(value)) {
      return value.length === 0;
    } else {
      return value === "" || value == null;
    }
  }
}

