import { Banner } from "@shopify/polaris";
import { useContext } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { EshopGuideContext } from "../../shared_components/providers/EshopGuideProvider";

export default ({ feature, children }) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { shopInfo } = useContext(EshopGuideContext);
  const { features } = shopInfo?.billing || {};

  // If feature is available in the shops billing plan, just show the children (without paywall)
  if (features && features.includes(feature)) {
    return children;
  }

  return (
    <div style={{ position: "relative" }}>
      {children}
      <div
        style={{
          position: "absolute",
          top: 0,
          right: 0,
          bottom: 0,
          left: 0,
          backdropFilter: "blur(3px)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          paddingLeft: 40,
          paddingRight: 40,
        }}
      >
        <Banner
          tone="warning"
          title={t("paywall.title")}
          action={{
            content: t("paywall.buttonLabel"),
            onAction: () => navigate("/PlansAndCoupons"),
          }}
        >
          {t("paywall.text")}
        </Banner>
      </div>
    </div>
  );
};
