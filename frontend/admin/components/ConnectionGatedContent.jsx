import { useTranslation } from "react-i18next";
import { useEffect, useState, useContext } from "react";
import { Banner, SkeletonBodyText } from "@shopify/polaris";
import { useAuthenticatedFetch } from "@shopify/app-bridge-react";
import { EshopGuideContext } from "../../shared_components/providers/EshopGuideProvider";

export default function ConnectionGatedContent({ children, isConnected, messageKey }) {
    const { t } = useTranslation();
    const fetch = useAuthenticatedFetch();
    const { appService } = useContext(EshopGuideContext);

    const [loading, setLoading] = useState(true);
    const [blocked, setBlocked] = useState(true);

    useEffect(() => {
        setLoading(false);
        setBlocked(!isConnected)
    },[isConnected]);

    const containerStyle = {
        width: "100%",
        position: "relative",
        marginBottom: "1rem",
    };

    const overlayStyle = {
        display: "flex",
        zIndex: 100,
        alignItems: "center",
        position: "absolute",
        inset: 0,
        background: "rgba(255, 255, 255, 0.667)",
        borderRadius: "var(--p-border-radius-2)",
    };

    const contentStyle = {
        width: "80%",
        marginLeft: "10%",
        boxShadow: "rgba(0, 0, 0, 0.086) 0px 4px 5px",
        borderRadius: "calc(var(--p-border-radius-2) + 0.0625rem)",
    };

    const activateAction = {
        content: t(`${messageKey}.action`),
        onAction: () => appService.connect(fetch),
        external: false
    }

    const gatedLayout = (<div style={containerStyle}>
        <div style={overlayStyle}>
            <div style={contentStyle}>
                <Banner title={t(`${messageKey}.title`)} tone={"warning"} action={activateAction}>
                    <p>{t(`${messageKey}.content`)}</p>
                </Banner>
            </div>
        </div>
        {children}
    </div>)

    return loading ? (
        <SkeletonBodyText></SkeletonBodyText>
    ) : blocked ? (
        gatedLayout
    ) : (
        children
    );
} 