import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {Badge, Text} from "@shopify/polaris";
import CustomTextField from "./CustomTextField";
import { useOrderData } from "./providers/OrderDataProvider";

/**
 * A text field that sanitizes input to prevent TitleTooLongError and removes characters
 * that would be removed by StringSanitizer.remove_expanding_chars
 */
export default function SanitizedTextField(props) {
  const { t } = useTranslation();
  const { value, onChange, id, maxResolvedLength } = props;
  const [sanitizationError, setSanitizationError] = useState(null);
  const [placeholderError, setPlaceholderError] = useState(null);
  const { resolvePlaceholders, isLoading } = useOrderData();

  // Characters that would be removed by StringSanitizer.remove_expanding_chars
  const disallowedChars = /[&<>"'©€®£¢§™]/;

  // Check for disallowed characters
  useEffect(() => {
    if (value && disallowedChars.test(value)) {
      setSanitizationError(t("validation.disallowed_characters"));
    } else {
      setSanitizationError(null);
    }
  }, [value, t]);

  // Check for placeholder expansion that would exceed maxLength
  useEffect(() => {
    if (!isLoading && value && maxResolvedLength) {
      // First sanitize the value
      const sanitizedValue = value.replace(disallowedChars, "");

      // Then resolve placeholders
      const resolvedValue = resolvePlaceholders(sanitizedValue);

      // Check if the resolved value exceeds maxLength
      if (resolvedValue.length > maxResolvedLength) {
        setPlaceholderError(t("validation.expanded_too_long", {
          label: props.label,
          maxResolvedLength,
          currentLength: resolvedValue.length
        }));
      } else {
        setPlaceholderError(null);
      }
    }
  }, [value, maxResolvedLength, isLoading, resolvePlaceholders, t, props.label]);

  // Handle input change
  const handleChange = (newValue) => {
    // Remove disallowed characters before passing to parent onChange
    const sanitizedValue = newValue.replace(disallowedChars, "");

    // If we actually removed characters, show an error message
    if (newValue !== sanitizedValue) {
      setSanitizationError(t("validation.disallowed_characters"));
    } else {
      setSanitizationError(null);
    }

    // Call the parent onChange with the sanitized value
    onChange(sanitizedValue, id);
  };

  // Calculate effective length after sanitization & placeholder resolution
  const effectiveLength = value ? resolvePlaceholders(value.replace(disallowedChars, "")).length : 0;
  // Check if the value contains placeholders
  const hasPlaceholders = value && /{{([^{}]+)}}/g.test(value);

  // Show error if effective length exceeds maxLength
  const lengthErrorText = hasPlaceholders ? "validation.max_resolved_length" : "validation.max_length"
  const lengthError =
    maxResolvedLength && effectiveLength > maxResolvedLength
      ? t(lengthErrorText, { label: props.label, maxLength: maxResolvedLength })
      : null;

  // Combine errors
  const error = sanitizationError || lengthError || placeholderError || props.error;

  const suffix =  hasPlaceholders && !isLoading && value ? t("settings.liquid.preview")+ ": " + resolvePlaceholders(value.replace(disallowedChars, "")) : null;

  return (
    <CustomTextField
      {...props}
      error={error}
      suffix={suffix}
      onChange={handleChange}
      helpText={
        <>
          {props.helpText}
          {props.helpText && <br />}
        </>
      }
    />
  );
}
