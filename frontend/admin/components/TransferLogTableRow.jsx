import React, {useContext, useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import {IndexTable, Link, Text, Badge, BlockStack, Tooltip, Icon, Button} from '@shopify/polaris';
import { useNavigate } from '@shopify/app-bridge-react';
import { Redirect } from '@shopify/app-bridge/actions';
import i18next from 'i18next';

import { userFriendlyDateTime } from '../utils/dateUtils';
import { useAuthenticatedFetch } from "../hooks";


import TransferDocumentsModal from '../components/TransferDocumentsModal';

import {
  TransferMajor
} from '@shopify/polaris-icons'
import {EshopGuideContext} from "../../shared_components/providers/EshopGuideProvider";
import ReactHtmlParser from "react-html-parser";
import VoucherStatus from './VoucherStatus';

const TransferLogTableRow = ({ index, id, order_name, date, invoices, credit_notes, transactions, errors, selectedResources, redirect, setTransferModalActive }) => {
  const navigate = useNavigate();
  const authenticatedFetch = useAuthenticatedFetch();
  const [transferData, setTransferData] = useState({ invoices, credit_notes, transactions, errors });
  const [hasExcludedTransfer, setHasExcludedTransfer] = useState(false);
  const [retryDisabled, setRetryDisabled] = useState(true);
  const {shopInfo} = useContext(EshopGuideContext);
  const [openBeaconMessage, setOpenBeaconMessage] = useState(false);

  useEffect(() => {
    const handleUpdateTransferLog = async (event) => {
      if (event.detail.order_id === id) {
        // React accordingly if the order_id matches

        // refetch transfer data for the current id
        const response = await authenticatedFetch(`/api/transfer_history?order_id=${id}`, {
          method: 'GET', headers: {
            'Content-Type': 'application/json',
          }
        });
        const data = await response.json();

        // Update state with new transfer data
        setTransferData(data.transfers[0]);
      }
    };

    document.addEventListener('updateTransferLog', handleUpdateTransferLog);

    return () => {
      document.removeEventListener('updateTransferLog', handleUpdateTransferLog);
    };
  }, [id]);

  const excludedInvoices =invoices.filter(({ last_action }) => last_action === 'Excluded');
  const excludedCreditNotes = credit_notes.filter(({ last_action }) => last_action === 'Excluded');
  const excludedTransactions = transactions.filter(({ last_action }) => last_action === 'Excluded');

  const checkExcludedAction = (data) => {
    return data.invoices.some(({ last_action }) => last_action === 'Excluded') ||
      data.credit_notes.some(({ last_action }) => last_action === 'Excluded') ||
      data.transactions.some(({ last_action }) => last_action === 'Excluded');
  };

  useEffect(() => {
    setTransferData({ invoices, credit_notes, transactions, errors });
  }, [invoices, credit_notes, transactions, errors]);

  useEffect(() => {
    setHasExcludedTransfer(checkExcludedAction(transferData));
  }, [transferData]);

  const  wrapWithTooltip = (content, lastAction, sync_info_id) => {
    if (lastAction === 'Excluded') {
      return (
        <Tooltip key={sync_info_id + "_tooltip"} width={"wide"}  content={<Text variant="bodySm" as="span">{i18next.t("transfer_log.order_excluded_tooltip")}</Text>}>
          {content}
        </Tooltip>
      );
    }
    return content;
  }


  const currencyFormatter = new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' });

  const formatCurrency = (amount) => {
    //remove backslashes from amount
    if (amount === null) { amount = '' }
    amount = amount.replace(/\\/g, '');
    return currencyFormatter.format(amount);
  }

  const mapStatus = (status, lastAction) => {

    if(lastAction === 'Job started') {
      return 'info'
    }
    if( lastAction === 'Excluded') {
      return 'info'
    }
    switch (status) {
      case 'draft':
        return 'default';
      case 'open':
        return 'warning';
      case 'paid':
        return 'success';
      case 'paidoff':
        return 'success';
      case 'voided':
        return 'critical';
      default:
        return 'info';
    }
  }

  const mapProgress = (status, lastAction) => {

    if(lastAction === 'Job started') {
      return 'incomplete'
    }
    if( lastAction === 'Excluded') {
      return 'incomplete'
    }
    switch (status) {
      case 'draft':
        return 'incomplete';
      case 'open':
        return 'partiallyComplete';
      case 'paid':
        return 'complete';
      case 'paidoff':
        return 'complete';
      case 'voided':
        return 'complete';
      default:
        return 'incomplete';
    }

  }

  const mapErrorType = (error_type) => {
    switch (error_type) {
      case 'ShopifyAPI::Order':
        return 'RE:';
      case 'ShopifyAPI::Refund':
        return 'GS:';
      case 'tender_transaction':
        return 'TR:';
      case 'transaction_assignment_hint':
        return 'TAH:';
      default:
        return '';
    }
  }

const mapTitle = (title, lastAction) => {
  if (lastAction === 'Job started') {
    return i18next.t("transfer_log.order_processing");
  }
  if (lastAction === 'Excluded') {
    return i18next.t("transfer_log.order_excluded");
  }
  if (title === 'Order Cancelled') {
    return i18next.t("transfer_log.order_cancelled");
  }
  return title || i18next.t("transfer_log.no_title");
}

  // This handles closing and opening Beacon from the Support Button. Without this, the articleIds won't close properly.
  useEffect(() => {
    if (window.Beacon) {
      window.Beacon('on', 'open', () => {
        window.Beacon('navigate', '/answers/')
        setOpenBeaconMessage(true)
      });

      window.Beacon('on', 'close', () => {
        window.Beacon('navigate', '/answers/');
        setOpenBeaconMessage(false)
      });
    }
  }, []);

  const openBeacon = (articleId) => {
    if (openBeaconMessage) { window.Beacon('navigate', '/answers/') }
    if (articleId === 'NULL') {
      if (window.Beacon) {
        window.Beacon('open');
        window.Beacon('navigate', '/ask/')
      }
    }
    else {
      if (window.Beacon) { window.Beacon('article', articleId) }
    }
  }

  useEffect(() => {
    if(shopInfo === undefined) {
      return
    }
    setRetryDisabled(new Date(shopInfo.install_date) > new Date(date));
  }, [shopInfo]);

  const retryTooltipContent = retryDisabled ? i18next.t("transfer_log.retry_disabled_tooltip") : i18next.t("transfer_log.retry_enabled_tooltip");

  return (
    <IndexTable.Row
      id={id}
      key={id}
      selected={selectedResources.includes(id)}
      position={index}
      onClick={() => { }}
      disabled={transferData.errors.length === 0}
    >
      <IndexTable.Cell>
        <Link onClick={() => {
          navigate({
            name: 'Order',
            resource: {
              id: id,
            }
          })
        }} target={"_blank"}>
          <Text variant="bodyMd" fontWeight="bold" as="span">
            {order_name}
          </Text>
        </Link>
      </IndexTable.Cell>
      <IndexTable.Cell>
        {userFriendlyDateTime(date)}
      </IndexTable.Cell>
      <IndexTable.Cell>
        {hasExcludedTransfer && (<Tooltip width={"wide"} hoverDelay={1000} persistOnClick={true}  content={<Text variant="bodySm" as="span">{retryTooltipContent}</Text>}>
          <Button disabled={retryDisabled} onClick={() => setTransferModalActive(order_name,true, excludedInvoices, excludedCreditNotes, excludedTransactions)} size={"slim"} icon={TransferMajor}>{i18next.t("transfer_log.retry_excluded_transfer_short")}</Button>
        </Tooltip>)}
      </IndexTable.Cell>
      <IndexTable.Cell>
        <BlockStack gap="100">
          {transferData.invoices.map(({sync_info_id,  title, status, target_id, last_action }, index) => (
            wrapWithTooltip(
            <Text variant="bodyMd" as="span" key={sync_info_id}>
              <Badge
                tone={mapStatus(status, last_action)}
                progress={mapProgress(status, last_action)}
              >
                <Link removeUnderline={true} monochrome={true} target={"_blank"} onClick={() => {
                  redirect.dispatch(Redirect.Action.REMOTE, {
                    url: `${import.meta.env.LEXOFFICE_SITE}/vouchers#/VoucherView/Invoice/${target_id}`,
                    newContext: true,
                  });
                }}>
                  {mapTitle(title, last_action) }
                </Link>
              </Badge>
            </Text>,
              last_action, sync_info_id)
            ))}
        </BlockStack>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <BlockStack gap="100">
          {transferData.credit_notes.map(({sync_info_id, title, status, target_id, last_action }, index) => (
            target_id &&
            wrapWithTooltip(
            <Text variant="bodyMd" as="span" key={sync_info_id}>
              <Badge
                tone={mapStatus(status, last_action)}
                progress={mapProgress(status, last_action)}
              >
                <VoucherStatus status={status} title={mapTitle(title, last_action)} redirect={redirect} target_id={target_id} />
              </Badge>
            </Text>,
              last_action, sync_info_id)
          ))}
        </BlockStack>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <BlockStack gap="100">
          {transferData.transactions.map(({sync_info_id, amount, target_id, last_action }, index) => (
            target_id &&
            wrapWithTooltip(
            <Text variant="bodyMd" as="span" key={sync_info_id}>
              <Badge
                tone={mapStatus("paid", last_action)}
                progress={mapProgress("paid", last_action)}
              >
                {mapTitle(`${formatCurrency(amount)}`  , last_action) }
              </Badge>
            </Text>,
            last_action, sync_info_id)
          ))}
        </BlockStack>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <BlockStack gap="100">
          {transferData.errors.map(({ title, doc_type, helpscout_id, error_type }, index) => (
            (<Text variant="bodyMd" as="span" key={`error-${index}`}>
              <Badge tone={"critical"} progress={"complete"}>
                <Link monochrome={true} removeUnderline={true} onClick={() => openBeacon(helpscout_id)}>
                  {`${mapErrorType(error_type)} ${title}`}
                </Link>
              </Badge>
            </Text>)
          ))}
        </BlockStack>
      </IndexTable.Cell>

    </IndexTable.Row>
  );
};

TransferLogTableRow.propTypes = {
  index: PropTypes.number.isRequired,
  id: PropTypes.string.isRequired,
  order_name: PropTypes.string.isRequired,
  date: PropTypes.string.isRequired,
  invoices: PropTypes.array.isRequired,
  credit_notes: PropTypes.array.isRequired,
  transactions: PropTypes.array.isRequired,
  errors: PropTypes.array.isRequired,
  selectedResources: PropTypes.array.isRequired,
};

export default TransferLogTableRow;