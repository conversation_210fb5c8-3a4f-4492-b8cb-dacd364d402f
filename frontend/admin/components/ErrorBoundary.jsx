import { <PERSON>, <PERSON> } from "@shopify/polaris";
import { Honeybadger, HoneybadgerErrorBoundary } from "@honeybadger-io/react";
import { useTranslation } from "react-i18next";

const honeybadger = Honeybadger.configure({
    apiKey: import.meta.env.HONEYBADGER_FRONTEND_API_KEY,
    environment: import.meta.env.MODE || "production",
});

export default ({ children }) => {
    return (
        <HoneybadgerErrorBoundary
            honeybadger={honeybadger}
            ErrorComponent={FallbackComponent}
        >
            {children}
        </HoneybadgerErrorBoundary>
    );
};

const FallbackComponent = () => {
    const { t } = useTranslation();

    return (
        <Page>
            <Banner
                title={t("error_boundary.title")}
                tone="critical"
            >
                <p>{t("error_boundary.description")}</p>
            </Banner>
        </Page>
    );
};