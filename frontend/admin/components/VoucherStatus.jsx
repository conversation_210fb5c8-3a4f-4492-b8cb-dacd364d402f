import React from "react";
import i18next from "i18next";
import { Link } from "@shopify/polaris";
import { Redirect } from "@shopify/app-bridge/actions";

export default function VoucherStatus({ status, title, target_id, redirect }) {

    const isOrderCancelled = title === 'Order Cancelled';
    const isPending = status === 'pending';
    const hasValidTarget = target_id !== 'NULL';

    const getTitle = () => {
        if (isOrderCancelled) {
            return i18next.t("transfer_log.order_cancelled");
        }
        if (isPending && title === 'Pending Transaction') {
            return i18next.t("transfer_log.pending_refund_transaction");
        }
        return title || i18next.t("transfer_log.no_title");
    };

    const handleRedirect = () => {
        redirect.dispatch(Redirect.Action.REMOTE, {
            url: `${import.meta.env.LEXOFFICE_SITE}/vouchers#/VoucherView/CreditNote/${target_id}`,
            newContext: true,
        });
    };

    const renderContent = () => {
        if (!hasValidTarget) {
            return getTitle();
        }

        if (!isPending) {
            return (
                <Link removeUnderline={true} monochrome={true} target={"_blank"} onClick={handleRedirect}>
                    {getTitle()}
                </Link>
            );
        }
        // has valid target and is pending should not happen.

        return null;
    };

    return <>{renderContent()}</>;
}
