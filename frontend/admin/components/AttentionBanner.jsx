import { Banner } from '@shopify/polaris';
import { useState, useEffect } from 'react';
import { useTranslation } from "react-i18next";

export default function AttentionBanner({ shopSettings }) {
    const [showBanner, setShowBanner] = useState(false);
    const { t } = useTranslation();

    const handleDismiss = () => {
        setShowBanner(false);
        localStorage.setItem('bannerDismissed', 'true');
    };

    useEffect(() => {
        if (window.localStorage) {
            const isDismissed = localStorage.getItem('bannerDismissed') === 'true';

            if (shopSettings.invoice_timing === 'orders/create' && !isDismissed) {
                setShowBanner(true);
            } else {
                setShowBanner(false);
            }
        }
    }, [shopSettings]);


    if (!showBanner) {
        return null;
    }

    return (
        <Banner
            onDismiss={handleDismiss}
            title={t("settings.invoices.invoice_timing_banner.title")}
            status="info"
        >
        </Banner>
    );
}