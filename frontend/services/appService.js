// AppService
// This service implements different methods between Lexoffice and Sevdesk
class AppService {
  constructor() {}

  async connect() {
    const urlParams = new URLSearchParams(window.location.search);
    const shop = urlParams.get('shop') || window.shop_domain
    const host = urlParams.get('host') || window.__SHOPIFY_DEV_HOST
    window.location.href = window.location.origin + '/auth/lexoffice?shop=' + shop + '&host=' + host;
  }
}

export default new AppService();
