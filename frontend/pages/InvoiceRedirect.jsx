import React, {useEffect, useState} from "react";
import {Button, EmptyState, Layout, Loading, Page, Spinner} from "@shopify/polaris";
import {useTranslation} from "react-i18next";
import {useAuthenticatedFetch} from '~/hooks';
import {useLocation} from "react-router-dom";
import {useNavigate} from "@shopify/app-bridge-react";

export default function InvoiceRedirect() {
  const { t } = useTranslation();
  const authenticatedFetch = useAuthenticatedFetch();
  const { search } = useLocation();
  const navigate = useNavigate();
  const [notFound, setNotFound] = useState(false);

  const params = new URLSearchParams(search);
  const orderId = params.get("id");

  const navigateToOrder = () => {
    navigate({
      name: 'Order',
      resource: {
        id: orderId,
      }
    })
  }

  useEffect(async() => {
    const response = await authenticatedFetch(`/api/redirect_to_invoice?id=${orderId}`, {
      method: 'GET',
    });
    const json = await response.json();

    if (json.status == "not found") {
      setNotFound(true);
      return;
    }
    const redirectUrl = json.url;
    navigate(redirectUrl, {target: 'new'});
    navigateToOrder();
  }, []);

  return (
    <Page fullWidth>
      <Layout>
        <Layout.Section>
          {notFound ?
            <EmptyState fullWidth={true} heading={t("vouchers.not_found")}>
              <Button onClick={navigateToOrder}> Zurück zum Auftrag</Button>
            </EmptyState> :
            <p><Spinner size={"small"}></Spinner> {t("vouchers.redirect")}</p>
          }
        </Layout.Section>
      </Layout>
    <Loading />
    </Page>
  )
}