import { Page } from "@shopify/polaris";
import React from "react";
import {ImportPanel} from "~/components/ImportPanel";
import HelpFooter from "~/shared/components/HelpFooter";
import i18next from "i18next";
import { useNavigate } from "react-router-dom";

export default function Import() {
  const navigate = useNavigate();

  return (
    <Page title={i18next.t("import.title")} subtitle={i18next.t("import.subtitle")} backAction={{content: 'Back', onAction: () => navigate(-1)}}>
      <ImportPanel></ImportPanel>
      <HelpFooter/>
    </Page>
  )
}
