import React, { useState, memo } from "react";
import PropTypes from 'prop-types';
import { TransferLogTable } from "./TransferLogTable";
import { Banner, Divider, Layout, List } from "@shopify/polaris";

// PropTypes
const shopDataPropType = PropTypes.shape({
    shop_info: PropTypes.arrayOf(PropTypes.shape({
        shopify_domain: PropTypes.string.isRequired
    })).isRequired
});

const ShopErrors = memo(({ shopData }) => {
    // State
    const [showBanner, setShowBanner] = useState(false);
    const [errorBannerTone, setErrorBannerTone] = useState('success');

    // Render helpers
    const renderBanner = () => {
        if (!showBanner) return null;

        const isSuccess = errorBannerTone === 'success';
        return (
            <Banner
                title={isSuccess ? 'Aktion erfolgreich' : '<PERSON><PERSON> beim dürchführen der Aktion'}
                tone={errorBannerTone}
                onDismiss={() => setShowBanner(false)}
            >
                <List>
                    <List.Item>
                        {isSuccess ? 'Die Aktion wurde richtig durchgeführt' : 'Es gab einen Fehler bei der Aktion'}
                    </List.Item>
                </List>
            </Banner>
        );
    };

    return (
        <div>
            {renderBanner()}
            <TransferLogTable 
                shop={shopData.shop_info[0].shopify_domain} 
                setErrorBannerTone={setErrorBannerTone}
                setShowBanner={setShowBanner}
            />
            <Divider/>
            <Layout>
                <Layout.AnnotatedSection/>
            </Layout>
        </div>
    );
});

ShopErrors.propTypes = {
    shopData: shopDataPropType.isRequired
};

export default ShopErrors;