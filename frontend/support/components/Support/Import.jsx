import React, { useCallback, useState, useEffect, memo, useMemo } from "react";
import PropTypes from 'prop-types';
import {
    Layout,
    ButtonGroup,
    Button,
    Divider,
    Badge,
    InlineStack,
    Loading,
    Checkbox,
    FormLayout,
    Text,
    Link,
    Banner,
    Card,
    Box
} from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import DateRangePicker from "../../../admin/components/Import/DateRangePicker.jsx";
import ImportProgress from "../../../admin/components/Import/ImportProgress.jsx";
import CompletedImport from "../../../admin/components/Import/CompletedImport.jsx";
import ReactHtmlParser from "react-html-parser";
import ReactJson from "react-json-view";
import { useAuthenticatedFetch } from "../../hooks/useAuthenticatedFetch";

// PropTypes
const shopDataPropType = PropTypes.shape({
    shop_info: PropTypes.arrayOf(PropTypes.shape({
        shopify_domain: PropTypes.string.isRequired
    })).isRequired,
    import: PropTypes.shape({
        import_running: PropTypes.bool
    }),
    old_imports: PropTypes.array
});

// Constants
const STORAGE_KEYS = {
    FAIR_USAGE_NOTE: 'FairUsageNoteDismissed'
};

const Import = memo(({ shopData, refetch }) => {
    const { t } = useTranslation();
    const authenticatedFetch = useAuthenticatedFetch();
    
    // State
    const today = new Date();
    const [importPeriod, setImportPeriod] = useState({ period: { since: new Date(today.getFullYear() - 1, 0, 1), until: new Date(today.getFullYear(), 0, 0) } });
    const [docTypes, setDocTypes] = useState({
        invoices: false,
        refunds: false,
        transactions: false
    });
    const [docCount, setDocCount] = useState({
        orders: null,
        refunds: null,
        transactions: null
    });
    const [uiState, setUiState] = useState({
        countLoading: false,
        jobId: null,
        importRunning: true,
        showFairUsageNote: false
    });
    const [completedImports, setCompletedImports] = useState([]);

    const shop = shopData.shop_info[0].shopify_domain;

    // Effects
    useEffect(() => {
        setUiState(prev => ({
            ...prev,
            importRunning: shopData?.import?.import_running
        }));
    }, [shopData]);

    useEffect(() => {
        try {
            const dismissed = sessionStorage.getItem(STORAGE_KEYS.FAIR_USAGE_NOTE) === 'true';
            setUiState(prev => ({ ...prev, showFairUsageNote: !dismissed }));
        } catch {
            setUiState(prev => ({ ...prev, showFairUsageNote: true }));
        }
    }, []);

    useEffect(() => {
        getCount();
    }, []);
    // Handlers
    const handleDateChange = useCallback((newValue) => {
        setImportPeriod(newValue);
        getCount(newValue);
    }, []);

    const handleDocTypeChange = useCallback((type) => (value) => {
        setDocTypes(prev => ({ ...prev, [type]: value }));
    }, []);

    const dismissFairUsageNote = useCallback(() => {
        setUiState(prev => ({ ...prev, showFairUsageNote: false }));
        try {
            sessionStorage.setItem(STORAGE_KEYS.FAIR_USAGE_NOTE, 'true');
        } catch (error) {
            console.error('Error saving to sessionStorage:', error);
        }
    }, []);

    const importButtonEnabled = useMemo(() => {
        const anyDocsToTransfer = (
            (docTypes.invoices && docCount.orders > 0) || 
            (docTypes.refunds && docCount.refunds > 0) || 
            (docTypes.transactions && docCount.transactions > 0)
        );
        return anyDocsToTransfer && !uiState.importRunning && !uiState.countLoading;
    }, [docTypes, docCount, uiState.importRunning, uiState.countLoading]);

    // API calls
    const getCount = async (periodValue = importPeriod) => {
        setUiState(prev => ({ ...prev, countLoading: true }));
        try {
            const response = await authenticatedFetch('/api/support/count', {
                method: 'POST',
                body: JSON.stringify({
                    start_date: periodValue.period.since,
                    end_date: periodValue.period.until,
                    ...docTypes,
                    shop
                })
            });
            const data = await response;
            if (data.success) {
                setDocCount({
                    orders: data.orders,
                    refunds: data.refunds,
                    transactions: data.transactions
                });
            } else {
                console.error('Error counting documents:', data.error);
            }
        } catch (error) {
            console.error('Error counting documents:', error);
        } finally {
            setUiState(prev => ({ ...prev, countLoading: false }));
        }
    };

    const startImport = async () => {
        try {
            const response = await authenticatedFetch('/api/support/start', {
                method: 'POST',
                body: JSON.stringify({
                    start_date: importPeriod.period.since,
                    end_date: importPeriod.period.until,
                    ...docTypes,
                    shop
                })
            });
            const data = await response;
            
            if (data.success) {
                setUiState(prev => ({
                    ...prev,
                    importRunning: true,
                    jobId: data.job_id
                }));
            } else {
                console.error('Error starting import:', data.error);
            }
        } catch (error) {
            console.error('Error starting import:', error);
        }
    };

    const importComplete = useCallback((data) => {
        setUiState(prev => ({
            ...prev,
            importRunning: false,
            jobId: null
        }));
        setCompletedImports(prev => [...prev, data]);
        refetch();
    }, [refetch]);

    // Render helpers
    const renderCheckbox = (type, label, count, disabled = false, helpText = null) => (
        <InlineStack gap="100" wrap={false}>
            <Checkbox
                label={t(label)}
                checked={docTypes[type]}
                onChange={handleDocTypeChange(type)}
                disabled={disabled}
                helpText={helpText}
            />
            {count != null && (!disabled || count > 0) && (
                <Badge>{count > 250 ? '250+' : count}</Badge>
            )}
        </InlineStack>
    );

    if (!shopData) return <Loading />;

    return (
        <div>
            <Layout>
                <Layout.AnnotatedSection
                    title="Import"
                    description={ReactHtmlParser(t('import.help_text'))}
                >
                    <Card>
                        <Box>
                            <FormLayout>
                                <FormLayout.Group>
                                    <DateRangePicker onChange={handleDateChange} />
                                </FormLayout.Group>
                                <Divider/>
                                <InlineStack gap="100" wrap={false}>
                                    <Text as="p" variant="headingSm" alignment="start">
                                        {t("import.doc_types")}
                                    </Text>
                                </InlineStack>
                                {renderCheckbox('invoices', 'import.invoices', docCount.orders)}
                                {renderCheckbox('refunds', 'import.refunds', docCount.refunds)}
                                {renderCheckbox(
                                    'transactions',
                                    'import.transactions',
                                    docCount.transactions,
                                    docCount.transactions === 0,
                                    docCount.transactions === 0 && (
                                        <Banner tone="warning">
                                            {t("import.no_transactions")}
                                            <Link onClick={() => navigate('/shopSettings')}>
                                                {t("settings.page_title")}
                                            </Link>
                                            {t("import.do")}
                                        </Banner>
                                    )
                                )}
                                <Divider/>
                                <ButtonGroup>
                                    <Button
                                        disabled={!importButtonEnabled}
                                        onClick={startImport}
                                        variant="primary"
                                        loading={uiState.countLoading}
                                    >
                                        {uiState.countLoading ? t("import.counting") : t("import.start")}
                                    </Button>
                                    <Text variant="bodyMd">
                                        Geschätzte Dauer: {Math.ceil((docCount.orders + docCount.refunds + docCount.transactions) * 4 / 60)} Minuten
                                    </Text>
                                </ButtonGroup>
                            </FormLayout>
                        </Box>

                        {uiState.showFairUsageNote && (
                            <Box paddingBlockStart="200">
                                <Banner tone="info" onDismiss={dismissFairUsageNote}>
                                    {ReactHtmlParser(t("import.fair_use"))}
                                </Banner>
                            </Box>
                        )}
                    </Card>

                    {uiState.importRunning && (
                        <ImportProgress 
                            jobId={uiState.jobId}
                            importComplete={importComplete}
                        />
                    )}
                    
                    {completedImports.map((importData, index) => (
                        <CompletedImport 
                            key={`import-${index}`}
                            importData={importData} 
                            redirectToLog={false}
                        />
                    ))}
                </Layout.AnnotatedSection>

                <Layout.AnnotatedSection
                    title="Alte Imports"
                    description="Die alte Imports von dem Shop"
                >
                    <Card>
                        {shopData.old_imports.map((importData, index) => (
                            <ReactJson 
                                key={`old-import-${index}`}
                                src={importData} 
                                name={`import vom ${importData.created_at}`} 
                                collapsed={true}
                                displayDataTypes={false}
                                enableClipboard={false}
                            />
                        ))}
                    </Card>
                </Layout.AnnotatedSection>
            </Layout>
        </div>
    );
});

Import.propTypes = {
    shopData: shopDataPropType.isRequired,
    refetch: PropTypes.func.isRequired
};

export default Import;