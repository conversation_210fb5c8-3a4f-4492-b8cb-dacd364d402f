import React, {useEffect, useRef, useState, useCallback, memo, useMemo} from "react";
import {BlockStack, Box, Card, DatePicker, Icon, Popover, TextField} from "@shopify/polaris";
import {CalendarMinor} from "@shopify/polaris-icons";
import PropTypes from 'prop-types';

// Utility functions
const formatDate = (date) => {
    try {
        const newDate = new Date(date);
        if (isNaN(newDate.getTime())) throw new Error('Invalid date');
        return newDate.toISOString().slice(0, 10);
    } catch (error) {
        console.error('Date formatting error:', error);
        return '';
    }
};

const getInitialDateState = (initialDate) => {
    const date = initialDate || new Date();
    return {
        month: date.getMonth(),
        year: date.getFullYear(),
    };
};

function CustomDatePicker({label, setNewSelectedDate, initialDate}) {
    // State management
    const [visible, setVisible] = useState(false);
    const [selectedDate, setSelectedDate] = useState(initialDate || new Date());
    const [dateState, setDateState] = useState(() => getInitialDateState(initialDate));
    const datePickerRef = useRef(null);

    // Memoized values
    const formattedValue = useMemo(() => {
        const newDate = new Date(selectedDate);
        newDate.setDate(newDate.getDate() + 1);
        return formatDate(newDate);
    }, [selectedDate]);

    // Memoized handlers
    const handleMonthChange = useCallback((month, year) => {
        setDateState({ month, year });
    }, []);

    const handleDateSelection = useCallback(({ end: newSelectedDate }) => {
        setSelectedDate(newSelectedDate);
        const newDate = new Date(newSelectedDate);
        newDate.setDate(newDate.getDate() + 1);
        setNewSelectedDate(formatDate(newDate));
        setVisible(false);
    }, [setNewSelectedDate]);

    const handleOnClose = useCallback(({ relatedTarget }) => {
        setVisible(false);
    }, []);

    const handleInputFocus = useCallback(() => {
        setVisible(true);
    }, []);

    // Effects
    useEffect(() => {
        if (selectedDate) {
            setNewSelectedDate(selectedDate);
            setDateState({
                month: selectedDate.getMonth(),
                year: selectedDate.getFullYear(),
            });
        }
    }, [selectedDate, setNewSelectedDate]);

    // Render helpers
    const renderTextField = (
        <TextField
            role="combobox"
            label={label}
            prefix={<Icon source={CalendarMinor} />}
            value={formattedValue}
            onFocus={handleInputFocus}
            onChange={() => {}} // Controlled component needs onChange
            autoComplete="off"
            readOnly // Since we're using the DatePicker for input
        />
    );

    const renderDatePicker = (
        <Card ref={datePickerRef}>
            <DatePicker
                month={dateState.month}
                year={dateState.year}
                selected={selectedDate}
                onMonthChange={handleMonthChange}
                onChange={handleDateSelection}
            />
        </Card>
    );

    return (
        <BlockStack inlineAlign="center" gap="400">
            <Box minWidth="276px" padding={{ xs: 200 }}>
                <Popover
                    active={visible}
                    autofocusTarget="none"
                    preferredAlignment="left"
                    fullWidth
                    preferInputActivator={false}
                    preferredPosition="below"
                    preventCloseOnChildOverlayClick
                    onClose={handleOnClose}
                    activator={renderTextField}
                >
                    {renderDatePicker}
                </Popover>
            </Box>
        </BlockStack>
    );
}

CustomDatePicker.propTypes = {
    label: PropTypes.string.isRequired,
    setNewSelectedDate: PropTypes.func.isRequired,
    initialDate: PropTypes.instanceOf(Date)
};

CustomDatePicker.defaultProps = {
    initialDate: new Date()
};

export default memo(CustomDatePicker);