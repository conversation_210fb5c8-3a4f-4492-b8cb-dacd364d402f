import React, { memo } from 'react';
import PropTypes from 'prop-types';
import ReactJson from "react-json-view";

const AuditChangeList = memo(({ changes, action, collapsed = true }) => {
    const name = action === 'create' ? 'Neue Einstellungen' : 'Geänderte Einstellungen';

    return (
        <div>
            <ReactJson 
                src={changes} 
                name={name} 
                collapsed={collapsed}
                displayDataTypes={false}
                enableClipboard={false}
                style={{ 
                    backgroundColor: 'transparent',
                    padding: '8px'
                }}
            />
        </div>
    );
});

AuditChangeList.propTypes = {
    changes: PropTypes.object.isRequired,
    action: PropTypes.oneOf(['create', 'update', 'delete']).isRequired,
    collapsed: PropTypes.bool
};

export default AuditChangeList;

