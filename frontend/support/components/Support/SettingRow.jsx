import React, { useState, useCallback, useEffect, memo } from "react";
import PropTypes from 'prop-types';
import {
    Button, Collapsible, Badge,
} from '@shopify/polaris';
import { ClipboardMinor } from "@shopify/polaris-icons";

// Constants
const TRUNCATE_LENGTH = 20;
const COPY_NOTIFICATION_DURATION = 1000;

// Styles
const styles = {
    container: {
        width: '500px'
    },
    content: {
        padding: '10px',
        border: '1px solid #dfe3e8',
        borderRadius: '5px',
        wordWrap: 'break-word',
        whiteSpace: 'pre-wrap'
    }
};

const SettingRow = memo(({ setting }) => {
    // State
    const [open, setOpen] = useState(false);
    const [buttonText, setButtonText] = useState(null);
    const [showCopied, setShowCopied] = useState(false);

    // Handlers
    const handleToggle = useCallback(async () => {
        setOpen(prev => !prev);
        try {
            await navigator.clipboard.writeText(setting);
            setShowCopied(true);
            setTimeout(() => {
                setShowCopied(false);
            }, COPY_NOTIFICATION_DURATION);
        } catch (error) {
            console.error('Failed to copy to clipboard:', error);
        }
    }, [setting]);

    // Effects
    useEffect(() => {
        if (setting === null) return;
        
        setButtonText(
            setting.length > TRUNCATE_LENGTH 
                ? `${setting}`.substring(0, TRUNCATE_LENGTH) + '...'
                : `${setting}`
        );
    }, [setting]);

    // Render helpers
    const renderContent = () => (
        <div style={styles.content}>
            {setting}
        </div>
    );
    return (
        <div style={styles.container}>
            <Button
                onClick={handleToggle}
                ariaExpanded={open}
                ariaControls="setting-collapsible"
                variant="tertiary"
            >
                {buttonText || 'null'}
                {showCopied && (
                    <Badge tone="success" icon={ClipboardMinor}>
                        Copied!
                    </Badge>
                )}
            </Button>
            <Collapsible
                open={open}
                id="setting-collapsible"
                transition={{
                    duration: '500ms',
                    timingFunction: 'ease-in-out'
                }}
                expandOnPrint
            >
                {renderContent()}
            </Collapsible>
        </div>
    );
});

SettingRow.propTypes = {
    setting: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number,
        PropTypes.bool
    ])
};

SettingRow.defaultProps = {
    setting: ''
};

export default SettingRow;