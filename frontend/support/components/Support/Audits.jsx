import React, { useCallback, useState, memo, useMemo } from "react";
import PropTypes from 'prop-types';
import { ResourceList, ResourceItem, Card, Badge, Button } from "@shopify/polaris";
import AuditChangeList from "./AuditChangesList";

// Constants
const ITEMS_PER_PAGE = 10;

// PropTypes
const auditPropType = PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    url: PropTypes.string,
    name: PropTypes.string,
    action: PropTypes.string.isRequired,
    auditable_type: PropTypes.string.isRequired,
    audited_changes: PropTypes.object.isRequired,
    created_at: PropTypes.string.isRequired
});

const AlternateTool = memo(({ 
    collapseAll, 
    toggleCollapseAll, 
    startIndex, 
    endIndex, 
    totalItems, 
    onPreviousPage, 
    onNextPage 
}) => (
    <>
        <Button onClick={toggleCollapseAll}>
            {collapseAll ? 'Alle ausklappen' : 'Alle einklappen'}
        </Button>
        <span style={{margin: '0 10px', left: 0}}>
            0: alter Wert | 1: neuer Wert
        </span>
        <Button 
            disabled={startIndex <= 0} 
            onClick={onPreviousPage}
        >
            Vorherige Seite
        </Button>
        <span style={{margin: '0 10px'}}>
            {startIndex + 1} - {endIndex >= totalItems ? totalItems : endIndex} von {totalItems}
        </span>
        <Button 
            disabled={endIndex >= totalItems} 
            onClick={onNextPage}
        >
            Nächste Seite
        </Button>
    </>
));

AlternateTool.propTypes = {
    collapseAll: PropTypes.bool.isRequired,
    toggleCollapseAll: PropTypes.func.isRequired,
    startIndex: PropTypes.number.isRequired,
    endIndex: PropTypes.number.isRequired,
    totalItems: PropTypes.number.isRequired,
    onPreviousPage: PropTypes.func.isRequired,
    onNextPage: PropTypes.func.isRequired
};

const Audits = memo(({ audits }) => {
    // State
    const [currentPage, setCurrentPage] = useState(1);
    const [collapseAll, setCollapseAll] = useState(true);

    // Memoized values
    const { startIndex, endIndex, paginatedAudits } = useMemo(() => {
        const start = (currentPage - 1) * ITEMS_PER_PAGE;
        const end = start + ITEMS_PER_PAGE;
        return {
            startIndex: start,
            endIndex: end,
            paginatedAudits: audits.slice(start, end)
        };
    }, [currentPage, audits]);

    // Handlers
    const handleNextPage = useCallback(() => {
        setCurrentPage(prev => prev + 1);
    }, []);

    const handlePreviousPage = useCallback(() => {
        setCurrentPage(prev => prev - 1);
    }, []);

    const toggleCollapseAll = useCallback(() => {
        setCollapseAll(prev => !prev);
    }, []);

    // Render helpers
    const renderItem = useCallback((item) => {
        const { id, url, name, action, auditable_type, audited_changes, created_at } = item;

        return (
            <ResourceItem
                id={String(id)}
                url={url}
                accessibilityLabel={`View details for ${name || id}`}
            >
                <Badge tone="info">{action}</Badge>
                <Badge tone="success">{auditable_type}</Badge>
                <Badge tone="warning">{created_at}</Badge>
                <AuditChangeList 
                    changes={audited_changes} 
                    action={action} 
                    collapsed={collapseAll}
                />
            </ResourceItem>
        );
    }, [collapseAll]);

    return (
        <Card>
            <ResourceList
                resourceName={{singular: 'audit', plural: 'audits'}}
                items={paginatedAudits}
                renderItem={renderItem}
                alternateTool={
                    <AlternateTool
                        collapseAll={collapseAll}
                        toggleCollapseAll={toggleCollapseAll}
                        startIndex={startIndex}
                        endIndex={endIndex}
                        totalItems={audits.length}
                        onPreviousPage={handlePreviousPage}
                        onNextPage={handleNextPage}
                    />
                }
            />
        </Card>
    );
});

Audits.propTypes = {
    audits: PropTypes.arrayOf(auditPropType).isRequired
};

export default Audits;