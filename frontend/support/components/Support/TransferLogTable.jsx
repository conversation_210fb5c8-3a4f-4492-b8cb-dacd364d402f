import React, {useCallback, useEffect, useMemo, useState, memo} from 'react';
import PropTypes from 'prop-types';
import {
    BlockStack,
    Badge,
    Divider,
    EmptyState,
    IndexTable,
    Layout,
    Link,
    Loading,
    Text,
    Pagination,
    SkeletonBodyText,
    useIndexResourceState,
    Button,
    Box,
    Card,
    Tabs,
    Filters,
    Tooltip,
    Icon,
    InlineStack
} from "@shopify/polaris";
import {
    ClipboardMinor,
    RefreshMajor,
    EmailMajor,
    RefreshMinor,
    EmailNewsletterMajor,
    CancelMinor
} from '@shopify/polaris-icons'
import i18next from "i18next";
import debounce from 'lodash.debounce';
import {useDataQuery} from "../../hooks/useDataQuery";
import i18n from "../../../admin/services/i18n";
import {getCsrfToken} from "../../hooks/getCsrfToken";
import DateRangePicker from "../../../admin/components/Import/DateRangePicker";
import ErrorBoundary from './ErrorBoundary';
import {useAuthenticatedFetch} from "../../hooks/useAuthenticatedFetch";

// Constants
const TABLE_CONFIG = {
    PAGE_SIZE: 10,
    STATUS_MAP: {
        draft: { tone: 'default', progress: 'incomplete' },
        open: { tone: 'warning', progress: 'partiallyComplete' },
        paid: { tone: 'success', progress: 'complete' },
        paidoff: { tone: 'success', progress: 'complete' },
        voided: { tone: 'critical', progress: 'complete' },
    },
    ERROR_TYPES: {
        'ShopifyAPI::Order': 'RE:',
        'ShopifyAPI::Refund': 'GS:',
        'tender_transaction': 'TR:',
        'Transaction Assignment Hint': 'TAH:',
    }
};

// Utility functions
const parseDate = (inputDate) => {
    const date = new Date(inputDate);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
};

const isEmpty = (value) => {
    if (Array.isArray(value)) return value.length === 0;
    return value === "" || value == null;
};

// PropTypes
const documentPropType = PropTypes.shape({
    title: PropTypes.string,
    status: PropTypes.string,
    target_id: PropTypes.string,
    last_action: PropTypes.string
});

const errorPropType = PropTypes.shape({
    title: PropTypes.string.isRequired,
    error_id: PropTypes.string.isRequired,
    helpscout_id: PropTypes.string,
    error_info_internal: PropTypes.string,
    shopify_type: PropTypes.string
});

// Memoized Components
const ErrorBadge = memo(({ error, onRetry, onCopy, internalErrorTone }) => {
    const { title, error_id, helpscout_id, error_info_internal, shopify_type } = error;
    
    return (
        <>
            <Text variant="bodyMd" as="span">
                <Button variant={'tertiary'} icon={RefreshMinor} onClick={() => onRetry(error_id)}/>
                <Badge tone={"critical"} progress={"complete"}>
                    <Link monochrome={true} removeUnderline={true} onClick={() => openBeacon(helpscout_id)}>
                        {`${TABLE_CONFIG.ERROR_TYPES[shopify_type] || ''} ${title}`}
                    </Link>
                </Badge>
            </Text>
            <Tooltip content={error_info_internal}>
                <Text variant="bodyMd" as="span">
                    <Button variant={'tertiary'} icon={ClipboardMinor} onClick={() => onCopy(error_info_internal)}/>
                    <Badge tone={internalErrorTone} progress={"complete"}>
                        {error_info_internal.length > 20 ? error_info_internal.substring(0, 20) + "..." : "Error hat keine interne Info"}
                    </Badge>
                </Text>
            </Tooltip>
        </>
    );
});

ErrorBadge.propTypes = {
    error: errorPropType.isRequired,
    onRetry: PropTypes.func.isRequired,
    onCopy: PropTypes.func.isRequired,
    internalErrorTone: PropTypes.string.isRequired
};

const DocumentBadge = memo(({ document, type }) => {
    const { title, status, target_id, last_action } = document;
    if (!target_id) return null;

    return (
        <InlineStack style={{padding: "0.5rem"}}>
            <Text variant="bodyMd" as="span" key={target_id}>
                <Badge
                    tone={TABLE_CONFIG.STATUS_MAP[status]?.tone || 'default'}
                    progress={TABLE_CONFIG.STATUS_MAP[status]?.progress || 'incomplete'}
                >
                    <InlineStack gap={100} blockAlign={'center'}>
                        {title || i18next.t("transfer_log.no_title")}
                        {last_action === 'Send Mail Job Queued' ? <Icon source={EmailNewsletterMajor} /> : 
         last_action === 'Mailed' ? <Icon source={EmailMajor} /> : <Icon source={CancelMinor} />}
                    </InlineStack>
                </Badge>
            </Text>
        </InlineStack>
    );
});

DocumentBadge.propTypes = {
    document: documentPropType.isRequired,
    type: PropTypes.string.isRequired
};

const TransactionBadge = memo(({ transaction }) => {
    const { amount, target_id } = transaction;
    if (!target_id) return null;

    return (
        <Text variant="bodyMd" as="span" key={target_id}>
            <Badge tone={"success"} progress="complete">
                {amount.replace(/\\/g, '')} €
            </Badge>
        </Text>
    );
});

TransactionBadge.propTypes = {
    transaction: PropTypes.shape({
        amount: PropTypes.string.isRequired,
        target_id: PropTypes.string.isRequired
    }).isRequired
};

export function TransferLogTable({shop, setErrorBannerTone, setShowBanner}) {
    const [currentPage, setCurrentPage] = useState(1);
    const [queryParams, setQueryParams] = useState({sort: '', filter: '', search: '', interval: {since: '', until: ''}});
    const [retry_all, setRetryAll] = useState(false);
    const [internalErrorTone, setInternalErrorTone] = useState('critical');
    const [singleErrorId, setSingleErrorId] = useState(null);

    const authenticatedFetch = useAuthenticatedFetch();

    useEffect(() => {
        i18n.changeLanguage('de');
    }, [i18n]);

    // Memoized values
    const queryString = useMemo(() => {
        return `/api/support/transfer_history?page=${currentPage}&sort=${queryParams.sort}&filter=${queryParams.filter}&search=${queryParams.search}&shop=${shop}&start_date=${queryParams.interval.since}&end_date=${queryParams.interval.until}`;
    }, [currentPage, queryParams, shop]);

    const {
        data: transferHistoryData,
        refetch: refetchTransfers,
        isLoading: isLoadingTransfers,
        isRefetching: isRefetchingTranfers,
    } = useDataQuery({ url: queryString });

    const transferHistory = transferHistoryData?.transfers || [];

    const emptyRowMarkupLoading = (<div><SkeletonBodyText rows={6}/><SkeletonBodyText rows={6}/></div>);
    const emptyStateMarkup = (
        <EmptyState
            heading={i18next.t("transfer_log.empty_state.heading")}
            image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png">
        </EmptyState>
    );
    const tableEmptyMarkup = isLoadingTransfers ? emptyRowMarkupLoading : emptyStateMarkup;

    const resourceName = {
        singular: "orders",
        plural: "orders"
    };

    const {
        selectedResources,
        allResourcesSelected,
        handleSelectionChange,
        removeSelectedResources
    } = useIndexResourceState(transferHistory);
    const [queryValue, setQueryValue] = useState(null);
    const [sortValue, setSortValue] = useState("today");

    const handleFilteredDateRemove = useCallback(() => {
        setSelectedDate(null);
        setQueryParams((prevParams) => ({...prevParams, interval: {since: '', until: ''}}));
        refetchTransfers();
    }, []);
    const handleQueryValueRemove = useCallback(() => setQueryValue(null), []);
    const handleClearAll = useCallback(() => {
        handleFilteredDateRemove();
        handleQueryValueRemove();
    }, [handleQueryValueRemove, handleFilteredDateRemove]);
    const handleSortChange = useCallback((value) => setSortValue(value), []);

    // Memoized handlers
    const handleRetries = useCallback(async () => {
        try {
            const response = await authenticatedFetch('/api/support/retry_transfers', {
                method: 'POST',
                body: JSON.stringify({
                    order_ids: selectedResources,
                    shop,
                    retry_all,
                    single_error_id: singleErrorId
                })
            });
            const data = await response;
            
            setShowBanner(true);
            setErrorBannerTone(data.success ? "success" : "critical");
            
            if (data.success) {
                removeSelectedResources(selectedResources);
                refetchTransfers();
            }
        } catch (error) {
            console.error('Error in handleRetries:', error);
            setShowBanner(true);
            setErrorBannerTone("critical");
        }
    }, [selectedResources, shop, retry_all, singleErrorId]);

    const handleDelete = useCallback(async (entity_type, all) => {
        try {
            const response = await authenticatedFetch('/api/support/delete_rows', {
                method: 'POST',
                body: JSON.stringify({
                    order_ids: selectedResources,
                    shop,
                    entity_type,
                    all
                })
            });
            const data = await response;
            
            setShowBanner(true);
            setErrorBannerTone(data.success ? "success" : "critical");
            
            if (data.success) {
                removeSelectedResources(selectedResources);
                refetchTransfers();
            }
        } catch (error) {
            console.error('Error in handleDelete:', error);
            setShowBanner(true);
            setErrorBannerTone("critical");
        }
    }, [selectedResources, shop, removeSelectedResources, refetchTransfers]);

    const retrySingleError = (errorId) => {
        setRetryAll(false);
        setSingleErrorId(errorId);
        handleRetries().then(r => refetchTransfers());
    }

    const handleAllRetries = () => {
        setRetryAll(true);
        handleRetries();
    };

    const promotedBulkActions = useMemo(() => [
        {
            content: 'Selektion neu übertragen',
            onAction: handleRetries
        },
        {
            content: 'Alle Fehler neu übertragen',
            onAction: handleAllRetries
        },
        {
            content: 'Fehler löschen',
            onAction: () => handleDelete('Error', false)
        },
        {
            content: 'Alle Fehler löschen',
            onAction: () => handleDelete('Error', true)
        },
        {
            content: 'Sync Infos löschen',
            onAction: () => handleDelete('SyncInfo', false)
        },
        {
            content: 'Alle Sync Infos löschen',
            onAction: () => handleDelete('SyncInfo', true)
        }
    ], [handleRetries, handleAllRetries, handleDelete]);

    const [selectedDate, setSelectedDate] = useState();
    function parseDate(inputDate) {
        const date = new Date(inputDate);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-based
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    const handleDateSelection = useCallback(
        (range) => {
            if (range === null) return;
            setSelectedDate(range);
            setQueryParams((prevParams) => ({...prevParams, interval: {
                since: parseDate(range.period.since),
                until: parseDate(range.period.until)
                }}));
            refetchTransfers();
        }
    )

    const handleErrorCopy = useCallback((errorText) => {
        navigator.clipboard.writeText(errorText);
        setInternalErrorTone('warning');
        setTimeout(() => {
            setInternalErrorTone('critical');
        }, 1000);
    }, []);

    const filters = useMemo(() => [
        {
            key: 'date',
            label: 'Erstellungsdatum',
            filter: (
                <DateRangePicker 
                    title={'Übertragungszeitraum'} 
                    onChange={handleDateSelection} 
                    defaultRange={selectedDate}
                />
            ),
            shortcut: true,
        }
    ], [handleDateSelection, selectedDate]);

    const appliedFilters = useMemo(() => {
        return selectedDate && !isEmpty(selectedDate)
            ? [
                {
                    key: "date",
                    label: disambiguateLabel("Erstellungsdatum", `${parseDate(selectedDate.period.since)} - ${parseDate(selectedDate.period.until)}`),
                    onRemove: handleFilteredDateRemove
                }
            ]
            : [];
    }, [selectedDate, handleFilteredDateRemove]);

    // Filter-Tabs
    const [selectedTab, setSelectedTab] = useState(0);

    const handleTabChange = useCallback((selectedTabIndex) => {
        setSelectedTab(selectedTabIndex);
        setCurrentPage(1);
        setQueryParams((prevParams) => ({...prevParams, filter: tabs[selectedTabIndex].filter}));
    }, []);


    const handleSearchChange = (searchValue) => {
        setCurrentPage(1);
        setQueryParams((prevParams) => ({...prevParams, search: searchValue}));
    }
    const resetSearch = useCallback(() => {
        setQueryParams((prevParams) => ({...prevParams, search: ''}));
    }, []);

    const handleSearchChangeDebounced = useCallback(debounce(handleSearchChange, 500), []);

    const tabs = [
        {
            id: 'tab-all',
            content: i18next.t("transfer_log.tabs.all"),
            filter: ''
        },
        {
            id: 'tab-invoice',
            filter: 'Invoice',
            content: i18next.t("transfer_log.tabs.invoice")
        },
        {
            id: 'tab-credit-note',
            filter: 'Refund',
            content: i18next.t("transfer_log.tabs.credit_note")
        },
        {
            id: 'tab-transaction',
            filter: 'Transaction',
            content: i18next.t("transfer_log.tabs.transaction")
        },
        {
            id: 'tab-tahs',
            filter: 'Transaction Assignment Hint',
            content: "Mit TAHs"
        },
        {
            id: 'tab-import',
            filter: 'Import',
            content: "Importiert"
        },
        {
            id: 'tab-errors',
            filter: 'Error',
            content: i18next.t("transfer_log.tabs.errors")
        },
    ];

    const mapStatus = (status) => {
        switch (status) {
            case 'draft':
                return 'default';
            case 'open':
                return 'warning';
            case 'paid':
                return 'success';
            case 'paidoff':
                return 'success';
            case 'voided':
                return 'critical';
            default:
                return 'info';
        }
    }
    const mapProgress = (status) => {
        switch (status) {
            case 'draft':
                return 'incomplete';
            case 'open':
                return 'partiallyComplete';
            case 'paid':
                return 'complete';
            case 'paidoff':
                return 'complete';
            case 'voided':
                return 'complete';
            default:
                return 'incomplete';
        }
    }

    const mapErrorType = (error_type) => {
        switch (error_type) {
            case 'ShopifyAPI::Order':
                return 'RE:';
            case 'ShopifyAPI::Refund':
                return 'GS:';
            case 'tender_transaction':
                return 'TR:';
            case 'transaction_assignment_hint':
                return 'TAH:';
            default:
                return '';
        }
    }
    const rowMarkup = useMemo(() => transferHistory.map(
        (
            {id, url, order_id, date, invoices, credit_notes, transactions, amountSpent, tah, errors},
            index
        ) => (
            <IndexTable.Row
                id={id}
                key={id}
                selected={selectedResources.includes(id)}
                position={index}
                onClick={() => {
                }}
            >
                <IndexTable.Cell>
                    <Link url={`/support/OrderData?shop=${shop}&order=${id}`} target={"_blank"}>
                        <Text variant="bodyMd" fontWeight="bold" as="span">
                            {id}
                        </Text>
                    </Link>
                </IndexTable.Cell>
                <IndexTable.Cell>
                    <Link url={`/support/OrderData?shop=${shop}&order=${id}`} target={"_blank"}>
                        <Text variant="bodyMd" fontWeight="bold" as="span">
                            {order_id}
                        </Text>
                    </Link>
                </IndexTable.Cell>
                <IndexTable.Cell>
                    {i18next.t('{{val, datetime}}', {
                        val: new Date(date), formatParams: {
                            val: {
                                year: 'numeric', month: 'long', day: 'numeric',
                                hour: 'numeric', minute: 'numeric'
                            },
                        }
                    })}
                </IndexTable.Cell>
                <IndexTable.Cell>
                    <BlockStack>
                        {invoices.map(({title, status, target_id, last_action}, index) => (
                            target_id &&
                            <InlineStack style={{padding: "0.5rem"}}>
                                <Text variant="bodyMd" as="span" key={target_id}>
                                    <Badge
                                        tone={mapStatus(status)}
                                        progress={mapProgress(status)}
                                    >
                                        <InlineStack gap={100} blockAlign={'center'}>
                                            {title || last_action || i18next.t("transfer_log.no_title")}
                                            {last_action === 'Send Mail Job Queued' ? <Icon source={EmailNewsletterMajor} /> : last_action === 'Mailed' ? <Icon source={EmailMajor} /> : <Icon source={CancelMinor} />}
                                        </InlineStack>
                                    </Badge>
                                </Text>
                            </InlineStack>
                        ))}
                    </BlockStack>
                </IndexTable.Cell>
                <IndexTable.Cell>
                    <BlockStack>
                        {credit_notes.map(({title, status, target_id, last_action}, index) => (
                            target_id &&
                            <div style={{padding: "0.5rem"}}>
                                <Text variant="bodyMd" as="span" key={target_id}>
                                    <Badge
                                        tone={mapStatus(status)}
                                        progress={mapProgress(status)}
                                    >
                                        <InlineStack gap={100} blockAlign={'center'}>
                                            {title || last_action || i18next.t("transfer_log.no_title")}
                                            {last_action === 'Send Mail Job Queued' ? <Icon source={EmailNewsletterMajor} /> : last_action === 'Mailed' ? <Icon source={EmailMajor} /> : <Icon source={CancelMinor} />}
                                        </InlineStack>
                                    </Badge>
                                </Text>
                            </div>
                        ))}
                    </BlockStack>
                </IndexTable.Cell>
                <IndexTable.Cell>
                    <BlockStack>
                        {transactions.map(({amount, target_id}, index) => (
                            target_id &&
                            <Text variant="bodyMd" as="span" key={target_id}>
                                <Badge tone={"success"} progress="complete">
                                    {amount.replace(/\\/g, '')} €
                                </Badge>
                            </Text>
                        ))}
                    </BlockStack>
                </IndexTable.Cell>
                <IndexTable.Cell>
                    <BlockStack>
                        <Text variant="bodyMd" as="span">
                            <Badge tone={tah ? "success" : "attention"} progress="complete">
                                {tah ? tah.last_action === 'Created' ? 'TAH erstellt ' : 'Wird übertragen' : 'Kein TAH'}
                            </Badge>
                        </Text>
                    </BlockStack>
                </IndexTable.Cell>
                <IndexTable.Cell>
                    <BlockStack>
                        {errors.map(({title, doc_type, helpscout_id, error_type, error_info_internal, shopify_type, error_id}, index) => (
                            (<>
                                <Text variant="bodyMd" as="span" key={`error-${index}`}>
                                    <Button variant={'tertiary'} icon={RefreshMinor} onClick={() => retrySingleError(error_id)}/>
                                    <Badge tone={"critical"} progress={"complete"}>
                                        <Link monochrome={true} removeUnderline={true}  onClick={() => openBeacon(helpscout_id)}>
                                            {`${mapErrorType(shopify_type)} ${title}`}
                                        </Link>
                                    </Badge>
                                </Text>
                                <Tooltip content={error_info_internal} key={`error-tooltip-${index}`}>
                                    <Text variant="bodyMd" as="span" key={`error-${index}`}>
                                        <Button variant={'tertiary'} icon={ClipboardMinor} onClick={() => handleErrorCopy(error_info_internal)}/>
                                        <Badge tone={internalErrorTone} progress={"complete"}>
                                            {error_info_internal && error_info_internal.length > 20 ? error_info_internal.substring(0, 20) + "..." : "Error hat keine interne Info"}
                                        </Badge>
                                    </Text>
                                </Tooltip>
                            </>)
                        ))}
                    </BlockStack>
                </IndexTable.Cell>
            </IndexTable.Row>
        )
    ), [transferHistory, selectedResources, shop, retrySingleError, handleErrorCopy, internalErrorTone]);

    const TABLE_CONFIG = {
        pageSize: 10,
        statusMap: {
            draft: { tone: 'default', progress: 'incomplete' },
            open: { tone: 'warning', progress: 'partiallyComplete' },
            paid: { tone: 'success', progress: 'complete' },
            paidoff: { tone: 'success', progress: 'complete' },
            voided: { tone: 'critical', progress: 'complete' },
        }
    };

    return (
        <ErrorBoundary>
            <div>
                {isLoadingTransfers && <Loading/>}
                <Divider/>
                <Layout>
                    <Layout.Section>
                        <Card padding="200">
                            <Tabs tabs={tabs} selected={selectedTab} onSelect={handleTabChange} />
                            <Filters
                                queryValue={queryValue}
                                queryPlaceholder={i18next.t("transfer_log.search_placeholder")}
                                filters={filters}
                                appliedFilters={appliedFilters}
                                onQueryChange={handleSearchChangeDebounced}
                                onQueryClear={resetSearch}
                                onClearAll={handleClearAll}>
                                <Box padding="300">
                                    <Button 
                                        onClick={refetchTransfers} 
                                        icon={RefreshMajor}
                                        loading={isRefetchingTranfers}
                                    />
                                </Box>
                            </Filters>
                            <IndexTable
                                resourceName={resourceName}
                                emptyState={tableEmptyMarkup}
                                itemCount={transferHistory.length}
                                selectedItemsCount={
                                    allResourcesSelected ? "All" : selectedResources.length
                                }
                                onSelectionChange={handleSelectionChange}
                                hasMoreItems
                                promotedBulkActions={promotedBulkActions}
                                lastColumnSticky
                                loading={isLoadingTransfers || isRefetchingTranfers}
                                headings={[
                                    {title: "ID"},
                                    {title: i18next.t("transfer_log.table_headers.order")},
                                    {title: "Shopify " + i18next.t("transfer_log.table_headers.date")},
                                    {title: i18next.t("transfer_log.invoices")},
                                    {title: i18next.t("transfer_log.credit_notes")},
                                    {title: i18next.t("transfer_log.transactions")},
                                    {title: "TAHs"},
                                    {title: i18next.t("transfer_log.table_headers.errors")},
                                ]}
                            >
                                {rowMarkup}
                            </IndexTable>

                            {transferHistoryData?.totalPages > 1 && (
                                <div style={{padding: "1.5rem"}}>
                                    <BlockStack inlineAlign="center">
                                        <Pagination
                                            label={i18next.t("will_paginate.pagination_label", {
                                                page: currentPage,
                                                total: transferHistoryData?.totalPages
                                            })}
                                            hasPrevious={currentPage > 1}
                                            previousKeys={[74]}
                                            previousTooltip={i18next.t("will_paginate.previous_label")}
                                            onPrevious={() => setCurrentPage((prev) => prev - 1)}
                                            hasNext={currentPage < transferHistoryData?.totalPages}
                                            nextKeys={[75]}
                                            nextTooltip={i18next.t("will_paginate.next_label")}
                                            onNext={() => setCurrentPage((prev) => prev + 1)}
                                        />
                                    </BlockStack>
                                </div>
                            )}
                        </Card>
                    </Layout.Section>
                </Layout>
            </div>
        </ErrorBoundary>
    );

    function disambiguateLabel(key, value) {
        switch (key) {
            case "date":
                return `Datum: ${value}`;
            default:
                return value;
        }
    }
}

TransferLogTable.propTypes = {
    shop: PropTypes.string.isRequired,
    setErrorBannerTone: PropTypes.func.isRequired,
    setShowBanner: PropTypes.func.isRequired
};

export default memo(TransferLogTable);

