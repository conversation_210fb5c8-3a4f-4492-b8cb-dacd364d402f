import React from 'react';
import {TextField} from "@shopify/polaris";
import PropTypes from 'prop-types';

export default function CommonCouponFields({numberOfCoupons, setNumberOfCoupons, freeTrial, setFreeTrial}) {
    return (
        <>
            <TextField 
                label="Anzahl" 
                value={numberOfCoupons} 
                type="number" 
                onChange={setNumberOfCoupons}
            />
            <TextField 
                label="Kostenlose Zeit" 
                value={freeTrial} 
                type="number" 
                suffix="Tage" 
                onChange={setFreeTrial} 
            />
        </>
    );
}

CommonCouponFields.propTypes = {
    numberOfCoupons: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    setNumberOfCoupons: PropTypes.func.isRequired,
    freeTrial: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    setFreeTrial: PropTypes.func.isRequired,
}; 