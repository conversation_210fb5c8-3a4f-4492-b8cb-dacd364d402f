import React, {useCallback, useState, memo, useMemo} from "react";
import PropTypes from 'prop-types';
import {
    Badge, BlockStack, Box,
    Button,
    Card,
    Collapsible,
    DataTable, InlineStack, Layout, TextField,
} from "@shopify/polaris";
import {planNotFoundImage} from "../../assets";
import SettingRow from "./SettingRow";
import {useAuthenticatedFetch} from "../../hooks/useAuthenticatedFetch";

// PropTypes
const shopDataPropType = PropTypes.shape({
    current_plan: PropTypes.shape({
        plan_type: PropTypes.string,
        price: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        name: PropTypes.string,
        whatuget: PropTypes.array
    }),
    app_recurring_charges: PropTypes.object,
    import_plan: PropTypes.object,
    app_import_charges: PropTypes.object,
    shop_settings: PropTypes.object.isRequired,
    shop_info: PropTypes.arrayOf(PropTypes.shape({
        shopify_domain: PropTypes.string.isRequired,
        discount_percent: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        import_discount_percent: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        billing_activated: PropTypes.bool,
        import_unlocked: PropTypes.bool
    })).isRequired
});

// Utility functions
const formatPrice = (price) => {
    if (typeof price === 'string') {
        return price.startsWith('$') ? price : `$${price}`;
    }
    return `$${price}`;
};

// Components
const PlanBadges = memo(({ plan, shopInfo, isImport = false }) => {
    if (!plan) return null;
    
    return (
        <BlockStack>
            <InlineStack gap={200}>
                Aktueller Plan <Badge tone="info">{plan.plan_type || plan.type}</Badge>
                <Badge tone="critical">{formatPrice(plan.price)}</Badge>
                <Badge tone="attention">
                    {isImport ? shopInfo.import_discount_percent : shopInfo.discount_percent}% Rabatt
                </Badge>
                {isImport ? (
                    shopInfo.import_unlocked_at || shopInfo.import_manually_unlocked_at ?
                        <Badge tone="success">Import aktiv</Badge> : 
                        <Badge tone="critical">Import inaktiv</Badge>
                ) : (
                    shopInfo.billing_activated && <Badge tone="success">App aktiv</Badge>
                )}
            </InlineStack>
        </BlockStack>
    );
});

PlanBadges.propTypes = {
    plan: PropTypes.object,
    shopInfo: PropTypes.object.isRequired,
    isImport: PropTypes.bool
};

const ChargeBadges = memo(({ charge, plan, isImport = false }) => {
    if (!charge) return null;

    return (
        <BlockStack>
            <InlineStack gap={200}>
                {isImport ? 'One Time Charge' : 'Recurring Charge'} {charge && <Badge tone="info">#{charge.status}</Badge>}
                {charge.test && <Badge progress="partiallyComplete" tone="warning">#Test Charge</Badge>}
                <Badge tone="success">#{charge.createdAt}</Badge>
                {plan && <Badge tone="attention">{formatPrice(plan?.price)}</Badge>}
            </InlineStack>
        </BlockStack>
    );
});

ChargeBadges.propTypes = {
    charge: PropTypes.object,
    isImport: PropTypes.bool
};

function ShopInfosAndSettings({shopData, refetch}) {
    const authenticatedFetch = useAuthenticatedFetch();
    // State
    const [appPlanDiscount, setAppPlanDiscount] = useState('');
    const [importPlanDiscount, setImportPlanDiscount] = useState('');
    const [orderID, setOrderID] = useState('');
    const [openSettings, setOpenSettings] = useState(false);
    const [settingsExpanded, setSettingsExpanded] = useState(false);
    const [openInfos, setOpenInfos] = useState(false);
    const [infosExpanded, setInfosExpanded] = useState(false);

    // Memoized values
    const plan = useMemo(() => ({
        ...shopData.current_plan,
        type: shopData.current_plan?.name,
        features: shopData.current_plan?.features
    }), [shopData.current_plan]);

    const settingsRows = useMemo(() => 
        Object.entries(shopData.shop_settings).map(([key, value]) => [
            key, 
            <SettingRow key={key} setting={value}/>
        ])
    , [shopData]);

    const infosRows = useMemo(() => 
        Object.entries(shopData.shop_info[0]).map(([key, value]) => [
            key, 
            <SettingRow key={key} setting={value}/>
        ])
    , [shopData]);
console.log({shopData})
    // Handlers
    const handleAppPlanDiscount = useCallback((value) => setAppPlanDiscount(value), []);
    const handleImportPlanDiscount = useCallback((value) => setImportPlanDiscount(value), []);
    const handleOrderIDChange = useCallback((value) => setOrderID(value), []);

    const handleToggleSettings = useCallback(() => {
        setOpenSettings(prev => !prev);
        setSettingsExpanded(prev => !prev);
    }, []);

    const handleToggleInfos = useCallback(() => {
        setOpenInfos(prev => !prev);
        setInfosExpanded(prev => !prev);
    }, []);

    const applyDiscount = useCallback(async (type) => {
        try {
            const discount = type === 'app' ? appPlanDiscount : importPlanDiscount;
            const response = await authenticatedFetch("/api/support/apply_discount", {
                method: "POST",
                body: JSON.stringify({
                    discount,
                    shop: shopData.shop_info[0].shopify_domain,
                    plan: type
                })
            });

            if (response.success) {
                refetch();
                type === 'app' ? setAppPlanDiscount('') : setImportPlanDiscount('');
            }
        } catch (error) {
            console.error('Error applying discount:', error);
            // Here you might want to show an error message to the user
        }
    }, [appPlanDiscount, importPlanDiscount, shopData.shop_info, refetch, authenticatedFetch]);

    const handleViewOrder = useCallback(() => {
        window.open(
            `/support/OrderData?shop=${shopData.shop_info[0].shopify_domain}&order=${orderID}`,
            '_blank'
        );
    }, [shopData.shop_info, orderID]);

    // Render helpers
    const renderDiscountField = (value, onChange, onApply, type) => (
        <TextField
            type="number"
            autoComplete="off"
            value={value}
            connectedLeft={'Rabatt:'}
            onChange={onChange}
            suffix={'%'}
            min={0}
            max={100}
            connectedRight={
                <Button 
                    submit 
                    variant='primary' 
                    onClick={() => onApply(type)}
                >
                    Rabatt anwenden
                </Button>
            }
        />
    );

    return (
        <div style={{height: '100%'}}>
            <Layout>
                <Layout.AnnotatedSection
                    title="App Plan Informationen"
                    description="Hier findest du Informationen zu dem Plan der App in dem Shop">
                    <Card title="Dein aktueller Plan" sectioned>
                        {(!plan.type && !shopData.app_recurring_charges) &&
                            <p> ❌ Kein aktiver App Plan ❌</p>}
                        <BlockStack gap="200">
                            {plan.type && (
                                <>
                                    <PlanBadges plan={plan} shopInfo={shopData.shop_info[0]}/>
                                    <h1 className="Polaris-Header-Title">{plan.name}</h1>
                                </>
                            )}
                            {renderDiscountField(
                                appPlanDiscount,
                                handleAppPlanDiscount,
                                applyDiscount,
                                'app'
                            )}
                            {shopData.app_recurring_charges && <ChargeBadges charge={shopData.app_recurring_charges.table}
                                           plan={shopData.current_plan}
                            />}
                        </BlockStack>
                    </Card>
                </Layout.AnnotatedSection>

                <Layout.AnnotatedSection
                    title="App Import Plan Informationen"
                    description="Hier findest du Informationen zu dem Import Plan der App im Shop">
                    <Card title="Dein aktueller Plan" sectioned>
                        <BlockStack gap="200">
                            {(!shopData.import_plan && !shopData.app_import_charges) && (
                                <p> ❌ Kein aktiver Import ❌</p>
                            )}
                            {shopData.import_plan && (
                                <>
                                    <PlanBadges 
                                        plan={shopData.import_plan} 
                                        shopInfo={shopData.shop_info[0]} 
                                        isImport 
                                    />
                                    <h1 className="Polaris-Header-Title">{shopData.import_plan.name}</h1>
                                </>
                            )}
                            {renderDiscountField(
                                importPlanDiscount,
                                handleImportPlanDiscount,
                                applyDiscount,
                                'import'
                            )}
                            <ChargeBadges charge={shopData.app_import_charges.table}
                                          plan={shopData.import_plan}
                                          isImport />
                        </BlockStack>
                    </Card>
                </Layout.AnnotatedSection>

                <Layout.AnnotatedSection
                    title={'Auftrag Daten Vorschau'}
                    description={'Die Shopify Bestellnummer und Shopify Shop Domain des Kunden, den du unterstützen möchtest.'}
                >
                    <Card>
                        <Box padding="400">
                            <TextField
                                type="text"
                                autoComplete="off"
                                value={orderID}
                                connectedLeft={'Auftrag ID:'}
                                onChange={handleOrderIDChange}
                                connectedRight={
                                    <Button 
                                        submit 
                                        variant='primary' 
                                        onClick={handleViewOrder}
                                    >
                                        Auftrag Daten laden
                                    </Button>
                                }
                            />
                        </Box>
                    </Card>
                </Layout.AnnotatedSection>

                <Layout.AnnotatedSection
                    title="Die Shop Einstellungen"
                    description="Hier findest du Informationen zu der Einstellung der App in dem Shop">
                    <Button
                        onClick={handleToggleSettings}
                        ariaExpanded={openSettings}
                        ariaControls="settings-collapsible"
                        disclosure={settingsExpanded ? 'up' : 'down'}
                        fullWidth
                        textAlign="left"
                    >
                        {settingsExpanded ? 'Einstellungen verbergen' : 'Einstellungen anzeigen'}
                    </Button>
                    <Collapsible
                        open={openSettings}
                        id="settings-collapsible"
                        transition={{duration: '500ms', timingFunction: 'ease-in-out'}}
                        expandOnPrint
                    >
                        <DataTable
                            columnContentTypes={['text', 'text']}
                            headings={[
                                <div key="setting" style={{fontWeight: 'bold'}}>Einstellung</div>,
                                <div key="value" style={{fontWeight: 'bold'}}>Wert</div>
                            ]}
                            rows={settingsRows}
                        />
                    </Collapsible>
                </Layout.AnnotatedSection>

                <Layout.AnnotatedSection
                    title="Die Shop Informationen"
                    description="Hier findest du Informationen zu dem Shop">
                    <Button
                        onClick={handleToggleInfos}
                        ariaExpanded={openInfos}
                        disclosure={infosExpanded ? 'up' : 'down'}
                        fullWidth
                        textAlign="left"
                    >
                        {infosExpanded ? 'Infos verbergen' : 'Infos anzeigen'}
                    </Button>
                    <Collapsible
                        open={openInfos}
                        id="infos-collapsible"
                        transition={{duration: '500ms', timingFunction: 'ease-in-out'}}
                        expandOnPrint
                    >
                        <DataTable
                            columnContentTypes={['text', 'text']}
                            headings={[
                                <div key="setting" style={{fontWeight: 'bold'}}>Einstellung</div>,
                                <div key="value" style={{fontWeight: 'bold'}}>Wert</div>
                            ]}
                            rows={infosRows}
                        />
                    </Collapsible>
                </Layout.AnnotatedSection>

                <Layout.Section/>
            </Layout>
        </div>
    );
}

ShopInfosAndSettings.propTypes = {
    shopData: shopDataPropType.isRequired,
    refetch: PropTypes.func.isRequired
};

export default memo(ShopInfosAndSettings);