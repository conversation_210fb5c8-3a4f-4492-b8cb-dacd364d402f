import React, {useCallback, useState} from 'react';
import {<PERSON>, <PERSON><PERSON>, DatePicker, Divider, InlineStack, Tabs, TextField} from "@shopify/polaris";
import CustomDatePicker from "./CustomDatePicker";
import CommonCouponFields from "./CommonCouponFields";
import {useAuthenticatedFetch} from "../../hooks/useAuthenticatedFetch";
import PropTypes from 'prop-types';


export default function CouponTypesTab({refetchCoupons}) {
    const authenticatedFetch = useAuthenticatedFetch();

    const [numberOfCoupons, setNumberOfCoupons] = useState(0);
    const [freeTrial, setFreeTrial] = useState(0);
    const [numberOfRedeems, setNumberOfRedeems] = useState(0);
    const [validity, setValidity] = useState(0);
    const [individualText, setIndividualText] = useState();
    const [selected, setSelected] = useState(0);
    const [showFeedback, setShowFeedback] = useState(false);
    const [feedback, setFeedback] = useState('');
    const [customTextSet, setCustomTextSet] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    const handleTabChange = useCallback(
        (selectedTabIndex) => setSelected(selectedTabIndex),
        [],
    );

    const tabs = [
        {
            id: 'one-time-coupon',
            content: 'Einmal Gutschein',
            accessibilityLabel: 'All customers',
            panelID: 'one-time-coupon-content',
        },
        {
            id: 'multi-use-coupon',
            content: 'Mehrzweck Gutschein',
            panelID: 'multi-use-coupon-content',
        },
    ];

    const renderTab = () => {
        switch (selected) {
            case 0:
                return oneTimeCouponTab();
            case 1:
                return multiUseCouponTab();
            default:
                return null;
        }
    }

    const oneTimeCouponTab = () => {
        return (
            <div>
                <CommonCouponFields
                    numberOfCoupons={numberOfCoupons}
                    setNumberOfCoupons={setNumberOfCoupons}
                    freeTrial={freeTrial}
                    setFreeTrial={setFreeTrial}
                />
                <Divider borderColor="border"/>
                <Button variant={'tertiary'} onClick={handleSave} >Einmal Gutschein speichern</Button>
            </div>
        )
    }

    const multiUseCouponTab = () => {
        return (
            <div>
                <CommonCouponFields
                    numberOfCoupons={numberOfCoupons}
                    setNumberOfCoupons={setNumberOfCoupons}
                    freeTrial={freeTrial}
                    setFreeTrial={setFreeTrial}
                />
                <TextField label={'Anzahl an Einlösungen'} value={numberOfRedeems} type={'number'} onChange={(value) => {setNumberOfRedeems(value)}} />
                <TextField label={'Individualisierbares Element'} value={individualText} type={'text'}
                           onChange={(value) => {
                               setIndividualText(value);
                               value.length > 0 ? setCustomTextSet(true) : setCustomTextSet(false);
                           }}/>
                <CustomDatePicker setNewSelectedDate={setValidity} label={'Gültig bis'}/>
                <Divider borderColor="border"/>
                <Button variant={'tertiary'} onClick={handleSave}>Mehrzweck Gutschein speichern</Button>
            </div>
        )
    }

    const validateForm = (values) => {
        const errors = {};
        if (values.numberOfCoupons <= 0) errors.numberOfCoupons = 'Must be greater than 0';
        if (values.freeTrial < 0) errors.freeTrial = 'Must be 0 or greater';
        return errors;
    };

    const saveCoupon = async (params) => {
        return authenticatedFetch('/api/support/coupon_codes', {
            method: 'POST',
            body: JSON.stringify(params),
        });
    };

    const handleSave = async () => {
        try {
            setIsLoading(true);
            const params = {
                number_of_coupons: numberOfCoupons,
                free_days: freeTrial,
            };

            if (selected === 1) {
                params.type = 'campaign';
                params.redeem_counter = numberOfRedeems;
                params.validity = validity;
                params.individual_prefix = individualText;
            }

            const data = await saveCoupon(params);
            setFeedback('Gutschein erfolgreich erstellt');
            setShowFeedback(true);
            refetchCoupons();
            setFreeTrial(0);
            setNumberOfCoupons(0);
            setNumberOfRedeems(0);
            setValidity(0);
            setIndividualText('');
        } catch (error) {
            setFeedback('Error: ' + error.message);
        } finally {
            setIsLoading(false);
        }
    };

    CouponTypesTab.propTypes = {
        refetchCoupons: PropTypes.func.isRequired
    };

    return (
        <div>
            <Tabs tabs={tabs} selected={selected} onSelect={handleTabChange} fitted>
                {showFeedback && <Banner onDismiss={() => {setShowFeedback(false)}}>
                    {feedback}
                </Banner>}
                {renderTab()}
            </Tabs>
        </div>
    )
}