import { <PERSON><PERSON><PERSON><PERSON>outer } from "react-router-dom";

import { QueryProvider, PolarisProvider } from "../admin/components";
import { Frame } from "@shopify/polaris";
import ErrorBoundary from "./components/Support/ErrorBoundary";
import Router from "../shared_components/Router";

export default function Support() {
  // Any .tsx or .jsx files in /pages will become a route
  // See documentation for <Routes /> for more info
  const pages = import.meta.globEager("./pages/**/!(*.test.[jt]sx)*.([jt]sx)");

  return (
    <PolarisProvider>
      <ErrorBoundary>
        <BrowserRouter>
          <ErrorBoundary>
            <QueryProvider>
              <Frame>
                <Router pages={pages} prefix="/support" help={false} />
              </Frame>
            </QueryProvider>
          </ErrorBoundary>
        </BrowserRouter>
      </ErrorBoundary>
    </PolarisProvider>
  );
}
