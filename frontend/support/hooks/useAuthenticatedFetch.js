import getCsrfToken from "./getCsrfToken";

export function useAuthenticatedFetch() {
  return async (url, options = {}) => {
    const defaultHeaders = {
      "Content-Type": "application/json",
      "X-CSRF-Token": getCsrfToken(),
    };

    const response = await fetch(url, {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  };
}
