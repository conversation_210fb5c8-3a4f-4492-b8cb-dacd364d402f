import { Routes as ReactRouterRout<PERSON>, Route } from "react-router-dom";

export default function SupportRoutes({ pages }) {
  const routes = generateSupportRoutes(pages);

  const routeComponents = routes.map(({ path, component: Component }) => (
    <Route key={path} path={path} element={<Component />} />
  ));

  return (
    <ReactRouterRoutes>
      {routeComponents}
    </ReactRouterRoutes>
  );
}

function generateSupportRoutes(pages) {
    const supportPages = Object.keys(pages).filter(key => {
        // Check for both patterns since the files are in support/pages but imported as ./pages
        return key.startsWith('./pages/') && !key.includes('admin');
    });
    
    const routes = supportPages
        .map((key) => {
            let path = key
                .replace("./pages", "")
                .replace(/\.(t|j)sx?$/, "")
                .replace(/\/index$/i, "/")
                .replace(/\b[A-Z]/, (firstLetter) => firstLetter.toLowerCase())
                .replace(/\[(?:[.]{3})?(\w+?)\]/g, (_match, param) => `:${param}`);

            if (path.endsWith("/") && path !== "/") {
                path = path.substring(0, path.length - 1);
            }

            // Remove the /support prefix since it's handled by the parent route
            // path = path.replace(/^\/support/, '');


            if (!pages[key].default) {
                console.warn(`${key} doesn't export a default React component`);
            }
            // prefix of the support routes
            path = `/support${path}`;

            return {
                path,
                component: pages[key].default,
            };
        })
        .filter((route) => route.component);
    
    return routes;
}