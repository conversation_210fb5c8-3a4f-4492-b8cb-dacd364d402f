import React from "react";
import {<PERSON><PERSON>, <PERSON>, Divider, EmptyState, Frame, Image, Layout, Page} from "@shopify/polaris";
import {overviewImage} from "../assets";

export default function SupportSignin() {

    const login = () => {
        window.location.href = '/support_users/auth/google_oauth2';
    };

    return (
        <Page title={"Lexware Office Support"} subtitle={"Eshop-Guide Apps Support UI"}>
            <Divider />
            <div style={{paddingTop: '5%'}}>
                <Frame>
                    <Layout>
                        <Card>
                            <EmptyState
                            heading={"SUPPORT MITARBEITER ANMELDUNG ERFORDERLICH"}
                            image={overviewImage}
                            action={{
                                content: 'Mit Eshop Guide Google Konto einloggen',
                                onAction: login,
                            }}
                            >

                            </EmptyState>
                        </Card>
                    </Layout>
                </Frame>
            </div>
        </Page>
    );
}