import React, { useCallback, useEffect, useState, memo, useMemo } from "react";
import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Banner,
    BlockStack,
    Button,
    ButtonGroup,
    Card,
    Divider,
    Frame,
    Icon,
    InlineGrid,
    InlineStack,
    Layout,
    Page,
    Pagination,
    Tag,
    TextField
} from "@shopify/polaris";
import { useDataQuery } from "../hooks/useDataQuery";
import { useAuthenticatedFetch } from "../hooks/useAuthenticatedFetch";
import CodeEditor from '@uiw/react-textarea-code-editor';
import { ViewMinor, WandMinor } from '@shopify/polaris-icons';
import { getCsrfToken } from "../hooks/getCsrfToken";
import { useNavigate } from "react-router-dom";

// Constants
const API_ENDPOINTS = {
    SNIPPETS: '/api/support/snippets',
    PREVIEW: '/api/support/snippets/preview'
};

const EDITOR_STYLES = {
    fontFamily: 'ui-monospace,SFMono-Regular,SF Mono,Consolas,Liberation Mono,Menlo,monospace'
};

const PAGINATION_STYLES = {
    maxWidth: '700px',
    margin: 'auto',
    border: '1px solid var(--p-color-border)'
};

const CodeSnippets = memo(() => {
    const navigate = useNavigate();
    const authenticatedFetch = useAuthenticatedFetch();

    // State
    const [editorState, setEditorState] = useState({
        darkMode: true,
        isFirstButtonActive: true,
        snippetId: null,
        newSnippetName: '',
        newSnippetDescription: '',
        newSnippetCode: '',
        shopDomain: '',
        orderId: '',
        showCodePreview: false,
        previewBanner: {
            content: '',
            tone: 'success',
            title: ''
        }
    });

    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);

    // Data fetching
    const { 
        data: codeSnippets,
        isLoading: loadingCodeSnippets,
        refetch: refetchCodeSnippets,
        error: errorLoadingCodeSnippets 
    } = useDataQuery({
        url: `${API_ENDPOINTS.SNIPPETS}?page=${currentPage}`
    });

    // Effects
    useEffect(() => {
        if (!loadingCodeSnippets && codeSnippets) {
            setTotalPages(codeSnippets.total_pages);
        }
    }, [codeSnippets, loadingCodeSnippets]);

    // Handlers
    const handleEditorStateChange = useCallback((key, value) => {
        setEditorState(prev => ({
            ...prev,
            [key]: value
        }));
    }, []);

    const handleThemeToggle = useCallback((isDark) => {
        handleEditorStateChange('darkMode', isDark);
        handleEditorStateChange('isFirstButtonActive', isDark);
    }, [handleEditorStateChange]);

    const handlePageChange = useCallback((newPage) => {
        setCurrentPage(newPage);
        refetchCodeSnippets();
    }, [refetchCodeSnippets]);

    const saveCodeSnippet = useCallback(async () => {
        const { snippetId, newSnippetName, newSnippetDescription, newSnippetCode } = editorState;
        const url = snippetId ? `${API_ENDPOINTS.SNIPPETS}/${snippetId}` : API_ENDPOINTS.SNIPPETS;
        const method = snippetId ? 'PUT' : 'POST';

        try {
            await authenticatedFetch(url, {
                method,
                body: JSON.stringify({
                    name: newSnippetName,
                    description: newSnippetDescription,
                    code: newSnippetCode
                })
            });

            setEditorState(prev => ({
                ...prev,
                snippetId: null,
                newSnippetName: '',
                newSnippetDescription: '',
                newSnippetCode: ''
            }));
            refetchCodeSnippets();
        } catch (error) {
            console.error('Error:', error);
        }
    }, [editorState, refetchCodeSnippets, authenticatedFetch]);

    const deleteSnippet = useCallback(async (snippetId) => {
        try {
            await authenticatedFetch(`${API_ENDPOINTS.SNIPPETS}/${snippetId}`, {
                method: 'DELETE'
            });
            refetchCodeSnippets();
        } catch (error) {
            console.error('Error:', error);
        }
    }, [refetchCodeSnippets, authenticatedFetch]);

    const handlePreview = useCallback(async (newCode) => {
        const { shopDomain, orderId, newSnippetCode } = editorState;
        
        try {
            const data = await authenticatedFetch(API_ENDPOINTS.PREVIEW, {
                method: 'POST',
                body: JSON.stringify({
                    shop_domain: shopDomain,
                    order_id: orderId,
                    code: newCode ? newSnippetCode : snippet.code
                })
            });
            
            if (data.liquid_error || data.errors) {
                handleEditorStateChange('previewBanner', {
                    content: data.errors,
                    tone: 'critical',
                    title: 'Code Vorschau Fehler'
                });
            } else {
                handleEditorStateChange('previewBanner', {
                    content: data.preview,
                    tone: 'success',
                    title: 'Code Vorschau'
                });
            }
            
            handleEditorStateChange('showCodePreview', true);
            refetchCodeSnippets();
        } catch (error) {
            console.error('Error:', error);
        }
    }, [editorState, handleEditorStateChange, refetchCodeSnippets, authenticatedFetch]);

    // Memoized values
    const secondaryActions = useMemo(() => [
        {
            content: 'Coupons',
            accessibilityLabel: 'Secondary action label',
            onAction: () => navigate('/support/coupons'),
        },
        {
            content: 'Beacons',
            onAction: () => navigate('/support/beacons'),
        },
        {
            content: 'Code Snippets',
            onAction: () => navigate('/support/codeSnippets'),
        }
    ], [navigate]);

    const paginationInfo = useMemo(() => ({
        hasNextPage: currentPage < totalPages,
        hasPreviousPage: currentPage > 1,
        label: `${currentPage}-${totalPages}`
    }), [currentPage, totalPages]);

    // Render helpers
    const renderSnippetEditor = () => {
        const { 
            darkMode, snippetId, newSnippetName, newSnippetDescription, 
            newSnippetCode, shopDomain, orderId 
        } = editorState;

        return (
            <Layout.AnnotatedSection
                title="Neuen Code erstellen"
                description="Erstelle einen neuen Code Snippet"
            >
                <Card>
                    <BlockStack gap="200">
                        {snippetId && (
                            <Tag>
                                <InlineStack gap={100}>
                                    <Icon source={WandMinor} />
                                    {`Bearbeitung von Code Snippet ${snippetId} aktiv`}
                                </InlineStack>
                            </Tag>
                        )}
                        <TextField 
                            label="Name" 
                            value={newSnippetName}
                            placeholder="Code Snippet Name"
                            onChange={(value) => handleEditorStateChange('newSnippetName', value)}
                            labelAction={{
                                content: 'Speichern',
                                onAction: saveCodeSnippet,
                                disabled: !newSnippetName && !newSnippetDescription && !newSnippetCode
                            }}
                        />
                        <TextField 
                            label="Beschreibung" 
                            placeholder="Code Snippet Beschreibung"
                            value={newSnippetDescription}
                            onChange={(value) => handleEditorStateChange('newSnippetDescription', value)}
                            multiline={2}
                        />
                        <ButtonGroup variant="segmented">
                            <Button 
                                pressed={darkMode} 
                                onClick={() => handleThemeToggle(true)}
                            >
                                Dark
                            </Button>
                            <Button 
                                pressed={!darkMode}
                                onClick={() => handleThemeToggle(false)}
                            >
                                Light
                            </Button>
                        </ButtonGroup>
                        <CodeEditor
                            value={newSnippetCode}
                            data-color-mode={darkMode ? 'dark' : 'light'}
                            language="js"
                            onChange={(e) => handleEditorStateChange('newSnippetCode', e.target.value)}
                            placeholder="Liquid Code Snippet"
                            padding={15}
                            style={EDITOR_STYLES}
                        />
                        <InlineGrid columns={['oneHalf', 'oneHalf']} gap={200}>
                            <TextField 
                                placeholder="Shop Domain" 
                                label="Shop Domain"
                                value={shopDomain} 
                                onChange={(value) => handleEditorStateChange('shopDomain', value)}
                            />
                            <InlineGrid columns={['twoThirds', 'oneThird']}>
                                <TextField 
                                    placeholder="Order ID" 
                                    label="Order ID zum Testing"
                                    value={orderId} 
                                    onChange={(value) => handleEditorStateChange('orderId', value)}
                                />
                                <div style={{ margin: '30%' }}>
                                    <Button
                                        icon={ViewMinor}
                                        variant="plain"
                                        onClick={() => handlePreview(true)}
                                    />
                                </div>
                            </InlineGrid>
                        </InlineGrid>
                    </BlockStack>
                </Card>
            </Layout.AnnotatedSection>
        );
    };

    const renderSnippetsList = () => {
        if (loadingCodeSnippets || errorLoadingCodeSnippets) return null;

        return (
            <Layout.AnnotatedSection
                title="Liquid Code Snippets"
                description="Hier findest du alle Liquid Code Snippets"
            >
                <Card>
                    {codeSnippets.snippets.map((snippet) => (
                        <React.Fragment key={snippet.id}>
                            <p>{snippet.name}</p>
                            <p>{snippet.description}</p>
                            <ButtonGroup>
                                <Button
                                    variant="plain"
                                    tone="success"
                                    onClick={() => {
                                        setEditorState(prev => ({
                                            ...prev,
                                            snippetId: snippet.id,
                                            newSnippetCode: snippet.code,
                                            newSnippetName: snippet.name,
                                            newSnippetDescription: snippet.description
                                        }));
                                    }}
                                >
                                    Bearbeiten
                                </Button>
                                <Button
                                    variant="plain"
                                    tone="critical"
                                    onClick={() => deleteSnippet(snippet.id)}
                                >
                                    Löschen
                                </Button>
                            </ButtonGroup>
                            <Divider />
                        </React.Fragment>
                    ))}
                    <div style={PAGINATION_STYLES}>
                        <Pagination
                            onPrevious={() => handlePageChange(currentPage - 1)}
                            onNext={() => handlePageChange(currentPage + 1)}
                            type="table"
                            hasNext={paginationInfo.hasNextPage}
                            hasPrevious={paginationInfo.hasPreviousPage}
                            label={paginationInfo.label}
                        />
                    </div>
                </Card>
            </Layout.AnnotatedSection>
        );
    };

    const renderPreviewBanner = () => {
        const { showCodePreview, previewBanner } = editorState;
        if (!showCodePreview) return null;

        return (
            <Layout.AnnotatedSection title="Code Vorschau">
                <Banner
                    title={previewBanner.title}
                    tone={previewBanner.tone}
                    onDismiss={() => handleEditorStateChange('showCodePreview', false)}
                >
                    {previewBanner.content}
                </Banner>
            </Layout.AnnotatedSection>
        );
    };

    return (
        <Page 
            title="Lexware Office Support" 
            subtitle="Eshop-Guide Apps Support UI" 
            secondaryActions={secondaryActions}
        >
            <Divider />
            <Frame>
                <AppProvider>
                    <Page 
                        backAction={{ onAction: () => navigate('/support') }}
                        title="Liquid Code Snippets"
                    >
                        <Layout>
                            {renderSnippetEditor()}
                            {renderSnippetsList()}
                            {renderPreviewBanner()}
                        </Layout>
                    </Page>
                </AppProvider>
            </Frame>
        </Page>
    );
});

export default CodeSnippets;