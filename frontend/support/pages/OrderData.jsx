import React, { useState, useMemo, memo } from "react";
import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON>,
    Card,
    Divider,
    Frame,
    Layout,
    Loading,
    Page
} from "@shopify/polaris";
import { useDataQuery } from "../hooks/useDataQuery";
import { useLocation } from "react-router-dom";
import ReactJson from "react-json-view";

// Constants
const SECTIONS = {
    ORDER: {
        title: "Auftrag Daten",
        description: "Shopify Order Daten",
        name: "Order"
    },
    TRANSACTIONS: {
        title: "Transaktionen Daten",
        description: "Shopify Transaktionen Daten",
        name: "Transactions"
    },
    TENDER_TRANSACTION: {
        title: "Tender Transaktion Daten",
        description: "Shopify Tender Transaktion Daten",
        name: "Tender Transaction"
    },
    SYNC_INFOS: {
        title: "Sync Infos Daten",
        description: "Alle Übertragungen vom Auftrag",
        name: "Sync Infos"
    },
    ERRORS: {
        title: "Fehler Daten",
        description: "Alle Fehler vom Auftrag",
        name: "Fehler Infos"
    }
};

const OrderData = memo(() => {
    const queryParams = new URLSearchParams(useLocation().search);
    const shop = queryParams.get('shop');
    const order_id = queryParams.get('order');

    // State
    const [collapseStates, setCollapseStates] = useState({
        all: true,
        transactions: true,
        tenderTransaction: true,
        syncInfos: true,
        errors: true
    });

    // Data fetching
    const { 
        data: orderData,
        isLoading: isLoadingOrderData,
        error: errorOrderData 
    } = useDataQuery({
        url: `/api/support/order?shop=${shop}&order_id=${order_id}`
    });

    // Handlers
    const handleCollapseToggle = (section) => {
        setCollapseStates(prev => ({
            ...prev,
            [section]: !prev[section]
        }));
    };

    // Memoized components
    const renderSection = useMemo(() => (section, data, collapseKey) => {
        const isCollapsed = collapseStates[collapseKey];
        const sectionConfig = SECTIONS[section];

        return (
            <Layout.AnnotatedSection
                title={sectionConfig.title}
                description={sectionConfig.description}
            >
                <Card sectioned>
                    <Button 
                        variant="plain"
                        onClick={() => handleCollapseToggle(collapseKey)}
                    >
                        {`Alle ${sectionConfig.title} ${isCollapsed ? 'ausklappen' : 'einklappen'}`}
                    </Button>
                    <ReactJson 
                        src={data} 
                        collapsed={isCollapsed} 
                        name={sectionConfig.name}
                        iconStyle="circle"
                    />
                    {!isCollapsed && (
                        <Button 
                            variant="plain"
                            onClick={() => handleCollapseToggle(collapseKey)}
                        >
                            Einklappen
                        </Button>
                    )}
                </Card>
            </Layout.AnnotatedSection>
        );
    }, [collapseStates]);

    const errorBanner = useMemo(() => {
        if (!errorOrderData) return null;

        return (
            <Banner
                title="Auftrag Daten Fehler"
                tone="critical"
            >
                <p>
                    Es gab ein Fehler beim Laden der Auftrag Daten. Bitte es später noch einmal versuchen.
                    Bitte prüfen, ob die gegebene Auftrag ID korrekt ist.
                    Refunds können nicht angezeigt werden.
                    {errorOrderData.message}
                </p>
            </Banner>
        );
    }, [errorOrderData]);

    const pageContent = useMemo(() => {
        if (isLoadingOrderData) {
            return <Loading />;
        }

        if (!orderData) {
            return null;
        }

        return (
            <Layout>
                {renderSection('ORDER', orderData.order, 'all')}
                {renderSection('TRANSACTIONS', orderData.transactions, 'transactions')}
                {renderSection('TENDER_TRANSACTION', orderData.tender_transaction, 'tenderTransaction')}
                {renderSection('SYNC_INFOS', orderData.sync_infos, 'syncInfos')}
                {renderSection('ERRORS', orderData.errors, 'errors')}
            </Layout>
        );
    }, [isLoadingOrderData, orderData, renderSection]);

    return (
        <AppProvider>
            <Page title="Lexware Office Support" subtitle="Eshop-Guide Apps Support UI" fullWidth>
                <Divider />
                <Frame>
                    <AppProvider>
                        <Page
                            backAction={{ url: '/api/support' }}
                            title={`${shop} Order Daten ${order_id}`}
                            fullWidth
                        >
                            {errorBanner}
                            {pageContent}
                        </Page>
                    </AppProvider>
                </Frame>
            </Page>
        </AppProvider>
    );
});

export default OrderData;