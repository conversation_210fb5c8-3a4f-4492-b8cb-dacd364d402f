import React, { useCallback, useEffect, useState, memo, useMemo } from "react";
import {
    App<PERSON><PERSON><PERSON>,
    Badge,
    BlockStack,
    Button,
    Card,
    Divider,
    Frame,
    IndexTable,
    InlineStack,
    Layout,
    Loading,
    Page,
    Pagination,
    Select,
    TextField,
    useIndexResourceState
} from "@shopify/polaris";
import { ClipboardMinor } from '@shopify/polaris-icons';
import { useDataQuery } from "../hooks/useDataQuery";
import { useAuthenticatedFetch } from "../hooks/useAuthenticatedFetch";
import CouponTypesTab from "../components/Support/CouponTypesTab";
import { getCsrfToken } from "../hooks/getCsrfToken";
import { useNavigate } from "react-router-dom";
import translations from "@shopify/polaris/locales/en.json";

// Constants
const API_ENDPOINTS = {
    COUPON_CODES: '/api/support/coupon_codes'
};

const COPY_TIMEOUT = 2000;

const NAVIGATION_ACTIONS = [
    {
        content: 'Coupons',
        accessibilityLabel: 'Secondary action label',
        path: '/support/coupons'
    },
    {
        content: 'Beacons',
        path: '/support/beacons'
    },
    {
        content: 'Code Snippets',
        path: '/support/codeSnippets'
    }
];

const Coupons = memo(() => {
    const navigate = useNavigate();
    const authenticatedFetch = useAuthenticatedFetch();

    // State
    const [editorState, setEditorState] = useState({
        showCreateCoupon: false,
        copiedCode: ""
    });

    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);

    // Data fetching
    const { 
        data: coupons,
        isLoading: isLoadingCoupons,
        refetch: refetchCoupons,
        error: errorCoupons 
    } = useDataQuery({
        url: `${API_ENDPOINTS.COUPON_CODES}?page=${currentPage}`
    });

    // Effects
    useEffect(() => {
        if (!isLoadingCoupons && coupons) {
            setTotalPages(coupons.total_pages);
        }
    }, [coupons, isLoadingCoupons]);

    // Handlers
    const handleEditorStateChange = useCallback((key, value) => {
        setEditorState(prev => ({
            ...prev,
            [key]: value
        }));
    }, []);

    const handlePageChange = useCallback((newPage) => {
        setCurrentPage(newPage);
        refetchCoupons();
    }, [refetchCoupons]);

    const copyToClipboard = useCallback((couponCode) => {
        navigator.clipboard.writeText(couponCode);
        handleEditorStateChange('copiedCode', couponCode);
        setTimeout(() => {
            handleEditorStateChange('copiedCode', "");
        }, COPY_TIMEOUT);
    }, [handleEditorStateChange]);

    const handleDeleteCoupon = useCallback(async (id) => {
        try {
            await authenticatedFetch(`${API_ENDPOINTS.COUPON_CODES}/${id}`, {
                method: 'DELETE'
            });
            refetchCoupons();
        } catch (error) {
            console.error('Error deleting coupon:', error);
        }
    }, [refetchCoupons, authenticatedFetch]);

    // Memoized values
    const couponCodes = useMemo(() => {
        if (isLoadingCoupons) return [];

        return coupons.coupon_codes.map(coupon => ({
            id: coupon.id,
            couponCode: coupon.coupon_code,
            freeDays: coupon.free_days,
            date: coupon.created_at,
            redeemCounter: coupon.redeem_counter,
            redeemed: coupon.redeemed,
            validity: coupon.validity,
            shop: coupon.shop_id
        }));
    }, [coupons, isLoadingCoupons]);

    const paginationInfo = useMemo(() => ({
        hasNextPage: currentPage < totalPages,
        hasPreviousPage: currentPage > 1,
        label: `${currentPage}-${totalPages}`
    }), [currentPage, totalPages]);

    const secondaryActions = useMemo(() => 
        NAVIGATION_ACTIONS.map(action => ({
            ...action,
            onAction: () => navigate(action.path)
        }))
    , [navigate]);

    // Index Table setup
    const resourceName = {
        singular: 'Coupon Code',
        plural: 'Coupon Codes'
    };

    const { selectedResources, allResourcesSelected, handleSelectionChange } = 
        useIndexResourceState(couponCodes);

    // Render helpers
    const renderCouponRow = useCallback(({
        id,
        couponCode,
        freeDays,
        date,
        redeemCounter,
        redeemed,
        validity,
        shop
    }, index) => (
        <IndexTable.Row
            id={id}
            key={id}
            selected={selectedResources.includes(id)}
            position={index}
        >
            <div style={{ padding: '12px 16px', width: '100%' }}>
                <BlockStack gap="100">
                    <InlineStack align="space-between">
                        <div style={{ display: 'flex' }}>
                            {editorState.copiedCode === couponCode ? (
                                <Badge tone="success">Copied!</Badge>
                            ) : (
                                <Button 
                                    icon={ClipboardMinor} 
                                    variant="monochromePlain" 
                                    tone="critical" 
                                    onClick={() => copyToClipboard(couponCode)}
                                >
                                    {couponCode}
                                </Button>
                            )}
                            <Badge>{freeDays} Tage kostenlos</Badge>
                        </div>
                        <Button 
                            variant="plain" 
                            tone="critical" 
                            onClick={() => handleDeleteCoupon(id)}
                        >
                            Löschen
                        </Button>
                    </InlineStack>
                    Erstellt am: {date.split('T')[0]}
                    <InlineStack align="space-between">
                        Coupon kann {redeemCounter} mal eingelöst werden.
                    </InlineStack>
                    <InlineStack align="start" gap="100">
                        <Badge tone={redeemed ? 'warning' : 'success'}>
                            {redeemed ? 'Eingelöst' : 'Einlösbar'}
                        </Badge>
                        {validity && (
                            <Badge tone="info">
                                Gültig bis {validity.split('T')[0]}
                            </Badge>
                        )}
                        {shop && (
                            <Badge tone="critical">
                                Dem Shop {shop} zugewiesen
                            </Badge>
                        )}
                    </InlineStack>
                </BlockStack>
            </div>
        </IndexTable.Row>
    ), [selectedResources, editorState.copiedCode, copyToClipboard, handleDeleteCoupon]);

    if (isLoadingCoupons) {
        return <Loading />;
    }

    if (errorCoupons) {
        return <p>Error loading coupons</p>;
    }

    return (
        <Page 
            title="Lexware Office Support" 
            subtitle="Eshop-Guide Apps Support UI" 
            secondaryActions={secondaryActions}
        >
            <Divider />
            <Frame>
                <AppProvider i18n={translations}>
                    <Page
                        backAction={{ onAction: () => navigate('/support') }}
                        title="Coupons"
                        primaryAction={{
                            content: 'Coupon Code erstellen',
                            disabled: false,
                            onAction: () => handleEditorStateChange('showCreateCoupon', true)
                        }}
                    >
                        <Layout>
                            {editorState.showCreateCoupon && (
                                <Layout.AnnotatedSection
                                    title="Neuen Coupon Code erstellen"
                                    description="Erstelle einen neuen Gutscheincode, der dem Kunden einen bestimmten Zeitraum kostenlosen Zugang zu lexoffice ermöglicht."
                                >
                                    <Card>
                                        <CouponTypesTab refetchCoupons={refetchCoupons} />
                                    </Card>
                                </Layout.AnnotatedSection>
                            )}
                            <Layout.AnnotatedSection
                                title="Coupon Codes"
                                description="Coupon Codes sind Codes, die dem Kunden einen bestimmten Zeitraum kostenlosen Zugang zu lexoffice ermöglichen."
                            >
                                <Card>
                                    <div style={{ width: '100%' }}>
                                        <IndexTable
                                            resourceName={resourceName}
                                            itemCount={couponCodes.length}
                                            selectedItemsCount={
                                                allResourcesSelected ? 'All' : selectedResources.length
                                            }
                                            condensed
                                            onSelectionChange={handleSelectionChange}
                                            headings={[
                                                { title: 'Coupon Code' },
                                                { title: 'Kostenlose Tage' },
                                                { title: 'Datum' },
                                                { title: 'Anzahl an Einlösungen', alignment: 'end' },
                                                { title: 'Eingelöst' },
                                                { title: 'Gültigkeit' },
                                                { title: 'Shop' }
                                            ]}
                                        >
                                            {couponCodes.map((coupon, index) => 
                                                renderCouponRow(coupon, index)
                                            )}
                                        </IndexTable>
                                    </div>
                                    <div style={{
                                        maxWidth: '700px',
                                        margin: 'auto',
                                        border: '1px solid var(--p-color-border)'
                                    }}>
                                        <Pagination
                                            onPrevious={() => handlePageChange(currentPage - 1)}
                                            onNext={() => handlePageChange(currentPage + 1)}
                                            type="table"
                                            hasNext={paginationInfo.hasNextPage}
                                            hasPrevious={paginationInfo.hasPreviousPage}
                                            label={paginationInfo.label}
                                        />
                                    </div>
                                </Card>
                            </Layout.AnnotatedSection>
                        </Layout>
                    </Page>
                </AppProvider>
            </Frame>
        </Page>
    );
});

export default Coupons;