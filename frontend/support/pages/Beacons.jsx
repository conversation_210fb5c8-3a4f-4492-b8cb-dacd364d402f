import React, { useCallback, useEffect, useState, memo, useMemo } from "react";
import {
    App<PERSON><PERSON><PERSON>,
    Badge,
    BlockStack,
    Button,
    Card,
    Checkbox,
    Divider,
    Frame,
    IndexTable,
    InlineStack,
    Layout,
    Loading,
    Page,
    Select,
    TextField,
    useIndexResourceState
} from "@shopify/polaris";
import { ClipboardMinor } from '@shopify/polaris-icons';
import { useDataQuery } from "../hooks/useDataQuery";
import { useAuthenticatedFetch } from "../hooks/useAuthenticatedFetch";
import { useNavigate } from "react-router-dom";
import translations from "@shopify/polaris/locales/en.json";

// Constants
const API_ENDPOINTS = {
    BEACON_MESSAGES: '/api/support/beacon_messages'
};

const COPY_TIMEOUT = 2000;

const NAVIGATION_ACTIONS = [
    {
        content: 'Coupons',
        accessibilityLabel: 'Secondary action label',
        path: '/support/coupons'
    },
    {
        content: 'Beacons',
        path: '/support/beacons'
    },
    {
        content: 'Code Snippets',
        path: '/support/codeSnippets'
    }
];

const Beacons = memo(() => {
    const navigate = useNavigate();
    const authenticatedFetch = useAuthenticatedFetch();

    // State
    const [editorState, setEditorState] = useState({
        showBeaconMessagesEditor: false,
        beaconMessageName: '',
        beaconMessageHSID: '',
        beaconMessageDomain: '',
        isNewFeature: false,
        idCopied: false
    });

    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);

    // Data fetching
    const { 
        data: beacons,
        isLoading: isLoadingBeacons,
        refetch: refetchBeacons,
        error: errorBeacons 
    } = useDataQuery({
        url: `${API_ENDPOINTS.BEACON_MESSAGES}?page=${currentPage}`
    });

    // Effects
    useEffect(() => {
        if (!isLoadingBeacons && beacons) {
            setTotalPages(beacons.total_pages);
        }
    }, [beacons, isLoadingBeacons]);

    // Handlers
    const handleEditorStateChange = useCallback((key, value) => {
        setEditorState(prev => ({
            ...prev,
            [key]: value
        }));
    }, []);

    const handlePageChange = useCallback((newPage) => {
        setCurrentPage(newPage);
        refetchBeacons();
    }, [refetchBeacons]);

    const copyToClipboard = useCallback((beaconId, hsId) => {
        window.Beacon('show-message', hsId, { force: true, delay: 1 });
        navigator.clipboard.writeText(hsId);
        handleEditorStateChange('idCopied', true);
        setTimeout(() => {
            handleEditorStateChange('idCopied', false);
        }, COPY_TIMEOUT);
    }, [handleEditorStateChange]);

    const handleSaveBeacon = useCallback(async () => {
        const { 
            beaconMessageName, 
            beaconMessageHSID, 
            beaconMessageDomain, 
            isNewFeature 
        } = editorState;

        try {
            await authenticatedFetch(API_ENDPOINTS.BEACON_MESSAGES, {
                method: 'POST',
                body: JSON.stringify({
                    name: beaconMessageName,
                    help_scout_id: beaconMessageHSID,
                    domain_name: beaconMessageDomain,
                    new_feature_message: isNewFeature
                })
            });

            refetchBeacons();
            handleEditorStateChange('showBeaconMessagesEditor', false);
        } catch (error) {
            console.error('Error saving beacon:', error);
        }
    }, [editorState, refetchBeacons, handleEditorStateChange, authenticatedFetch]);

    const handleDeleteBeacon = useCallback(async (id) => {
        try {
            await authenticatedFetch(`${API_ENDPOINTS.BEACON_MESSAGES}/${id}`, {
                method: 'DELETE'
            });
            refetchBeacons();
        } catch (error) {
            console.error('Error deleting beacon:', error);
        }
    }, [refetchBeacons, authenticatedFetch]);

    // Memoized values
    const beaconMessages = useMemo(() => {
        if (isLoadingBeacons) return [];

        return beacons.beacon_messages.map(beacon => ({
            id: beacon.id,
            name: beacon.name,
            date: beacon.created_at,
            hsId: beacon.help_scout_id,
            domainName: beacon.domain_name,
            newFeature: beacon.new_feature_message
        }));
    }, [beacons, isLoadingBeacons]);

    const paginationInfo = useMemo(() => ({
        hasNextPage: currentPage < totalPages,
        hasPreviousPage: currentPage > 1,
        label: `${currentPage}-${totalPages}`
    }), [currentPage, totalPages]);

    const secondaryActions = useMemo(() => 
        NAVIGATION_ACTIONS.map(action => ({
            ...action,
            onAction: () => navigate(action.path)
        }))
    , [navigate]);

    // Index Table setup
    const resourceName = {
        singular: 'Beacon Message',
        plural: 'Beacon Messages'
    };

    const { selectedResources, allResourcesSelected, handleSelectionChange } = 
        useIndexResourceState(beaconMessages);

    // Render helpers
    const renderBeaconEditor = () => {
        const { 
            showBeaconMessagesEditor,
            beaconMessageName,
            beaconMessageHSID,
            beaconMessageDomain,
            isNewFeature 
        } = editorState;

        if (!showBeaconMessagesEditor) return null;

        return (
            <Layout.AnnotatedSection
                title="Neuen Beacon erstellen"
                description="Die Beacons sind Nachrichten, die in der App angezeigt werden, um den Benutzer zu informieren oder zu unterstützen."
            >
                <Card>
                    <TextField 
                        label="Name" 
                        value={beaconMessageName} 
                        onChange={(value) => handleEditorStateChange('beaconMessageName', value)}
                    />
                    <TextField 
                        label="HS ID" 
                        value={beaconMessageHSID} 
                        onChange={(value) => handleEditorStateChange('beaconMessageHSID', value)} 
                    />
                    <Select
                        label="Beacon Bereich"
                        options={beacons.beacon_areas.map(domain => ({
                            label: domain.name,
                            value: domain.id
                        }))}
                        onChange={(value) => handleEditorStateChange('beaconMessageDomain', value)}
                        value={beaconMessageDomain}
                    />
                    <Checkbox
                        label="Neues Feature?"
                        checked={isNewFeature}
                        onChange={(value) => handleEditorStateChange('isNewFeature', value)}
                    />
                    <Divider borderColor="border" />
                    <Button variant="tertiary" onClick={handleSaveBeacon}>
                        Beacon speichern
                    </Button>
                </Card>
            </Layout.AnnotatedSection>
        );
    };

    const renderBeaconRow = useCallback(({
        id,
        name,
        date,
        hsId,
        domainName,
        newFeature
    }, index) => (
        <IndexTable.Row
            id={id}
            key={id}
            selected={selectedResources.includes(id)}
            position={index}
        >
            <div style={{ padding: '12px 16px', width: '100%' }}>
                <BlockStack gap="100">
                    <InlineStack align="space-between">
                        <div style={{ display: 'flex' }}>
                            {editorState.idCopied ? (
                                <Badge tone="success">Copied!</Badge>
                            ) : (
                                <Button 
                                    icon={ClipboardMinor} 
                                    variant="monochromePlain" 
                                    tone="critical" 
                                    onClick={() => copyToClipboard(id, hsId)}
                                >
                                    {hsId}
                                </Button>
                            )}
                            <Badge>{name}</Badge>
                        </div>
                        <Button 
                            variant="plain" 
                            tone="critical" 
                            onClick={() => handleDeleteBeacon(id)}
                        >
                            Löschen
                        </Button>
                    </InlineStack>
                    Erstellt am: {date.split('T')[0]}
                    <InlineStack align="start" gap="100">
                        <Badge tone="warning">{domainName}</Badge>
                        {newFeature && <Badge tone="success">Neues Feature</Badge>}
                    </InlineStack>
                </BlockStack>
            </div>
        </IndexTable.Row>
    ), [selectedResources, editorState.idCopied, copyToClipboard, handleDeleteBeacon]);

    if (isLoadingBeacons) {
        return <Loading />;
    }

    return (
        <Page 
            title="Lexware Office Support" 
            subtitle="Eshop-Guide Apps Support UI" 
            secondaryActions={secondaryActions}
        >
            <Divider />
            <Frame>
                <AppProvider i18n={translations}>
                    <Page
                        backAction={{ onAction: () => navigate('/support') }}
                        title="Beacon Messages"
                        primaryAction={{
                            content: 'Beacon erstellen',
                            disabled: editorState.showBeaconMessagesEditor,
                            onAction: () => handleEditorStateChange('showBeaconMessagesEditor', true)
                        }}
                    >
                        <Layout>
                            {renderBeaconEditor()}
                            <Layout.AnnotatedSection
                                title="Beacons"
                                description="Die Beacons sind Nachrichten, die in der App angezeigt werden, um den Benutzer zu informieren oder zu unterstützen."
                            >
                                <Card>
                                    <div style={{ width: '100%' }}>
                                        <IndexTable
                                            resourceName={resourceName}
                                            itemCount={beaconMessages.length}
                                            selectedItemsCount={
                                                allResourcesSelected ? 'All' : selectedResources.length
                                            }
                                            condensed
                                            onSelectionChange={handleSelectionChange}
                                            headings={[
                                                { title: 'Beacon' },
                                                { title: 'Date' },
                                                { title: 'Help Scout ID' },
                                                { title: 'Domain' },
                                                { title: 'Feature' }
                                            ]}
                                        >
                                            {beaconMessages.map((beacon, index) => 
                                                renderBeaconRow(beacon, index)
                                            )}
                                        </IndexTable>
                                    </div>
                                </Card>
                            </Layout.AnnotatedSection>
                        </Layout>
                    </Page>
                </AppProvider>
            </Frame>
        </Page>
    );
});

export default Beacons;