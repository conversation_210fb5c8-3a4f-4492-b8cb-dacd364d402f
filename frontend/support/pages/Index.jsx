import {
    <PERSON><PERSON>ple<PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    Divider,
    Form,
    FormLayout,
    Frame,
    Layout,
    Page,
    TextField
} from "@shopify/polaris";
import React, { useCallback, useMemo, useState, memo } from "react";
import { useNavigate } from "react-router-dom";
import { overviewImage, overviewImageSuccess } from "../assets";
import { useAuthenticatedFetch } from "../hooks/useAuthenticatedFetch";

// Constants
const API_ENDPOINTS = {
    AUTOCOMPLETE: '/api/support/autocomplete',
    VIEW_MERCHANT: '/support/merchant',
    VIEW_ORDER: '/support/OrderData'
};

const Index = memo(() => {
    const navigate = useNavigate();
    const authenticatedFetch = useAuthenticatedFetch();
    
    // State
    const [inputValue, setInputValue] = useState('');
    const [orderID, setOrderID] = useState('');
    const [suggestion, setSuggestion] = useState([]);
    const [selectedOptions, setSelectedOptions] = useState([]);
    const [options, setOptions] = useState([]);
    const [loading, setLoading] = useState(false);

    const handleShopDomainChange = useCallback(async (value) => {
        if (!value) {
            setSuggestion([]);
            return [];
        }

        try {
            const data = await authenticatedFetch(`${API_ENDPOINTS.AUTOCOMPLETE}?url=${value}`);
            return data.data;
        } catch (error) {
            console.error('Error fetching suggestions:', error);
            return [];
        }
    }, [authenticatedFetch]);

    const handleKeyDown = useCallback((event) => {
        if (event.key === 'Enter' || event.key === 'Tab') {
            setInputValue(suggestion || inputValue);
            setSuggestion('');
        } else if (event.key === 'Backspace') {
            setInputValue(inputValue);
            setSuggestion('');
        }
    }, [inputValue, suggestion]);

    const handleOrderIDChange = useCallback((value) => {
        setOrderID(value);
    }, []);

    const updateText = useCallback(async (value) => {
        setInputValue(value);
        if (!value) {
            setOptions([]);
            setLoading(false);
            return;
        }

        setLoading(true);
        try {
            const data = await handleShopDomainChange(value);
            if (!data || data.length === 0) return;

            const selectSuggestions = data.map(shop => ({
                value: shop,
                label: shop
            }));
            setSuggestion(selectSuggestions);

            const filterRegex = new RegExp(value, 'i');
            const resultOptions = selectSuggestions.filter(option => 
                option.label.match(filterRegex)
            );
            setOptions(resultOptions);
        } finally {
            setLoading(false);
        }
    }, [handleShopDomainChange]);

    const updateSelection = useCallback((selected) => {
        const selectedValue = selected.map((selectedItem) => {
            const matchedOption = options.find(option => 
                option.value.match(selectedItem)
            );
            return matchedOption?.label || '';
        });

        setSelectedOptions(selected);
        setInputValue(selectedValue[0] || '');
    }, [options]);

    // Memoized values
    const secondaryActions = useMemo(() => [
        {
            content: 'Coupons',
            accessibilityLabel: 'Secondary action label',
            onAction: () => navigate('/support/coupons'),
        },
        {
            content: 'Beacons',
            onAction: () => navigate('/support/beacons'),
        },
        {
            content: 'Code Snippets',
            onAction: () => navigate('/support/codeSnippets'),
        }
    ], [navigate]);

    const textField = useMemo(() => (
        <Autocomplete.TextField
            onChange={updateText}
            label="Shop Domain:"
            value={inputValue}
            placeholder="Search"
            autoComplete="off"
        />
    ), [inputValue, updateText]);

    return (
        <Page 
            title="Lexware Office Support"
            subtitle="Eshop-Guide Apps Support UI"
            secondaryActions={secondaryActions}
        >
            <Divider />
            <Layout>
                <img src={overviewImageSuccess} alt="Overview" width={700} />
                <Layout.AnnotatedSection
                    title="Kunden Shop"
                    description="Die Shopify Shop Domain des Kunden, den du unterstützen möchtest."
                >
                    <Card>
                        <Autocomplete
                            options={options}
                            selected={selectedOptions}
                            onSelect={updateSelection}
                            textField={textField}
                            loading={loading}
                        />
                        <div style={{ marginTop: '10px' }}>
                            <Button
                                variant="primary"
                                onClick={() => navigate(`${API_ENDPOINTS.VIEW_MERCHANT}?shop=${inputValue}`)}
                            >
                                Shop öffnen
                            </Button>
                        </div>
                    </Card>
                </Layout.AnnotatedSection>
                <Layout.AnnotatedSection
                    title="Auftrag Daten Vorschau"
                    description="Die Shopify Bestellnummer und Shopify Shop Domain des Kunden, den du unterstützen möchtest."
                >
                    <Card>
                        <div onKeyDown={handleKeyDown}>
                            <Form noValidate>
                                <FormLayout>
                                    <Autocomplete
                                        options={options}
                                        selected={selectedOptions}
                                        onSelect={updateSelection}
                                        textField={textField}
                                        loading={loading}
                                    />
                                    <TextField
                                        type="text"
                                        autoComplete="off"
                                        value={orderID}
                                        connectedLeft="Auftrag ID:"
                                        onChange={handleOrderIDChange}
                                        connectedRight={
                                            <Button 
                                                submit 
                                                variant="primary" 
                                                onClick={() => navigate(`${API_ENDPOINTS.VIEW_ORDER}?shop=${inputValue}&order=${orderID}`)}
                                            >
                                                Auftrag Daten laden
                                            </Button>
                                        }
                                    />
                                </FormLayout>
                            </Form>
                        </div>
                    </Card>
                </Layout.AnnotatedSection>
            </Layout>
        </Page>
    );
});

export default Index;