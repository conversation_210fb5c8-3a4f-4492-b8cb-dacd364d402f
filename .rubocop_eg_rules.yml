# EshopGuide Dev Team general cop rules.

# Do not change this file! It might be overridden in the next maintenance circle.
# For project specific rules, please use the rubocop.yml file.

Layout/ArgumentAlignment:
  EnforcedStyle: with_fixed_indentation
  IndentationWidth: 2
Layout/EndAlignment:
  EnforcedStyleAlignWith: keyword
Layout/FirstArgumentIndentation:
  EnforcedStyle: consistent
Layout/FirstArrayElementIndentation:
  EnforcedStyle: consistent
Layout/FirstHashElementIndentation:
  EnforcedStyle: consistent
Layout/IndentationWidth:
  Width: 2
Layout/MultilineMethodCallIndentation:
  EnforcedStyle: indented
  IndentationWidth: 2
Layout/MultilineOperationIndentation:
  EnforcedStyle: indented

Lint/MissingSuper:
  Enabled: false

Metrics/AbcSize:
  Max: 20
  Exclude:
    - "spec/**/*"
    - "lib/tasks/**/*"
    - "config/**/*"
    - "db/**/*"
Metrics/BlockLength:
  Exclude:
    - "spec/**/*"
    - "lib/tasks/**/*"
    - "config/**/*"
    - "db/**/*"
Metrics/CyclomaticComplexity:
  Max: 10
  Exclude:
    - "spec/**/*"
    - "lib/tasks/**/*"
    - "config/**/*"
    - "db/**/*"
Metrics/ClassLength:
  Max: 150
  Exclude:
    - "spec/**/*"
    - "lib/tasks/**/*"
    - "config/**/*"
Metrics/MethodLength:
  Max: 20
  Exclude:
    - "spec/**/*"
    - "lib/tasks/**/*"
    - "config/**/*"
    - "db/**/*"
Metrics/PerceivedComplexity:
  Max: 10
  Exclude:
    - "spec/**/*"
    - "lib/tasks/**/*"
    - "config/**/*"
    - "db/**/*"

Style/Documentation:
  Enabled: false
Style/QuotedSymbols:
  EnforcedStyle: double_quotes
Style/SafeNavigationChainLength:
  Max: 3
Style/StringLiterals:
  EnforcedStyle: double_quotes
  ConsistentQuotesInMultiline: true
Style/StringLiteralsInInterpolation:
  EnforcedStyle: double_quotes
