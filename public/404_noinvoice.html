<!DOCTYPE html>
<html>
<head>
  <title><PERSON>u diesem Auftrag existiert keine Rechnung in lexoffice</title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <style>
    body {
      background-color: #EFEFEF;
      color: #2E2F30;
      text-align: center;
      font-family: arial, sans-serif;
      margin: 0;
    }

    div.dialog {
      width: 95%;
      max-width: 33em;
      margin: 4em auto 0;
    }

    div.dialog > div {
      border: 1px solid #CCC;
      border-right-color: #999;
      border-left-color: #fff;
      border-bottom-color: #fff;
      border-top-left-radius: 9px;
      border-top-right-radius: 9px;
      background-color: white;
      padding: 7px 12% 0;
      box-shadow: 0 3px 8px rgba(50, 50, 50, 0.17);
    }

    h1 {
      font-size: 100%;
      line-height: 1.5em;
    }

    div.dialog > p {
      position: relative;
      margin: 0 0 1em;
      padding: 1em;
      background-color: #f4f4f4;
      border: 1px solid #CCC;
      border-right-color: #999;
      border-left: 60px solid #fff;
      border-bottom-color: #999;
      border-top-color: #999;
      border-radius: 4px;

      color: #666;
      box-shadow: 0 3px 8px rgba(50, 50, 50, 0.17);
    }

    div.dialog > p:first-of-type {
      border-top-color: #DADADA;
      border-top-left-radius: 0;
      border-top-right-radius:0;
    }

    button{
      display: inline-block;
      margin-top: 20px;
      background-color: #028ad9;
      font-weight: bold;
      color:#fff;
      text-decoration: none;
      padding: 20px;
      border-radius: 4px;
      border-color: transparent;
    }
    button:hover{
      background-color: #62ACD7;
    }

    .icon{
      position: absolute;
      top:10px;
      left: -50px;
      width:40px;
      height: 40px;
    }
    .ghost{
      background-image: url("/ghost.svg");
    }
  </style>
</head>

<body>
  <!-- This file lives in public/404.html -->
  <div class="dialog">
    <div>
      <h1>Zu diesem Auftrag existiert leider keine Rechnung.</h1>
    </div>
    <p><span class="icon ghost"></span>Evtl. wurde der Auftrag noch nicht verarbeitet oder der Auftrag existierte bereits, bevor die lexoffice App installiert wurde.<br/> Um ältere Aufträge zu lexoffice zu übertragen, kannst du in der App das Import-Feature freischalten.
      <br/><br/>Hilfe dazu findest du <a href="https://eshop-guide.helpscoutdocs.com/article/42-es-werden-keine-rechnungen-erstellt">hier</a> oder unter
      <a href="mailto://<EMAIL>"><EMAIL></a>
    </p>
    <button onclick="window.history.go(-2);">Zurück zum Auftrag</button>
  </div>
</body>
</html>
