<!DOCTYPE html>
<html>
<head>
  <title>Die App konnte leider nicht geladen werden</title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <style>
  body {
    background-color: #EFEFEF;
    color: #2E2F30;
    text-align: center;
    font-family: arial, sans-serif;
    margin: 0;
  }

  div.dialog {
    width: 95%;
    max-width: 33em;
    margin: 4em auto 0;
  }

  div.dialog > div {
    border: 1px solid #CCC;
    border-right-color: #999;
    border-left-color: #fff;
    border-bottom-color: #fff;
    border-top-left-radius: 9px;
    border-top-right-radius: 9px;
    background-color: white;
    padding: 7px 12% 0;
    box-shadow: 0 3px 8px rgba(50, 50, 50, 0.17);
  }

  h1 {
    font-size: 100%;
    line-height: 1.5em;
  }

  div.dialog > p {
    position: relative;
    margin: 0 0 1em;
    padding: 1em;
    background-color: #f4f4f4;
    border: 1px solid #CCC;
    border-right-color: #999;
    border-left: 60px solid #fff;
    border-bottom-color: #999;
    border-top-color: #999;
    border-radius: 4px;

    color: #666;
    box-shadow: 0 3px 8px rgba(50, 50, 50, 0.17);
  }

  div.dialog > p:first-of-type {
    border-top-color: #DADADA;
    border-top-left-radius: 0;
    border-top-right-radius:0;
  }

  .contact_support{
    display: inline-block;
    margin-top: 20px;
    background-color: #028ad9;
    font-weight: bold;
    color:#fff;
    text-decoration: none;
    padding: 20px;
    border-radius: 4px;
  }
  .contact_support:hover{
    background-color: #62ACD7;
  }

  .icon{
    position: absolute;
    top:10px;
    left: -50px;
    width:40px;
    height: 40px;
  }
  .emoji{
    background-image: url("/baffled_emoji.svg");
  }
  .chrome_logo{
    background-image: url("/chrome_logo.svg");
  }
  .support{
    background-image: url("/life_buoy.svg");
  }

  </style>
</head>

<body>
  <!-- This file lives in public/500.html -->
  <div class="dialog">
    <div>
      <h1>Die App konnte leider nicht geladen werden...</h1>
    </div>
    <p><span class="icon emoji"></span>
      Das kann aus unterschiedlichen Gr&uuml;nden der Fall sein. Entschuldige uns daf&uuml;r!<br/><br/>
      Versuche am Besten folgende Schritte, um den Fehler zu beheben:
    </p>
    <p><span class="icon chrome_logo"></span>
      Verwende die aktuellste Version deines Browsers, <strong>bestenfalls Google Chrome.</strong><br/><br/>
      <small>(Shopify schreibt dazu: "Um Shopify zu nutzen, musst du einen aktuellen Webbrowser verwenden. Wenn du nicht die neueste Version deines Browsers verwendest, kannst du m&ouml;glicherweise nicht auf deinen Shopify-Adminbereich zugreifen oder alle Funktionen darin nutzen. Die besten Ergebnisse erzielst du, wenn du mit der neuesten Version von Google Chrome auf Shopify zugreifst." <a href="https://help.shopify.com/de/manual/shopify-admin/supported-browsers" target="_blank">Hier der ausf&uuml;hrliche Shopify Hilfe-Artikel zu dem Thema.</a>)</small>
    </p>
    <p><span class="icon support"></span>
      Sollte das nicht helfen, wende dich gerne umgehend an unseren Support.<br/> Nenne uns in der E-Mail bitte <strong>deine interne Shopify URL</strong> (xyz.myshopify.com) und die Aktion, nach welcher du die Fehlermeldung erhalten hast.</br></br>
      Wir werden unser Bestes geben, dich bei der Fehlerbehebung zu unterst&uuml;tzen.
    </p>
    <a class="contact_support" href="mailto://<EMAIL>">Support kontaktieren</a>
  </div>
</body>
</html>
