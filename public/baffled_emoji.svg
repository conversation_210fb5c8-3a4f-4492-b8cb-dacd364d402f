<?xml version="1.0" encoding="utf-8"?><svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg" width="40px" height="40px" xmlns:xlink="http://www.w3.org/1999/xlink" style="width:100%;height:100%;background-size:initial;background-repeat-y:initial;background-repeat-x:initial;background-position-y:initial;background-position-x:initial;background-origin:initial;background-color:initial;background-clip:initial;background-attachment:initial;animation-play-state:paused" ><g class="ldl-scale" style="transform-origin:50% 50%;transform:rotate(0deg) scale(0.81, 0.81);animation-play-state:paused" ><linearGradient y2="79.344" x2="50" y1="22.624" x1="50" gradientUnits="userSpaceOnUse" id="a" style="animation-play-state:paused" ><stop stop-color="#f5e6c8" offset="0" style="stop-color:rgb(255, 191, 90);animation-play-state:paused" ></stop>
<stop stop-color="#f8c788" offset=".509" style="stop-color:rgb(255, 191, 90);animation-play-state:paused" ></stop>
<stop stop-color="#f5e6c8" offset="1" style="stop-color:rgb(255, 191, 90);animation-play-state:paused" ></stop></linearGradient>
<circle stroke-miterlimit="10" stroke-width="3" stroke="#f8b26a" fill="url(#a)" r="40" cy="50" cx="50" style="stroke:rgb(255, 191, 90);animation-play-state:paused" ></circle>
<path d="M29 64.667h43.333" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="5" stroke="#000" fill="none" style="animation-play-state:paused" ></path>
<g style="animation-play-state:paused" ><circle fill="#fff" r="10.75" cy="39.25" cx="34.25" style="fill:rgb(255, 255, 255);animation-play-state:paused" ></circle>
<circle r="5" cy="39.25" cx="34.25" style="animation-play-state:paused" ></circle></g>
<g style="animation-play-state:paused" ><circle fill="#fff" r="10.75" cy="39.25" cx="65.75" style="fill:rgb(255, 255, 255);animation-play-state:paused" ></circle>
<circle r="5" cy="39.25" cx="65.75" style="animation-play-state:paused" ></circle></g>
<metadata xmlns:d="https://loading.io/stock/" style="animation-play-state:paused" ><d:name style="animation-play-state:paused" >surprise</d:name>


<d:tags style="animation-play-state:paused" >surprise,wow,impressed,astonish,amaze,shock,astound,startling,remarkable</d:tags>


<d:license style="animation-play-state:paused" >by</d:license>


<d:slug style="animation-play-state:paused" >9t4mlo</d:slug></metadata></g><!-- generated by https://loading.io/ --></svg>