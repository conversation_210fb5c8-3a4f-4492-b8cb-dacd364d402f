# frozen_string_literal: true

namespace :webhooks do
  desc 'Register webhooks for all Shops'
  task register: :environment do
    original_adapter = ActiveJob::Base.queue_adapter

    begin
      # Temporarily set Sidekiq as the adapter
      ActiveJob::Base.queue_adapter = :sidekiq

      Shop.all.each do |shop|
        WebhooksManagerJob.perform_later(
          shop_domain: shop.shopify_domain,
          shop_token: shop.shopify_token
        )
      end
    ensure
      # Restore the original adapter
      ActiveJob::Base.queue_adapter = original_adapter
    end
  end
end
