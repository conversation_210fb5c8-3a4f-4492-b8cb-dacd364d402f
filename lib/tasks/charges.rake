# frozen_string_literal: true

namespace :charges do
  desc 'Find shops without active subscription'
  task find_shops_without_active_subscription: :environment do
    # Reset tmp_has_missing_recurring_charge to false for all shops
    # rubocop:disable Rails/SkipsModelValidations
    Shop.update_all(tmp_has_missing_recurring_charge: false)
    # rubocop:enable Rails/SkipsModelValidations
    shops = Shop.where('billing_plan_id > 0').where('billing_activated = true').where('plan_needs_update = false')

    total_shops_without_active_subscription = 0
    total_errors = 0
    development_plan = ShopifyBilling::BillingPlan.find_by(short_name: 'developer')

    shops.each do |shop|
      shop.with_shopify_session do
        recurring_charge = ShopifyAPI::RecurringApplicationCharge.current

        if recurring_charge.nil? || (recurring_charge.test && shop.billing_plan_id != development_plan.id)
          last_sync_info = SyncInfo.for_shop(shop.id).order(created_at: :desc).first
          total_shops_without_active_subscription += 1

          shop.update!(tmp_has_missing_recurring_charge: true)

          puts "#{shop.shopify_domain};#{shop.email};#{recurring_charge&.test&.to_s};#{last_sync_info&.created_at}"
        end
      rescue StandardError
        total_errors += 1
      end
    end

    puts '----------------------------------------'
    puts "Shops without active subscription: #{total_shops_without_active_subscription}"
    puts "Errors: #{total_errors}"
  end

  desc 'Find shops with import unlocked and without one time charge'
  task import_manually_unlocked_imports: :environment do
    shops = Shop.where('import_unlocked = true')
    total = shops.count
    count = 0

    shops.each do |shop|
      count += 1
      puts "Processing #{count} / #{total}... (#{shop.shopify_domain})"

      shop.with_shopify_session do
        one_time_charges = ShopifyAPI::ApplicationCharge.all
        active_one_time_charge = one_time_charges.find { |charge| charge.status == 'active' && !charge.test }

        shop.update!(import_manually_unlocked_at: Time.zone.now) if active_one_time_charge.nil?
      rescue StandardError => e
        puts "Error for shop #{shop.shopify_domain}: #{e.class}: #{e.message}"
      end
    end
  end

  desc 'Initial import of all active recurring charges'
  task import_recurring_charges: :environment do
    count = 0
    total = Shop.count

    ShopifyBilling::Charge.delete_all

    Shop.all.each do |shop|
      count += 1
      puts "Processing #{count} / #{total}..."

      # Skip shops without a billing plan
      next if shop.legacy_billing_plan_id.nil? || shop.legacy_billing_plan_id.zero?

      begin
        shop.reset_app_installation_cache
        shop.with_shopify_session do
          app_installation = GetAppInstallation.call

          if app_installation.activeSubscriptions&.any?
            app_installation.activeSubscriptions.each do |subscription|
              ShopifyBilling::Charge.find_or_create_by!(shopify_id: subscription.id) do |charge|
                charge.billing_plan_id = shop.legacy_billing_plan_id
                charge.created_at = subscription.createdAt
                charge.updated_at = subscription.createdAt
              end
            end
          end
        end
      rescue StandardError => e
        puts "Error for #{shop.shopify_domain}: #{e.class}: #{e.message}"
      end
    end
  end
end
