# frozen_string_literal: true

namespace :test_data do
  desc 'Generate test data for transfer logs (sync_infos and error_logs)'
  task :generate_transfer_logs, %i[shop_id count] => :environment do |_t, args|
    shop_id = args[:shop_id].to_i
    count = (args[:count] || 50_000).to_i

    unless Shop.exists?(shop_id)
      puts "Error: Shop with ID #{shop_id} does not exist"
      exit 1
    end

    puts "Generating #{count} test records for shop_id: #{shop_id}"

    # Constants for generating realistic data
    LAST_ACTIONS = ['Created', 'Job started', 'Excluded'].freeze # rubocop:disable Lint/ConstantDefinitionInBlock
    ERROR_TYPES = [ # rubocop:disable Lint/ConstantDefinitionInBlock
      '406 Not Acceptable',
      'uninitialized constant Shop::OAuthRefreshTokenService',
      'Connection refused',
      'Invalid taxRatePercentage',
      'Resource not found'
    ].freeze

    # Create records in batches to avoid memory issues
    batch_size = 1000
    total_batches = (count / batch_size.to_f).ceil
    records_created = 0
    orders_created = 0
    error_logs_created = 0

    # Starting order number as requested
    order_number_start = 10_000
    # Base time for all orders
    base_time = 1.year.ago

    ActiveRecord::Base.transaction do
      total_batches.times do |_batch|
        remaining = [batch_size, count - records_created].min

        # Generate orders first (each order will have multiple related records)
        # Sort orders by creation time to ensure correlation between time and IDs
        orders = Array.new((remaining / 5.0).ceil) do |i|
          current_order_number = order_number_start + orders_created + i
          # Generate a timestamp that increases with order number
          # This ensures correlation between timestamps and order numbers
          created_at = base_time + ((orders_created + i) * 2).hours

          # Generate shopify_id that correlates with timestamp
          # Higher shopify_id for later timestamps
          shopify_id = 5_000_000_000_000 + ((orders_created + i) * 1000) + rand(1..999)

          {
            id: shopify_id.to_s,
            name: "##{current_order_number}",
            created_at:,
            total_price: rand(10.0..1000.0).round(2)
          }
        end

        sync_info_batch = []
        error_logs_batch = []

        orders.each do |order|
          # For each order, create a group of related records
          # 1. Always create an Invoice
          invoice_id = SecureRandom.uuid
          sync_info_batch << {
            shop_id:,
            shopify_id: order[:id],
            target_id: invoice_id,
            created_at: order[:created_at],
            updated_at: order[:created_at] + rand(1..60).minutes,
            target_type: 'Invoice',
            last_action: 'Created',
            shopify_order_id: order[:id],
            shopify_order_name: order[:name],
            shopify_created_at: order[:created_at],
            extra_infos: {
              voucher_title: "RE#{format('%04d', rand(1..9999))}",
              status: 'open',
              total_price: order[:total_price],
              document_id: invoice_id
            }
          }

          # 2. Add a Transaction for the Invoice
          # Transaction ID also correlates with order timestamp
          transaction_id = order[:id].to_i + 1
          sync_info_batch << {
            shop_id:,
            shopify_id: transaction_id,
            target_id: SecureRandom.uuid,
            created_at: order[:created_at] + 5.minutes,
            updated_at: order[:created_at] + 5.minutes,
            target_type: 'Transaction',
            last_action: 'Created',
            shopify_order_id: order[:id],
            shopify_order_name: order[:name],
            shopify_created_at: order[:created_at],
            extra_infos: {
              amount: format('%.2f', order[:total_price])
            }
          }

          # 3. Sometimes (30% chance) add a Refund with its Transaction
          if rand < 0.3
            refund_amount = (order[:total_price] * rand(0.1..1.0)).round(2)
            refund_id = SecureRandom.uuid
            refund_created_at = order[:created_at] + rand(1..30).days

            # Refund ID also correlates with timestamp (later than order)
            refund_shopify_id = order[:id].to_i + 2

            # Add Refund
            sync_info_batch << {
              shop_id:,
              shopify_id: refund_shopify_id,
              target_id: refund_id,
              created_at: refund_created_at,
              updated_at: refund_created_at + rand(1..60).minutes,
              target_type: 'Refund',
              last_action: 'Created',
              shopify_order_id: order[:id],
              shopify_order_name: order[:name],
              shopify_created_at: refund_created_at,
              extra_infos: {
                voucher_title: "GS#{format('%04d', rand(1..9999))}",
                status: 'open',
                total_price: refund_amount,
                document_id: refund_id
              }
            }

            # Refund transaction ID also correlates with timestamp
            refund_transaction_id = refund_shopify_id + 1

            # Add Transaction for Refund
            sync_info_batch << {
              shop_id:,
              shopify_id: refund_transaction_id,
              target_id: SecureRandom.uuid,
              created_at: refund_created_at,
              updated_at: refund_created_at,
              target_type: 'Transaction',
              last_action: 'Created',
              shopify_order_id: order[:id],
              shopify_order_name: order[:name],
              shopify_created_at: refund_created_at,
              extra_infos: {
                amount: format('%.2f', -refund_amount)
              }
            }
          end

          # 4. Sometimes (20% chance) add an error log
          next unless rand < 0.2

          error_type = ERROR_TYPES.sample
          error_created_at = order[:created_at] + rand(1..5).minutes

          error_info_internal = if error_type.include?('406')
                                  {
                                    timestamp: error_created_at.iso8601,
                                    status: 406,
                                    error: 'Not Acceptable',
                                    path: '/v1/invoices',
                                    traceId: SecureRandom.hex(6),
                                    message: 'Invalid taxRatePercentage or does not match booking date or country code.'
                                  }.to_json
                                else
                                  error_type
                                end

          error_logs_batch << {
            shop_id:,
            exception_type: error_type,
            error_info_internal:,
            error_info_external: 'Ein unbekannter Fehler ist aufgetreten.',
            shopify_id: order[:id],
            shopify_name: order[:name],
            created_at: error_created_at,
            updated_at: error_created_at,
            shopify_type: 'ShopifyAPI::Order',
            order_id: order[:id]
          }
        end

        # Insert records in bulk
        SyncInfo.insert_all!(sync_info_batch) # rubocop:disable Rails/SkipsModelValidations
        if error_logs_batch.any?
          ErrorLog.insert_all!(error_logs_batch) # rubocop:disable Rails/SkipsModelValidations
          error_logs_created += error_logs_batch.size
        end

        records_created += sync_info_batch.size
        orders_created += orders.size
        puts "Progress: #{records_created} records created (#{orders_created} orders, #{error_logs_created} errors)"
      end
    end

    puts "\nCompleted generating test data:"
    puts "- #{records_created} sync_info records"
    puts "- #{orders_created} orders"
    puts "- #{error_logs_created} error_log records"
  end
end
