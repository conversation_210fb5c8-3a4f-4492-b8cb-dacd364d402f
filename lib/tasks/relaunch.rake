# frozen_string_literal: true

namespace :relaunch do
  desc 'For each shop queues a job that updates the invoice infos for all synced documents'
  task update_invoice_info: :environment do
    Shop.find_each do |shop|
      next unless shop.connected_to_lexoffice? && shop.plan_active?

      InvoiceDataRetrievalJob.perform_async(shop.id)
      Rails.logger.info("Queued InvoiceDataRetrievalJob for #{shop.name}")
    end
  end

  task register_lexoffice_webhooks: :environment do
    Shop.find_each do |shop|
      next unless shop.connected_to_lexoffice? && shop.plan_active?

      LexofficeWebhooksJob.perform_async(shop.id)
      Rails.logger.info("Queued LexofficeWebhooksJob for #{shop.name}")
    end
  end

  desc 'extra payment accounts'
  task extra_payment_accounts: :environment do
    all_types_to_skip = %w[amazon apple_pay credit_card google_pay klarna
                           samsung_pay shopify_pay klarna_pay_later sofort paypal].freeze
    Shop.find_each do |shop|
      shop.with_shopify_session do
        transactions = ShopifyAPI::TenderTransaction.all(params: { limit: 100 })
        # transactions = transactions.fetch_next_page while transactions.next_page?
        transactions.each do |transaction|
          next if all_types_to_skip.include?(transaction.payment_method.downcase)

          order = nil
          Retriable.retriable(tries: 2) do
            order = ShopifyAPI::Order.find(id: transaction.order_id)
          end
          next if order.nil? || order.errors.codes.any?

          if !order.nil? && shop.transaction_setting.present?
            puts FindAndMapExtraFinancialAccountsService.call(transaction.attributes['amount'].to_d, shop,
                                                              order)
          end
        end
      end
    rescue ActiveResource::UnauthorizedAccess
      next
    end
  end
end
