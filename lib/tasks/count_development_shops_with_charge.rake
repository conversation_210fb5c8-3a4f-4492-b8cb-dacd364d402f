# frozen_string_literal: true

namespace :eshopguide do
  desc 'Counts all development shops with a charge'
  task count_development_shops_with_charge: :environment do
    development_shops_with_charge = []
    count = 0
    total = Shop.count

    Shop.all.each do |shop|
      count += 1
      puts "Processing #{count} / #{total}..."

      begin
        shop.with_shopify_session do
          current_shop = ShopifyAPI::Shop.current
          charge = ShopifyAPI::RecurringApplicationCharge.current

          is_development_shop = current_shop.plan_name.include?('test') || current_shop.plan_name.include?('dev')
          has_active_charge = charge && charge.price.to_f.positive? && charge.test != true

          development_shops_with_charge << shop.shopify_domain if is_development_shop && has_active_charge
        end
      rescue StandardError => e
        puts "Error for #{shop.shopify_domain}: #{e.message}"
      end
    end

    puts "Development shops with active charge: #{development_shops_with_charge.count} / #{total}"
    puts development_shops_with_charge.join("\n")
  end
end
