# frozen_string_literal: true

# rubocop:disable Layout/LineLength

namespace :eshopguide do
  desc 'Gets all installed shops In<PERSON>s'
  task getshopinfos: :environment do
    Shop.all.each do |shop|
      ShopifyAPI::Session.setup({ api_key: ENV.fetch('SHOPIFY_CLIENT_API_KEY', nil),
                                  secret: ENV.fetch('SHOPIFY_CLIENT_API_SECRET', nil) })
      shop.with_shopify_session do
        remoteshop = ShopifyAPI::Shop.current

        shop.email = remoteshop.email
        shop.name = remoteshop.name
        shop.country = remoteshop.country
        shop.shop_owner = remoteshop.shop_owner
        shop.save
        ShopifyAPI::Base.clear_session
      end
    rescue StandardError => e
      puts "Shop lookup failed: #{e}"
    end
  end

  desc 'Get the registered webhooks'
  task :registeredwebhooks, [:shop] => [:environment] do |_t, args|
    p 'START'
    shop = Shop.find_by(name: args[:shop])
    unless shop.nil?
      begin
        p 'looking up Webhooks'
        ShopifyAPI::Session.setup({ api_key: ENV.fetch('SHOPIFY_CLIENT_API_KEY', nil),
                                    secret: ENV.fetch('SHOPIFY_CLIENT_API_SECRET', nil) })
        shop.with_shopify_session do
          pp ShopifyAPI::Webhook.all
          ShopifyAPI::Base.clear_session
        end
      rescue StandardError => e
        puts "Shop lookup failed for #{shop.name}: " + e.to_s
      end
    end
  end

  desc 'Resave all settings to reinit rules'
  task reinit_settings: :environment do
    SyncRule.all.destroy_all
    Shop.all.each do |shop|
      ShopifyAPI::Session.setup({ api_key: ENV.fetch('SHOPIFY_CLIENT_API_KEY', nil),
                                  secret: ENV.fetch('SHOPIFY_CLIENT_API_SECRET', nil) })
      shop.with_shopify_session do
        @shop_settings = shop.shop_setting
        Sevdesk::CheckAccount.api_token = @shop_settings.service_api_token
        @check_accounts = Sevdesk::CheckAccount.all.to_a.reject do |account|
          account.translationCode == 'CASH_REGISTER'
        end

        if @shop_settings.invoice_account.nil? && @check_accounts.to_a.select do |account|
             account.translationCode == 'STANDARD_CHECKACCOUNT'
           end.any?
          @shop_settings.invoice_account = @check_accounts.to_a.find do |account|
            account.translationCode == 'STANDARD_CHECKACCOUNT'
          end.id
        end

        @shop_settings.save
        puts "Settings Reinit complete for #{shop.shopify_domain}."
        ShopifyAPI::Base.clear_session
      end
    rescue StandardError => e
      puts "Shop lookup failed for #{shop.name}: " + e.to_s
    end
  end

  desc 'Get sync_infos straight'
  task :sync_sync_infos, [:shop] => [:environment] do |_t, args|
    shop = Shop.find_by(name: args[:shop])
    unless shop.nil?
      begin
        ShopifyAPI::Session.setup({ api_key: ENV.fetch('SHOPIFY_CLIENT_API_KEY', nil),
                                    secret: ENV.fetch('SHOPIFY_CLIENT_API_SECRET', nil) })
        shop.with_shopify_session do
          p 'Shopify Session start'
          service = shop.service_instance.module.constantize
          sevdesk_token = shop.shop_setting.service_api_token
          count = 0

          SyncInfo.where(shop_id: shop.id).find_each do |info|
            # Check if Invoice exists
            service::Invoice.set_api_token(sevdesk_token)
            begin
              invoice = service::Invoice.find(info.target_id)
              if invoice.present?
                p "Info for order #{info.shopify_id} KEPT."
              else
                # info.destroy

                p "Info for order #{info.shopify_id} deleted."
                count += 1
              end
            rescue StandardError
              # info.destroy
              p "Info for order #{info.shopify_id} deleted."
              count += 1
            end
          end
          p "#{count} Sync infos destroyed"

          ShopifyAPI::Base.clear_session
        end
      rescue StandardError => e
        puts "Shop lookup failed for #{shop.name}: " + e.to_s
      end
    end
  end

  desc 'Kick 403/401 Jobs from queue'
  task clean_retry_queue: :environment do
    query = Sidekiq::RetrySet.new
    query.select do |job|
      job.item['error_class'] == 'RestClient::Forbidden'
    end.map(&:delete)
  end

  desc 'Kick 403/401 Jobs from queue'
  task clean_retry_queue406: :environment do
    query = Sidekiq::RetrySet.new
    query.select do |job|
      job.item['error_class'] == 'RestClient::NotAcceptable'
    end.map(&:delete)
  end
  # heroku run rake eshopguide:clean_retry_queue_400 --app lexoffice-integration
  desc 'Kick 400 Jobs from queue'
  task clean_retry_queue400: :environment do
    query = Sidekiq::RetrySet.new
    query.select do |job|
      job.item['error_class'] == 'RestClient::BadRequest'
    end.map(&:delete)
  end

  desc 'Kick errors from Queue'
  task clean_retries: :environment do
    query = Sidekiq::RetrySet.new
    query.select do |job|
      job.item['error_class'] == 'RestClient::Forbidden' ||
        job.item['error_class'] == 'RestClient::NotAcceptable' ||
        job.item['error_class'] == 'RestClient::BadRequest' ||
        job.item['error_class'] == 'ActiveRecord::RecordNotFound' ||
        job.item['error_class'] == 'ActiveResource::UnauthorizedAccess' ||
        job.item['error_class'] == 'ActiveResource::BadRequest'
    end.map(&:delete)
  end

  # heroku run rake eshopguide:check_orders["Shopname"] --app lexoffice-integration > filename.txt
  desc 'export all Shopify Orders'
  task :check_orders, [:shop] => [:environment] do |_t, args|
    # DB logging aus
    ActiveRecord::Base.logger
    ActiveRecord::Base.logger = nil

    shop = Shop.find_by(name: args[:shop])
    unless shop.nil?
      begin
        ShopifyAPI::Session.setup({ api_key: ENV.fetch('SHOPIFY_CLIENT_API_KEY', nil),
                                    secret: ENV.fetch('SHOPIFY_CLIENT_API_SECRET', nil) })
        shop.with_shopify_session do
          ShopifyAPI::Order.all_in_store_in_batches({ order: 'created_at asc' }) do |order|
            info = SyncInfo.find_by(shopify_id: order.id)
            if info.blank? || info.target_id.nil?
              puts "#{order.name}, #{order.id}, #{Date.parse(order.processed_at).strftime('%F')}, #{order.gateway}, #{order.financial_status}, #{info.present? ? 'übertragen' : 'NICHT übertragen'}"
            end
          end
        end
      rescue StandardError => e
        puts "Shop lookup failed for #{shop.name}: " + e.to_s
      end
    end
  end

  desc 'export order for Shop'
  task :get_order, %i[shop order_id] => [:environment] do |_t, args|
    # DB logging aus
    ActiveRecord::Base.logger
    ActiveRecord::Base.logger = nil

    shop = Shop.find_by(name: args[:shop])
    order_id = args[:order_id]
    unless shop.nil?
      begin
        ShopifyAPI::Session.setup({ api_key: ENV.fetch('SHOPIFY_CLIENT_API_KEY', nil),
                                    secret: ENV.fetch('SHOPIFY_CLIENT_API_SECRET', nil) })
        shop.with_shopify_session do
          pp ShopifyAPI::Order.find(id: order_id).to_json
        end
      rescue StandardError => e
        puts "Shop lookup failed for #{shop.name}: " + e.to_s
      end
    end
  end

  desc 'retry failed orders for Shop'
  task :retry_errorlog_for_shop, [:myshopify_domain] => [:environment] do |_t, args|
    # DB logging aus
    ActiveRecord::Base.logger
    ActiveRecord::Base.logger = nil

    shop = Shop.find_by(shopify_domain: args[:myshopify_domain])
    errorlogs = ErrorLog.where(shop:)
    unless shop.nil?
      errorlogs.each do |errorlog|
        errorlog.retry_job
        sleep 3
      rescue StandardError => e
        puts "Error while retrying Order #{errorlog.shopify_id}: " + e.to_s
      end
    end
  end

  desc 'retry all failed orders from specific ID and UP'
  task :retry_errorlog_from_id, [:min_id] => [:environment] do |_t, args|
    # DB logging aus
    ActiveRecord::Base.logger
    ActiveRecord::Base.logger = nil
    errorlogs = ErrorLog.where("id >= #{args[:min_id]}")
    count = errorlogs.count
    index = 1
    errorlogs.each do |errorlog|
      errorlog.retry_job
      puts "Queued Job #{index} of #{count}"
    rescue StandardError => e
      puts "Error while retrying Order #{errorlog.shopify_id}: " + e.to_s
    ensure
      index += 1
    end
  end

  desc 'process ORDER for Shop'
  task :process_order, %i[shop order_id] => [:environment] do |_t, args|
    # DB logging aus
    ActiveRecord::Base.logger
    ActiveRecord::Base.logger = nil

    shop = Shop.find_by(name: args[:shop])
    order_id = args[:order_id]
    unless shop.nil?
      begin
        ShopifyAPI::Session.setup({ api_key: ENV.fetch('SHOPIFY_CLIENT_API_KEY', nil),
                                    secret: ENV.fetch('SHOPIFY_CLIENT_API_SECRET', nil) })
        shop.with_shopify_session do
          SyncOrderJob.perform_async('orders/create', order_id, shop.id)
        end
      rescue StandardError => e
        puts "Shop lookup failed for #{shop.name}: " + e.to_s
      end
    end
  end

  desc 'process ORDER for Shop'
  task :process_orders, %i[shop order_ids] => [:environment] do |_t, args|
    # DB logging aus
    ActiveRecord::Base.logger
    ActiveRecord::Base.logger = nil

    shop = Shop.find_by(name: args[:shop])
    order_ids = args[:order_ids].split
    unless shop.nil?
      begin
        ShopifyAPI::Session.setup({ api_key: ENV.fetch('SHOPIFY_CLIENT_API_KEY', nil),
                                    secret: ENV.fetch('SHOPIFY_CLIENT_API_SECRET', nil) })
        shop.with_shopify_session do
          order_ids.each do |order_id|
            SyncOrderJob.perform_async('orders/create', order_id, shop.id)
          end
        end
      rescue StandardError => e
        puts "Shop lookup failed for #{shop.name}: " + e.to_s
      end
    end
  end

  desc 'process REFUND for Shop'
  task :process_refund, %i[shop refund_id order_id] => [:environment] do |_t, args|
    # DB logging aus
    ActiveRecord::Base.logger
    ActiveRecord::Base.logger = nil

    shop = Shop.find_by(name: args[:shop])
    refund_id = args[:refund_id]
    order_id = args[:order_id]
    unless shop.nil?
      begin
        ShopifyAPI::Session.setup({ api_key: ENV.fetch('SHOPIFY_CLIENT_API_KEY', nil),
                                    secret: ENV.fetch('SHOPIFY_CLIENT_API_SECRET', nil) })
        shop.with_shopify_session do
          RefundJob.perform_async('refunds/create', refund_id, order_id, shop.id)
        end
      rescue StandardError => e
        puts "Shop lookup failed for #{shop.name}: " + e.to_s
      end
    end
  end

  desc 'Reload lexoffice settings for all shops'
  task update_lexoffice_infos: :environment do
    Shop.all.each do |shop|
      next if shop.lexoffice_token.nil?

      begin
        shop.refresh_token_if_expired
      rescue StandardError
        "#{shop.name} UNAUTHORIZED"
      end

      profile_endpoint = Lexoffice::Profile.new(shop.lexoffice_token)
      begin
        profile_info = profile_endpoint.get_info
        # save organiszationID to Shop
        shop.lexoffice_organization_id = profile_info['organizationId']
        shop.lexoffice_tax_type = profile_info['taxType']
        shop.lexoffice_small_business = profile_info['smallBusiness']
        shop.distance_sales_principle = (profile_info['distanceSalesPrinciple'].presence || 'not defined')
        shop.save
      rescue StandardError => e
        puts "#{shop.shopify_domain}: #{e}"
      end
    end
  end

  desc 'Transfers all invoices for all connected Shops between two dates. Date format: 2021-03-11'
  task :import_refunds_and_invoices_for_shop,
       %i[start_date end_date shop import_refunds import_invoices] => [:environment] do |_t, args|
    ActiveRecord::Base.logger = nil
    start_date = args[:start_date].to_datetime.to_s
    end_date = args[:end_date].to_datetime.to_s
    shop = Shop.find_by(name: args[:shop])
    # correct dates
    if shop.created_at > end_date
      puts "skipped #{shop.name}, Installed after end date"
      next
    end
    shop.created_at if shop.created_at > start_date

    begin
      shop.with_shopify_session do
        # gets rescued/skipped if it has no valid Shopify token
        ShopifyAPI::Shop.current
        ShopifyAPI::Order.all_in_store_in_batches({ created_at_min: start_date, created_at_max: end_date,
                                                    order: 'created_at asc' }) do |order|
          refunds = order.refunds
          if refunds.present? && args[:import_refunds] == 'true'
            refunds.each do |refund|
              sleep 2
              if refund.transactions.count { |x| x.status = 'success' }.positive?
                RefundJob.perform_async('refunds/create', refund.id, order.id, shop.id)
                p "Queued REFUND JOB for Refund #{refund.id}, Order #{order.id}"
              end
            end
          end
          if args[:import_invoices] == 'true'
            sleep 2
            SyncOrderJob.perform_async('orders/create', order.id, shop.id)
            p "Queued ORDER JOB for Order #{order.id}"
          end
        end
      end
    rescue StandardError => e
      puts e
    end
  end

  desc 'Adds order Infos to refund entries'
  task fix_refund_sync_infos: :environment do
    ShopifyAPI::Session.setup({ api_key: ENV.fetch('SHOPIFY_CLIENT_API_KEY', nil),
                                secret: ENV.fetch('SHOPIFY_CLIENT_API_SECRET', nil) })
    # get relevant shops
    relevant_shops = SyncInfo.where("target_type = 'Refund' and target_id is null and shopify_order_id is null").distinct.pluck(:shop_id)
    relevant_refund_ids = SyncInfo.where("target_type = 'Refund' and target_id is null and shopify_order_id is null").pluck(:shopify_id)

    processed_orders = 0
    relevant_shops.each do |shop_id|
      shop = Shop.find(shop_id)
      p "processing #{shop.shopify_domain}"
      shop.with_shopify_session do
        ShopifyAPIRetry::REST.retry do
          ShopifyAPI::Order.all_in_store_in_batches({ created_at_min: shop.created_at,
                                                      created_at_max: Time.zone.today, order: 'created_at asc', limit: 250 }) do |order|
            processed_orders += 1
            order.refunds.each do |refund|
              next unless relevant_refund_ids.include? refund.id

              sync_info = SyncInfo.find_by(shopify_id: refund.id, target_id: nil)
              p "updating SyncInfo #{sync_info.id}"
              sync_info.update(shopify_order_id: order.id, shopify_created_at: refund.processed_at,
                               shopify_order_name: order.name)
            end
            p "processed orders: #{processed_orders}" if (processed_orders % 100).zero?
          end
        end
      end
    rescue StandardError => e
      puts e
    end
  end

  desc 'Update shopify_names in error_logs table'
  task update_shopify_names: :environment do
    # Iterate through error_logs with order_id not nil
    Rails.logger.info 'Starting up and collecting all data...'
    results = ErrorLog.where(
      "(
          shopify_name LIKE ? OR
          shopify_name LIKE ? OR
          shopify_name LIKE ? OR
          shopify_name LIKE ? OR
          shopify_name IS NULL
        ) AND order_id IS NOT NULL",
      '%TenderTransaction%',
      '%TahCreationJob%',
      '%TenderTransactionJob%',
      '%FinancialAccountJob%'
    )

    results.each do |error_log|
      order_id = error_log.order_id.to_i
      shop = error_log.shop
      shop.with_shopify_session do
        ShopifyAPIRetry::REST.retry do
          # Fetch the order from Shopify
          order = ShopifyAPI::Order.find(id: order_id)

          # Compare shopify_name with order name
          error_log.update(shopify_name: order.name) if error_log.shopify_name != order.name
        end
      rescue ActiveResource::ResourceNotFound
        Rails.logger.info 'Order Not Found'
        Rails.logger.info "Skipped Entry for SHOP ID #{shop.id}"
        next
      rescue StandardError => e
        Rails.logger.info "An error occurred: #{e.message}"
        Rails.logger.info "Error on Entry for SHOP ID #{shop.id}"
        next
      end
    end
  end

  desc 'Update error_type_id for ErrorLogs for new error message'
  task update_error_type_id: :environment do
    # Find and update ErrorLogs with {"message": null} in error_info_internal
    ErrorLog.where('error_info_internal ~ ?', '{"message":null}').update_all(error_type_id: 33) # rubocop:disable Rails/SkipsModelValidations
  rescue StandardError => e
    Rails.logger.error "An error occurred: #{e.message}"
  end

  desc 'create sync infos for manuel invoices and retry errors'
  task :create_sync_infos_and_retry_orders, %i[shop order_ids] => [:environment] do |_t, args|
    shop = Shop.find_by(shopify_domain: args[:shop])
    order_ids = args[:order_ids].split
    unless shop.nil?
      begin
        ShopifyAPI::Session.setup({ api_key: ENV.fetch('SHOPIFY_CLIENT_API_KEY', nil),
                                    secret: ENV.fetch('SHOPIFY_CLIENT_API_SECRET', nil) })
        shop.with_shopify_session do
          order_ids.each do |order_id|
            SyncInfo.find_or_create_by(shopify_id: order_id, shop_id: shop.id) do |sync_info|
              sync_info.assign_attributes(target_type: 'Order', target_id: 'manual',
                                          extra_infos: { 'manual_invoice' => true, 'reason' => 'shopify_bug' })
              sync_info.save
            end
            ErrorLog.find_by(shopify_id: order_id, shop_id: shop.id)&.delete
          end
          ErrorLog.for_shop(shop).each do |error|
            error.retry_job
            sleep(1)
          end
        end
      rescue StandardError => e
        puts "Shop lookup failed for #{shop.name}: " + e.to_s
      end
    end
  end

  desc 'Create Transactions with array of Order IDs'
  task :create_transactions, %i[shop order_ids offest] => [:environment] do |_t, args|
    shop = Shop.find_by(shopify_domain: args[:shop])
    order_ids = args[:order_ids].split
    offset = args[:offset].to_i
    shop.with_shopify_session do
      order_ids.each do |order_id|
        order = ShopifyAPI::Order.find(id: order_id)
        next if order.nil?

        sleep(1)
        start_date = Time.zone.parse(order.created_at) - offset.day
        end_date = Time.zone.parse(order.created_at) + offset.day
        transactions = ShopifyAPI::TenderTransaction.all(processed_at_min: start_date, processed_at_max: end_date,
                                                         limit: 250)
        transaction = transactions.find { |t| t.order_id == order_id }
        next if transaction.nil?

        TenderTransactionJob.perform_async(transaction.to_json, shop.id)
      rescue StandardError
        next
      end
    end
  end

  namespace :shop do
    desc 'Set confirm_tax_settings to true for shops without specific errors'
    task confirm_tax_settings: :environment do
      error_type_ids = [11, 2, 5, 4, 3, 34]

      Shop.find_each do |shop|
        error_count = ErrorLog.where(shop_id: shop.id, error_type_id: error_type_ids).count

        if error_count.zero?
          shop_settings = shop.shop_setting
          next if shop_settings.nil?

          shop_settings.update(confirm_tax_settings: true)
          Rails.logger.info "Updated ShopSettings for Shop ID: #{shop.id} to confirm_tax_settings: true"
        else
          Rails.logger.info "Shop ID: #{shop.id} has errors and was not updated."
        end
      rescue StandardError
        next
      end
    end
  end

  desc 'Check tender transactions and update the sync info of the missing ones in lexoffice'
  task :check_tender_transactions, %i[shop_id start_date end_date] => [:environment] do |_t, args|
    shop = Shop.find(args[:shop_id])
    sync_infos = SyncInfo.where("shop_id = ? AND target_type = 'Transaction' AND created_at >= ? AND created_at <= ?", shop.id, args[:start_date], args[:end_date])
    unless sync_infos.empty?
      shop.refresh_token_if_expired
      headers = { Authorization: "Bearer #{shop.lexoffice_token}", Accept: 'application/json', 'Content-Type': 'application/json' }
      shop.with_shopify_session do
        sync_infos.each do |sync_info|
          Retriable.retriable do
            JSON.parse(RestClient.get("#{ENV.fetch('LEXOFFICE_API')}/v1/finance/transactions/#{sync_info.target_id}",
                                      headers))
          end
        rescue RestClient::NotFound
          sync_info.update!(target_id: nil)
        rescue StandardError => e
          puts "Error while checking TenderTransaction #{sync_info.target_id}: #{e}"
          next
        end
      end
    end
  end

  # This Rake task iterates over all shops and deletes any webhooks that are not needed.
  # It does this by:
  # 1. Establishing a Shopify session for each shop.
  # 2. Retrieving the list of webhook topics required by the shop's synchronization rules.
  # 3. Fetching all currently registered webhooks from Shopify.
  # 4. Filtering out the webhooks that are not in the list of required topics.
  # 5. Deleting the unneeded webhooks from Shopify.
  # This helps in cleaning up unnecessary webhook subs, ensuring that only the required webhooks are maintained.
  desc 'delete unneeded webhooks subscriptions'
  task delete_unneeded_webhooks: :environment do
    webhook_topics_without_rules = %w[shop/update app/uninstalled customers/data_request customers/redact shop/redact]
    Shop.all.each do |shop|
      shop.with_shopify_session do
        topics = shop.sync_rules.map(&:webhooks).flatten
        webhooks = ShopifyAPI::Webhook.all
        webhooks = webhooks.to_a.reject do |hook|
          topics.include?(hook.topic) || webhook_topics_without_rules.include?(hook.topic)
        end
        puts "Deleting unneeded webhooks with topics #{webhooks.map(&:topic)} for #{shop.name}"
        webhooks.each(&:destroy)
        puts "Deleted #{webhooks.count} unneeded webhooks for #{shop.name}"
      end
    rescue StandardError => e
      puts "Shop lookup failed for #{shop.name}: #{e}"
      next # Skip to the next shop if an error occurs
    end
  end

  desc 'Register TenderTransaction webhook for all shops'
  task register_tender_transactions_webhook: :environment do
    index = 0
    shops_count = Shop.count
    Shop.all.each do |shop|
      index += 1
      HandleRulesService.call(shop.shop_setting, shop)
      puts "Shop #{shop.name} has been processed. #{index} of #{shops_count}"
    rescue StandardError => e
      puts "An Error may have occurred when trying to register webhooks for #{shop.name}: #{e}"
      next # Skip to the next shop if an error occurs
    end
  end
end
# rubocop:enable Layout/LineLength
