# frozen_string_literal: true

BATCH_SIZE = 1000

namespace :reporting do
  desc 'Log historic plan activations and one-time purchases for all shops with their original dates'
  task :log_historic_activations, %i[start_date subscriptions_only one_time_only] => :environment do |_t, args|
    ActiveJob::Base.queue_adapter = :sidekiq

    start_date = args[:start_date] ? Date.strptime(args[:start_date], '%d-%m-%Y').beginning_of_day : nil
    puts "Processing shops installed after: #{start_date}" if start_date

    process_subscriptions = args[:subscriptions_only].nil? || args[:subscriptions_only].to_s.casecmp('true').zero?
    process_one_time = args[:one_time_only].nil? || args[:one_time_only].to_s.casecmp('true').zero?

    shops = start_date ? Shop.where('created_at >= ?', start_date) : Shop.all
    shops.find_each do |shop|
      shop.with_shopify_session do
        # Log subscriptions
        if process_subscriptions && shop.billing_plan&.id && !shop.billing_plan&.id&.zero?
          subscription = GetActiveSubscription.call

          if subscription.status == 'ACTIVE'
            CentralEventLogger.log_event(
              event_name: 'plan_activation',
              event_type: CentralEventLogger::EventTypes::CONVERSION,
              customer_myshopify_domain: shop.shopify_domain,
              event_value: subscription.name,
              payload: {
                change_type: 'historic',
                price_at_activation: subscription.amount
              },
              timestamp: subscription.created_at,
              external_id: subscription.id
            )

            puts "Logged historic activation for shop: #{shop.shopify_domain}"
          end
        end

        # Log one-time purchases
        if process_one_time
          one_time_purchases = GetOneTimePurchases.call
          one_time_purchases&.each do |purchase|
            CentralEventLogger.log_event(
              event_name: 'one_time_purchase',
              event_type: CentralEventLogger::EventTypes::CONVERSION,
              customer_myshopify_domain: shop.shopify_domain,
              event_value: purchase.name,
              payload: {
                change_type: 'historic',
                price: purchase.amount
              },
              timestamp: purchase.created_at,
              external_id: purchase.id
            )

            puts "Logged one-time purchase for shop: #{shop.shopify_domain}"
          end
        end
      end
    rescue StandardError => e
      puts "Error processing shop #{shop.shopify_domain}: #{e.message}"
    end

    puts 'Finished logging historic plan activations and one-time purchases'
  end

  desc 'Log historic shop installations for all shops with their created_at dates'
  task log_historic_installations: :environment do
    ActiveJob::Base.queue_adapter = :sidekiq

    Shop.find_each do |shop|
      remote_shop = shop.with_shopify_session do
        ShopifyAPI::Shop.current
      rescue ShopifyAPI::Errors::HttpResponseError => e
        puts "Error fetching shop info for shop #{shop.shopify_domain}: #{e.message}"
      end

      CentralEventLogger.log_event(
        event_name: 'app_installed',
        event_type: CentralEventLogger::EventTypes::USER_ACQUISITION,
        customer_myshopify_domain: shop.shopify_domain,
        customer_info: {
          name: shop.name,
          owner: shop.shop_owner,
          email: shop.email
        },
        event_value: remote_shop&.plan_name,
        payload: {
          shop_name: shop.name,
          shop_owner: shop.shop_owner,
          country: shop.country,
          shopify_plan: remote_shop&.plan_name,
          shopify_plan_display_name: remote_shop&.plan_display_name
        },
        timestamp: shop.created_at,
        external_id: "install:#{shop.shopify_domain}"
      )

      puts "Logged historic installation for shop: #{shop.shopify_domain}"
    rescue StandardError => e
      puts "Error processing shop #{shop.shopify_domain}: #{e.message}"
    end

    puts 'Finished logging historic shop installations'
  end

  desc 'Log invoice and credit note creations from 2025'
  task :log_document_creations, %i[start_date end_date] => :environment do |_t, args|
    require 'parallel'

    ActiveJob::Base.queue_adapter = :sidekiq

    start_date = if args[:start_date]
                   Date.strptime(args[:start_date], '%d-%m-%Y').beginning_of_day
                 else
                   Date.new(2025, 1, 1)
                 end

    end_date = if args[:end_date]
                 Date.strptime(args[:end_date], '%d-%m-%Y').end_of_day
               else
                 start_date + 1.day
               end

    puts "Processing documents created between: #{start_date} and #{end_date}"

    def process_logging(sync_info, shopify_plan, document_type, shop)
      CentralEventLogger.log_event(
        event_name: document_type == 'Invoice' ? 'invoice_created' : 'credit_note_created',
        event_type: 'customer_usage',
        customer_myshopify_domain: shop.shopify_domain,
        event_value: sync_info.extra_infos[:total_price],
        payload: {
          shopify_plan: { display_name: shopify_plan['displayName'] },
          app_plan: shop.billing_plan.name,
          document_number: sync_info.extra_infos[:voucher_title],
          total_price: sync_info.extra_infos[:total_price]
        },
        timestamp: sync_info.created_at,
        external_id: sync_info.shopify_id
      )
    end

    def process_batch(batch, document_type)
      Parallel.each(batch, in_threads: 4) do |sync_info|
        ActiveRecord::Base.connection_pool.with_connection do
          shop = Shop.select(:id, :shopify_domain, :shopify_token).find(sync_info.shop_id)

          # Cache shopify_plan for 4 hours
          shopify_plan = Rails.cache.fetch("shopify_plan:#{shop.id}", expires_in: 4.hours) do
            shop.shopify_plan
          end

          process_logging(sync_info, shopify_plan, document_type, shop)
          puts "Logged #{document_type} creation for shop: #{shop.shopify_domain},
document: #{sync_info.extra_infos[:voucher_title]}"
        rescue StandardError => e
          Honeybadger.notify(e, context: {
                               shop_id: sync_info.shop_id,
                               sync_info_id: sync_info.id,
                               document_type:,
                               error_message: e.message
                             })
          puts "Error processing #{document_type.downcase} sync_info #{sync_info.id} : #{e.message}"
        end
      end
    end

    # Process invoices in batches
    puts 'Processing invoices...'
    total_invoices = SyncInfo.where(target_type: 'Invoice')
                             .where('created_at >= ? AND created_at <= ?', start_date, end_date)
                             .count
    processed_invoices = 0

    SyncInfo.where(target_type: 'Invoice')
            .where('created_at >= ? AND created_at <= ?', start_date, end_date)
            .select(:id, :shop_id, :created_at, :shopify_id, :extra_infos)
            .find_in_batches(batch_size: BATCH_SIZE) do |batch|
      process_batch(batch, 'Invoice')
      processed_invoices += batch.size
      puts "Processed #{processed_invoices}/#{total_invoices} invoices
 (#{(processed_invoices.to_f / total_invoices * 100).round(2)}%)"
    end

    # Process credit notes in batches
    puts 'Processing credit notes...'
    total_credit_notes = SyncInfo.where(target_type: 'Refund')
                                 .where('created_at >= ? AND created_at <= ?', start_date, end_date)
                                 .count
    processed_credit_notes = 0

    SyncInfo.where(target_type: 'Refund')
            .where('created_at >= ? AND created_at <= ?', start_date, end_date)
            .select(:id, :shop_id, :created_at, :shopify_id, :extra_infos)
            .find_in_batches(batch_size: BATCH_SIZE) do |batch|
      process_batch(batch, 'Refund')
      processed_credit_notes += batch.size
      puts "Processed #{processed_credit_notes}/#{total_credit_notes} credit notes
 (#{(processed_credit_notes.to_f / total_credit_notes * 100).round(2)}%)"
    end

    puts "Finished logging document creations. Processed #{processed_invoices} invoices
 and #{processed_credit_notes} credit notes."
  end
end
