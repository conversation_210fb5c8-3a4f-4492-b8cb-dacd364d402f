# frozen_string_literal: true

require 'countries'
# oss check class
class Oss
  def initialize(order, shop, contact, shop_settings)
    @order = order
    @shop = shop
    @contact = contact
    @shop_settings = shop_settings
  end

  def is_oss_relevant?
    !@shop.lexoffice_small_business &&
      made_after_oss? &&
      lexoffice_dsp_set? &&
      contact_oss_relevant? &&
      destination_country_oss_relevant? &&
      !customer_tax_exempt?
  end

  def get_tax_sub_type
    @order.at_least_one_delivery? ? 'distanceSales' : 'electronicServices'
  end

  # shop's distance sales principle is origin or destination, if not then not defined
  def lexoffice_dsp_set?
    return false if @shop.distance_sales_principle == 'not defined'

    true
  end

  # checks if the shipping/billing address is in different country than the store
  # shipping address has precedence
  def destination_country_oss_relevant?
    address_to_check = @order.shipping_address || @order.billing_address
    return false if address_to_check.blank?

    address_to_check = address_to_check.symbolize_keys
    return false if address_to_check[:country_code] == 'CH' || address_to_check[:country_code] == 'RE'

    is_north_ireland = address_to_check[:province_code] == 'NIR'

    c = ISO3166::Country.new(address_to_check[:country_code])
    is_in_eu = c.present? && c.in_eu?
    !@order.customer.nil? && address_to_check[:country_code] != @shop.country &&
      (is_in_eu || is_north_ireland) && @shop.country.present?
  end

  # if tax exempt than not oss relevant
  def customer_tax_exempt?
    !@order.customer.nil? && @order.customer.tax_exempt
  end

  # contact types and address check
  def contact_oss_relevant?
    return false if @contact['roles']['customer'].nil?

    if @contact['company'].present?
      company = @contact['company']
      if company['vatRegistrationId'].present? || (company['allowTaxFreeInvoices'].present? &&
                                                  company['allowTaxFreeInvoices'])
        return false
      end
    end
    true
  end

  # order should be processed after 01.07.2021
  def made_after_oss?
    Date.parse(@order.processed_at) >= Date.parse('2021-07-01')
  end
end
