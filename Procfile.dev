web: bundle exec rails s -p 3000
jobworker: bundle exec sidekiq -q default -q import -q doctasks -q active_storage_analysis -q active_storage_purge -c 8
ngrok: ngrok start lexoffice
vite: bundle exec vite dev
liveworker: bundle exec sidekiq -q live -c 8
jobworker: bundle exec sidekiq -q default -q doctasks -q import -q error_retry -q misc -q active_storage_analysis -q active_storage_purge -q infosync -c 8