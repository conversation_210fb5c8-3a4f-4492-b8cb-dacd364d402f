# frozen_string_literal: true

class RemoveOldOrderIdColumnsAndTriggers < ActiveRecord::Migration[7.1]
  def up
    # --- SYNC_INFOS ---
    # Drop indexes on old columns
    remove_index :sync_infos, :shopify_order_id if index_exists?(:sync_infos, :shopify_order_id)
    if index_exists?(:sync_infos, %i[shopify_order_id target_type])
      remove_index :sync_infos, %i[shopify_order_id target_type]
    end

    # Remove triggers and functions
    execute <<-SQL.squish
      DROP TRIGGER IF EXISTS sync_infos_copy_shopify_order_id ON sync_infos;
      DROP TRIGGER IF EXISTS sync_infos_copy_shopify_order_id_on_create ON sync_infos;
      DROP FUNCTION IF EXISTS copy_shopify_order_id();
    SQL

    # Remove old column
    remove_column :sync_infos, :shopify_order_id, :string

    # Rename new column
    rename_column :sync_infos, :new_shopify_order_id, :shopify_order_id

    # --- ERROR_LOGS ---
    remove_index :error_logs, :order_id if index_exists?(:error_logs, :order_id)

    # Remove triggers and functions
    execute <<-SQL.squish
      DROP TRIGGER IF EXISTS error_logs_copy_order_id ON error_logs;
      DROP TRIGGER IF EXISTS error_logs_copy_order_id_on_create ON error_logs;
      DROP FUNCTION IF EXISTS copy_error_log_order_id();
    SQL

    # Remove old column
    remove_column :error_logs, :order_id, :string

    # Rename new column
    rename_column :error_logs, :new_order_id, :order_id
  end

  def down
    # This migration is irreversible due to column drops and data type changes
    raise ActiveRecord::IrreversibleMigration
  end
end
