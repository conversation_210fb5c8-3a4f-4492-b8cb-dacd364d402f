class RenameBillingPlan < ActiveRecord::Migration[7.0]
  def up
    # Update the name of the existing plan (Staging + Production have different names)
    ShopifyBilling::BillingPlan.where(name: 'LexOffice Autosync').update_all(name: 'Basic')
    ShopifyBilling::BillingPlan.where(name: 'lexoffice Autosync').update_all(name: 'Basic')
  end

  def down
    ShopifyBilling::BillingPlan.where(name: 'Basic').update_all(name: 'lexoffice Autosync')
  end
end
