class CreateTransactionSettings < ActiveRecord::Migration[5.2]
  def change
    create_table :transaction_settings do |t|
      t.boolean :enable_amazon, default: false
      t.string :amazon_account_id
      t.boolean :enable_apple_pay, default: false
      t.string :apple_pay_account_id
      t.boolean :enable_credit_card, default: false
      t.string :credit_card_account_id
      t.boolean :enable_google_pay, default: false
      t.string :google_pay_account_id
      t.boolean :enable_klarna, default: false
      t.string :klarna_account_id
      t.boolean :enable_samsung_pay, default: false
      t.string :samsung_pay_account_id
      t.boolean :enable_shopify_pay, default: false
      t.string :shopify_pay_account_id

      t.timestamps
    end

    add_reference :transaction_settings, :shop, foreign_key: true
  end
end
