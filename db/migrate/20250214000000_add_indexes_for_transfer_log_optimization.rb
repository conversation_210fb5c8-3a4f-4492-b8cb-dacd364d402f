# frozen_string_literal: true

class AddIndexesForTransferLogOptimization < ActiveRecord::Migration[7.0]
  def change
    # Add composite indexes for common query patterns
    add_index :error_logs, [:shop_id, :order_id], name: 'idx_error_logs_shop_order'
    add_index :sync_infos, [:shop_id, :shopify_order_id], name: 'idx_sync_infos_shop_order'

    # Add index for search
    add_index :sync_infos, :shopify_order_name, name: 'idx_sync_infos_order_name'

    # Add index for filtering
    add_index :sync_infos, :target_type, name: 'idx_sync_infos_target_type'
    add_index :sync_infos, :last_action, name: 'idx_sync_infos_last_action'
  end
end 