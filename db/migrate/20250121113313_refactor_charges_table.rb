class RefactorChargesTable < ActiveRecord::Migration[7.0]
  def change
    drop_table :charges

    create_table :charges do |t|
      t.string :shopify_id
      t.string :shopify_domain
      t.datetime :current_period_end
      t.references :billing_plan, foreign_key: true
      t.string :status
      t.boolean :test
      t.timestamps
    end

    add_index :charges, :shopify_id, unique: false
    add_index :charges, :shopify_domain, unique: false
  end
end
