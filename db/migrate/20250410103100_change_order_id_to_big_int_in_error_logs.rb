class ChangeOrderIdToBigIntInErrorLogs < ActiveRecord::Migration[7.0]
  def up

    # Add a new column with the desired type
    add_column :error_logs, :new_order_id, :bigint

    # Create a trigger to copy values on update
    execute <<-SQL.squish
      CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION copy_error_log_order_id()
      RETURNS TRIGGER AS $$
      BEGIN
        IF NEW.new_order_id IS NULL THEN
          NEW.new_order_id = NEW.order_id::bigint;
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      CREATE TRIGGER error_logs_copy_order_id
      BEFORE UPDATE ON error_logs
      FOR EACH ROW
      WHEN (NEW.new_order_id IS NULL)
      EXECUTE FUNCTION copy_error_log_order_id();
    SQL
  end

  def down
    # Drop the trigger and function
    execute <<-SQL.squish
      DROP TRIGGER IF EXISTS error_logs_copy_order_id ON error_logs;
      DROP FUNCTION IF EXISTS copy_error_log_order_id();
    SQL

    remove_column :error_logs, :new_order_id
  end
end
