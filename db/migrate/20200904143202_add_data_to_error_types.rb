class AddDataToErrorTypes < ActiveRecord::Migration[5.2]
  def up
    ErrorType.create(signal: "Unauthorized", external_info_text_key: "exceptions.unauthorized",helpscout_article_id:"42")
    ErrorType.create(signal: "Steuersatz", external_info_text_key: "exceptions.wrong_tax_rate",helpscout_article_id:"42")
    ErrorType.create(signal: "No vatfree invoices allowed for this organization", external_info_text_key: "exceptions.small_business_with_taxes",helpscout_article_id:"42")
  end

  def down
    ErrorType.delete_all
  end
end
