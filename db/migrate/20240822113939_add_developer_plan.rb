class AddDeveloperPlan < ActiveRecord::Migration[7.0]
  def up
    add_column :billing_plans, :development_plan, :boolean, default: false
    add_column :billing_plans, :available_for_development_shop, :boolean, default: false
    add_column :billing_plans, :available_for_production_shop, :boolean, default: true

    ShopifyBilling::BillingPlan.create(
      name: 'Developer Basic',
      short_name: 'developer',
      price: 0,
      plan_type: 'recurring',
      development_plan: true,
      available_for_development_shop: true,
      available_for_production_shop: false
    )

    ShopifyBilling::BillingPlan.find_by(short_name: 'import')&.update(available_for_development_shop: true)
  end

  def down
    remove_column :billing_plans, :development_plan
    remove_column :billing_plans, :available_for_development_shop
    remove_column :billing_plans, :available_for_production_shop

    ShopifyBilling::BillingPlan.find_by(short_name: 'developer').destroy!
  end
end
