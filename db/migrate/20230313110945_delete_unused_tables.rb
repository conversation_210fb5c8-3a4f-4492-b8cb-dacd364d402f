class DeleteUnusedTables < ActiveRecord::Migration[7.0]
  # Note: this migration is not reversible
  def up
    drop_table :payment_type_account_mappings, if_exists: true
    drop_table :sync_groups, if_exists: true
    drop_table :sync_filters, if_exists: true
    drop_table :shopify_filters, if_exists: true
    drop_table :sync_filter_templates, if_exists: true
    # TODO: drop table when fixtures are removed. Else test suite will currently fail.
    #drop_table :sync_rule_templates, if_exists: true

    remove_column :sync_rules, :sync_group_id, if_exists: true
    remove_index :sync_rules, :sync_group_id, if_exists: true
    remove_column :sync_rules, :trello_list, if_exists: true
  end
end
