# frozen_string_literal: true

class CreateSupportUserEvents < ActiveRecord::Migration[7.1]
  def change
    create_table :support_user_events do |t| # rubocop:disable Rails/CreateTableWithTimestamps
      t.references :support_user, null: false, foreign_key: true
      t.string :name, null: false
      t.jsonb :properties, null: false, default: {}

      t.datetime :time, null: false
    end

    add_index :support_user_events, %i[name time]
  end
end
