class RemoveUnnecessaryAttributesFromShopSettings < ActiveRecord::Migration[5.2]
  def up
    change_table :shop_settings, bulk: true do |t|
      t.remove :service_secret
      t.remove :invoice_timing
      t.remove :invoice_contact
      t.remove :use_tax_exempt
      t.remove :tax_exempt_text
      t.remove :use_skus
      t.remove :use_brutto
    end
  end

  def down
    change_table :shop_settings, bulk: true do |t|
      t.string :service_secret
      t.string :invoice_timing
      t.string :invoice_contact
      t.boolean :use_tax_exempt
      t.string :tax_exempt_text
      t.boolean :use_skus
      t.boolean :use_brutto
    end
  end
end
