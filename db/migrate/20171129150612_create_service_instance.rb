class CreateServiceInstance < ActiveRecord::Migration[4.2]
  def change
    create_table :service_instances do |t|
      t.string :name
      t.string :css_class
      t.string :logo_1
      t.string :logo_2
      t.string :url
      t.string :portal_url
      t.string :website_url
      t.string :module
    end

  end

  def data
    ServiceInstance.create!(name:"sevDesk", css_class:"sevdesk", logo_1:"sevdesklogodark.svg", logo_2:nil, url:"https://my.sevdesk.de/api/v1/", portal_url:"https://my.sevdesk.de",website_url:"https://sevdesk.de/?partner=103", module:"Sevdesk")
    ServiceInstance.create!(name:"pebeSmart", css_class:"pebesmart", logo_1:"pebe_logo.svg", logo_2:nil, url:"https://my.pebesmart.ch/api/v1/", portal_url:"https://my.pebesmart.ch",website_url:"https://pebesmart.ch/", module:"Pebesmart")
    ServiceInstance.create!(name:"1&1", css_class:"einsundeins", logo_1:"Einsundeins_logo.jpg", logo_2:nil, url:"https://online-buchhaltung.1und1.de/api/v1/", portal_url:"https://online-buchhaltung.1und1.de",website_url:"https://hosting.1und1.de/buchhaltung-online", module:"Einsundeins")
  end
end
