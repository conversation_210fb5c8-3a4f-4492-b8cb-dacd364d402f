class AddPlusPlan < ActiveRecord::Migration[7.0]
  def up
    ShopifyBilling::BillingPlan.create(
      name: 'Plus',
      short_name: 'plus',
      price: 20,
      plan_type: 'recurring',
      development_plan: false,
      available_for_development_shop: true,
      available_for_production_shop: true,
      features: ['custom_layouts', 'exclude_emails_by_tag', 'exclude_orders_by_tag', 'customer_ui']
    )
  end

  def down
    ShopifyBilling::BillingPlan.find_by(short_name: 'plus').destroy!
  end
end
