class AddDefaultsToShopSettings < ActiveRecord::Migration[5.2]
  def change
    change_table :shop_settings, bulk: true do |t|
      t.change_default :create_invoices, from: nil, to: true
      t.change_default :create_refunds, from: nil, to: true
      t.change_default :excludePOS, from: nil, to: true
      t.change_default :invoice_mail_subject, from: nil, to: I18n.t('settings.invoice_mail_subject_text_with_liquid')
      t.change_default :invoice_mail_body, from: nil, to: I18n.t('settings.invoice_mail_body_text')
    end

    add_index :shop_settings, :shop_id
  end
end
