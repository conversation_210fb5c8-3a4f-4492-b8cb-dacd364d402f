class ChargesUniqueIndex < ActiveRecord::Migration[7.0]
  def change
    # Remove all old "manual" charges
    ShopifyBilling::Charge.where(shopify_id: nil).delete_all
    ShopifyBilling::Charge.where(shopify_id: '').delete_all

    # Remove index that allows null values in shopify_id
    remove_index :charges, :shopify_id, unique: true, where: 'shopify_id IS NOT NULL'

    # Make shopify_id not nullable
    change_column_null :charges, :shopify_id, false

    # Add new index
    add_index :charges, :shopify_id, unique: true
  end
end
