class AddShopifyOrderIdInsertTrigger < ActiveRecord::Migration[7.0]
  def up
    # Create a trigger to call the copy_shopify_order_id function on insert for sync_infos table
    execute <<-SQL.squish
      CREATE OR REPLACE FUNCTION copy_shopify_order_id()
      RETURNS TRIGGER AS $$
      BEGIN
        IF NEW.new_shopify_order_id IS NULL THEN
          NEW.new_shopify_order_id = NEW.shopify_order_id::bigint;
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      CREATE TRIGGER sync_infos_copy_shopify_order_id_on_create
      BEFORE INSERT ON sync_infos
      FOR EACH ROW
      WHEN (NEW.new_shopify_order_id IS NULL)
      EXECUTE FUNCTION copy_shopify_order_id();
    SQL

    # Create a trigger to call the copy_error_log_order_id function on insert for error_logs table
    execute <<-SQL.squish
      CREATE OR REPLACE FUNCTION copy_error_log_order_id()
      R<PERSON>URNS TRIGGER AS $$
      BEGIN
        IF NEW.new_order_id IS NULL THEN
          NEW.new_order_id = NEW.order_id::bigint;
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      CREATE TRIGGER error_logs_copy_order_id_on_create
      BEFORE INSERT ON error_logs
      FOR EACH ROW
      WHEN (NEW.new_order_id IS NULL)
      EXECUTE FUNCTION copy_error_log_order_id();
    SQL
  end

  def down
    # Drop the triggers
    execute <<-SQL.squish
      DROP TRIGGER IF EXISTS sync_infos_copy_shopify_order_id_on_create ON sync_infos;
    SQL

    execute <<-SQL.squish
      DROP TRIGGER IF EXISTS error_logs_copy_order_id_on_create ON error_logs;
    SQL
  end
end
