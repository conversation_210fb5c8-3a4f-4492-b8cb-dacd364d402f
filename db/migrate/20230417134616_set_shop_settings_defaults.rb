class SetShopSettingsDefaults < ActiveRecord::Migration[7.0]
  def change
    change_table :shop_settings, bulk: true do |t|
      t.change_default :invoice_posttext, from: nil, to: ""
      t.change_default :refund_pretext, from: nil, to: ""
      t.change_default :refund_posttext, from: nil, to: ""
      t.change_default :credit_note_mail_body, from: nil, to: ""
      t.change_default :credit_note_mail_subject, from: nil, to: ""
    end
  end
end
