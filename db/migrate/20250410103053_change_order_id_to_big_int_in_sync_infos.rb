class ChangeOrderIdToBigIntInSyncInfos < ActiveRecord::Migration[7.0]
  def up
    # Drop the dependent view
    execute <<-SQL.squish
      DROP VIEW IF EXISTS transfer_histories;
    SQL

    # Add a new column with the desired type
    add_column :sync_infos, :new_shopify_order_id, :bigint

    # Create a trigger to copy values on update
    execute <<-SQL.squish
      CREATE OR REPLACE FUNCTION copy_shopify_order_id()
      RETURNS TRIGGER AS $$
      BEGIN
        IF NEW.new_shopify_order_id IS NULL THEN
          NEW.new_shopify_order_id = NEW.shopify_order_id::bigint;
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      CREATE TRIGGER sync_infos_copy_shopify_order_id
      BEFORE UPDATE ON sync_infos
      FOR EACH ROW
      WHEN (NEW.new_shopify_order_id IS NULL)
      EXECUTE FUNCTION copy_shopify_order_id();
    SQL
  end

  def down
    # Drop the trigger and function
    execute <<-SQL.squish
      DROP TRIGGER IF EXISTS sync_infos_copy_shopify_order_id ON sync_infos;
      DROP FUNCTION IF EXISTS copy_shopify_order_id();
    SQL

    remove_column :sync_infos, :new_shopify_order_id
  end
end
