# frozen_string_literal: true

# migration class to add extra infos to sync infos
class AddExtraInfosToSyncInfos < ActiveRecord::Migration[7.0]
  def change
    # sync info table is used for storing infos about many different entities. extra_infos would help storing additional
    # entity-specific data
    add_column :sync_infos, :extra_infos, :text
    # adds index on shopify_order_id and target_type column
    add_index :sync_infos, %i[shopify_order_id target_type]
  end
end
