class CreateErrorLogs < ActiveRecord::Migration[5.2]
  def change
    create_table :error_types do |t|
      t.string :signal
      t.string :external_info_text_key
      t.string :helpscout_article_id

      t.timestamps
    end

    create_table :error_logs do |t|
      t.references :shop, foreign_key: true
      t.string :exception_type
      t.string :error_info_internal
      t.string :error_info_external
      t.string :shopify_id
      t.string :shopify_name
      t.references :error_type, foreign_key: true
      t.timestamps
    end
  end
end
