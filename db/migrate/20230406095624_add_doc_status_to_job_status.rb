class AddDocStatusToJobStatus < ActiveRecord::Migration[7.0]
  def change
    add_column :job_statuses, :invoices_queued, :integer, default: 0
    add_column :job_statuses, :invoices_processed, :integer, default: 0
    add_column :job_statuses, :invoices_skipped, :integer, default: 0
    add_column :job_statuses, :refunds_queued, :integer, default: 0
    add_column :job_statuses, :refunds_processed, :integer, default: 0
    add_column :job_statuses, :refunds_skipped, :integer, default: 0
    add_column :job_statuses, :transactions_queued, :integer, default: 0
    add_column :job_statuses, :transactions_processed, :integer, default: 0
    add_column :job_statuses, :transactions_skipped, :integer, default: 0
  end
end
