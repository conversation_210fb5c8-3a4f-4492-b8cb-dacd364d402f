# frozen_string_literal: true

# Billing plans
ShopifyBilling::BillingPlan.create(
  name: "Standard",
  short_name: "standard",
  price: 11.49,
  plan_type: "recurring",
  interval: "EVERY_30_DAYS",
  features: [],
  available_for_production_shop: true
)

ShopifyBilling::BillingPlan.create(
  name: "Import",
  short_name: "import",
  price: 99,
  plan_type: "one_time",
  features: [],
  available_for_development_shop: true,
  available_for_production_shop: true
)

ShopifyBilling::BillingPlan.create(
  name: "Developer Standard",
  short_name: "developer",
  price: 0,
  plan_type: "recurring",
  interval: "EVERY_30_DAYS",
  features: [],
  development_plan: true,
  available_for_development_shop: true,
  available_for_production_shop: false
)

ShopifyBilling::BillingPlan.create(
  name: "Premium",
  short_name: "premium",
  price: 22.99,
  plan_type: "recurring",
  interval: "EVERY_30_DAYS",
  features: %w[custom_layouts exclude_emails_by_tag exclude_orders_by_tag customer_ui],
  available_for_development_shop: true,
  available_for_production_shop: true
)

ShopifyBilling::BillingPlan.create(
  name: "Standard",
  short_name: "standard_annual",
  price: 120,
  plan_type: "recurring",
  interval: "ANNUAL",
  features: [],
  available_for_production_shop: true
)

ShopifyBilling::BillingPlan.create(
  name: "Developer Standard",
  short_name: "developer_annual",
  price: 0,
  plan_type: "recurring",
  interval: "ANNUAL",
  features: [],
  development_plan: true,
  available_for_development_shop: true,
  available_for_production_shop: false
)

ShopifyBilling::BillingPlan.create(
  name: "Premium",
  short_name: "premium_annual",
  price: 240,
  plan_type: "recurring",
  interval: "ANNUAL",
  features: %w[custom_layouts exclude_emails_by_tag exclude_orders_by_tag customer_ui],
  available_for_development_shop: true,
  available_for_production_shop: true
)

# Service Instance
ServiceInstance.create(
  name: 'LexOffice', css_class: 'lexoffice', logo_1: 'lexoffice.png', logo_2: nil,
  url: 'https://lexoffice-sandbox.grld.eu', portal_url: 'https://www.lexoffice.de/',
  website_url: 'https://www.lexoffice.de/', module: 'Lexoffice'
)
