WITH error_data AS (
    SELECT
        el.order_id,
        el.shop_id,
        ARRAY_AGG(el.error_info_external) AS error_messages,
        bool_or(el.error_info_external IS NOT NULL) AS has_error_message,
        ARRAY_AGG(el.id) AS error_ids,
        ARRAY_AGG(et.helpscout_article_id) AS error_helpscout_id
    FROM
        error_logs el
            LEFT JOIN error_types et on et.id = el.error_type_id
    GROUP BY
        el.order_id,
        el.shop_id
)
SELECT
    si.shopify_order_id,
    MAX(si.shopify_order_name) AS shopify_order_names,
    ARRAY_AGG(si.target_id) AS target_ids,
    ARRAY_AGG(si.target_type) AS target_types,
    ARRAY_AGG(si.extra_infos) AS extra_infos,
    ed.error_messages,
    ed.has_error_message,
    ed.error_ids,
    ed.error_helpscout_id,
    COALESCE(MAX(si.shopify_created_at), MAX(si.created_at)) AS shopify_created_at,
    MAX(si.shop_id) AS shop_id
FROM
    sync_infos si
        LEFT JOIN error_data ed ON si.shopify_order_id = ed.order_id AND si.shop_id = ed.shop_id
GROUP BY
    si.shopify_order_id,
    ed.error_messages,
    ed.has_error_message,
    ed.error_ids,
    ed.error_helpscout_id;
