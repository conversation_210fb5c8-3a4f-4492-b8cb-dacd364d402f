SELECT shopify_order_id,
       <PERSON><PERSON>(shopify_order_name) AS shopify_order_names,
       ARRAY_AGG(target_id) AS target_ids,
       ARRAY_AGG(target_type) AS target_types,
       ARRAY_AGG(extra_infos) AS extra_infos,
       ARRAY_AGG(error_logs.error_info_external) AS error_messages,
       bool_or(error_logs.error_info_external IS NOT NULL) AS has_error_message,
       ARRAY_AGG(error_logs.id) AS error_ids,
       ARRAY_AGG(et.helpscout_article_id) AS error_helpscout_id,
       MAX(shopify_created_at) AS shopify_created_at,
       MAX(sync_infos.shop_id) AS shop_id
FROM sync_infos
        LEFT OUTER JOIN (error_logs LEFT OUTER JOIN error_types et on et.id = error_logs.error_type_id) ON sync_infos.shopify_order_id = error_logs.order_id
    AND sync_infos.shop_id = error_logs.shop_id
GROUP BY shopify_order_id